package com.gy.show.runnner;

import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.dto.TaskProgressDTO;
import com.gy.show.enums.TaskStatusEnum;
import com.gy.show.enums.TaskTypeEnum;
import com.gy.show.service.RequirementTaskService;
import com.gy.show.util.ProgressCalculator;
import com.gy.show.ws.FullViewTsServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableScheduling
public class TaskMonitor implements ApplicationRunner {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private FullViewTsServer server;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("TaskMonitor 启动成功，开始监控任务进度");
    }

    /**
     * 定时推送任务进度到前端
     * 每5秒执行一次
     */
    @Scheduled(fixedRate = 1500)
    public void pushTaskProgress() {
        try {
            // 1. 先更新任务状态
            updateTaskStatuses();

            // 2. 获取当前时间在任务开始时间和结束时间之间的任务集合
            List<RequirementTaskDTO> executingTasks = requirementTaskService.queryCurrentExecutingTasks();

            if (executingTasks.isEmpty()) {
                log.debug("当前没有正在执行的任务");
                return;
            }

            log.info("当前正在执行的任务数量: {}", executingTasks.size());

            // 3. 根据当前时间来计算任务执行的百分比 (已在service层实现)
            // 4. 将任务集合转换为TaskProgressDTO并组装推送数据
            List<TaskProgressDTO> taskProgressList = convertToTaskProgressDTO(executingTasks);

            // 5. 通过websocket推送到前端 FULL_VIEW_TASK_PROGRESS
            server.sendTaskProgress(taskProgressList);

            // 打印任务进度信息
            for (TaskProgressDTO taskProgress : taskProgressList) {
                String progressStr = ProgressCalculator.formatPercentage(taskProgress.getProgress(), 2);
                log.debug("任务[{}] 进度: {}, 剩余时间: {}秒",
                        taskProgress.getTaskName(),
                        progressStr,
                        taskProgress.getRemainingSeconds());
            }

        } catch (Exception e) {
            log.error("推送任务进度时发生异常", e);
        }
    }

    /**
     * 更新任务状态
     * 1. 将待执行且已到开始时间的任务状态改为执行中
     * 2. 将执行中且已过结束时间的任务状态改为执行成功
     */
    private void updateTaskStatuses() {
        try {
            LocalDateTime now = LocalDateTime.now();

            // 查询所有需要状态更新的任务（待执行和执行中）
            List<RequirementTaskDTO> allTasks = requirementTaskService.queryTasksForStatusUpdate();

            if (allTasks.isEmpty()) {
                return;
            }

            // 分类处理任务
            List<String> tasksToStart = new ArrayList<>();    // 需要开始执行的任务
            List<String> tasksToComplete = new ArrayList<>(); // 需要标记完成的任务

            for (RequirementTaskDTO task : allTasks) {
                // 待执行的任务，如果当前时间已到开始时间，则改为执行中
                if (task.getStatus() == 0 && !now.isBefore(task.getStartTime())) {
                    tasksToStart.add(task.getId());
                }
                // 执行中的任务，如果当前时间已过结束时间，则改为执行成功
                else if (task.getStatus() == 1 && now.isAfter(task.getEndTime())) {
                    tasksToComplete.add(task.getId());
                }
            }

            // 批量更新状态
            if (!tasksToStart.isEmpty()) {
                requirementTaskService.batchUpdateTaskStatus(tasksToStart, 1); // 改为执行中
                log.info("自动启动任务数量: {}", tasksToStart.size());
            }

            if (!tasksToComplete.isEmpty()) {
                requirementTaskService.batchUpdateTaskStatus(tasksToComplete, 2); // 改为执行成功
                log.info("自动完成任务数量: {}", tasksToComplete.size());
            }

        } catch (Exception e) {
            log.error("更新任务状态时发生异常", e);
        }
    }

    /**
     * 将RequirementTaskDTO转换为TaskProgressDTO
     */
    private List<TaskProgressDTO> convertToTaskProgressDTO(List<RequirementTaskDTO> tasks) {
        return tasks.stream().map(task -> {
            TaskProgressDTO progressDTO = new TaskProgressDTO();

            // 基本信息
            progressDTO.setTaskId(task.getId());
            progressDTO.setTaskName(task.getTaskName());
            progressDTO.setRequirementId(task.getRequirementId());
            progressDTO.setTaskType(task.getTaskType());
            progressDTO.setImportance(task.getImportance());
            progressDTO.setStatus(task.getStatus());
            progressDTO.setTaskComment(task.getTaskComment());

            // 时间信息
            progressDTO.setStartTime(task.getStartTime());
            progressDTO.setEndTime(task.getEndTime());
            progressDTO.setCurrentTime(task.getCurrentTime());

            // 进度信息
            progressDTO.setProgress(task.getProcess());
            // 计算百分比并保留2位小数，例如：23.43%
            double percentage = ProgressCalculator.toPercentage(task.getProcess(), 2);
            progressDTO.setProgressPercentage(percentage);

            // 计算时间相关信息
            calculateTimeInfo(progressDTO);

            // 设置状态和类型名称
            setDisplayNames(progressDTO);

            return progressDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 计算时间相关信息
     */
    private void calculateTimeInfo(TaskProgressDTO progressDTO) {
        LocalDateTime startTime = progressDTO.getStartTime();
        LocalDateTime endTime = progressDTO.getEndTime();
        LocalDateTime currentTime = progressDTO.getCurrentTime();

        // 计算总时长
        long totalSeconds = Duration.between(startTime, endTime).getSeconds();
        progressDTO.setTotalSeconds(totalSeconds);

        // 计算已执行时间
        long elapsedSeconds = Duration.between(startTime, currentTime).getSeconds();
        progressDTO.setElapsedSeconds(Math.max(0, elapsedSeconds));

        // 计算剩余时间
        long remainingSeconds = Duration.between(currentTime, endTime).getSeconds();
        progressDTO.setRemainingSeconds(Math.max(0, remainingSeconds));
    }

    /**
     * 设置显示名称
     */
    private void setDisplayNames(TaskProgressDTO progressDTO) {
        // 设置状态名称
        TaskStatusEnum statusEnum = TaskStatusEnum.getEnumByCode(progressDTO.getStatus());
        if (statusEnum != null) {
            progressDTO.setStatusName(statusEnum.getMessage());
        }

        // 设置任务类型名称
        if (progressDTO.getTaskType() != null && !progressDTO.getTaskType().isEmpty()) {
            List<String> typeNames = progressDTO.getTaskType().stream()
                    .map(typeCode -> {
                        TaskTypeEnum typeEnum = TaskTypeEnum.getEnumByCode(typeCode);
                        return typeEnum != null ? typeEnum.getMessage() : "未知";
                    })
                    .collect(Collectors.toList());
            progressDTO.setTaskTypeName(String.join(",", typeNames));
        }
    }
}
