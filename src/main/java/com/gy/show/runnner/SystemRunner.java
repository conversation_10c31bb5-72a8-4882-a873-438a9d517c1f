package com.gy.show.runnner;

import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.gy.show.constants.CacheConstant.TELEMETRY_DISPATCHER;
import static com.gy.show.constants.Constants.stationList;

/**
 * 系统初始化相关
 */
@Slf4j
@Component
public class SystemRunner implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 初始化遥测数据转发
        initTelemetryDispatcher();

    }

    private void initTelemetryDispatcher() {
        for (String station : stationList) {
            String contr = RedisUtil.StringOps.get(TELEMETRY_DISPATCHER + station);

            // 如果为空则初始化一个
            if (StringUtils.isBlank(contr)) {
                RedisUtil.StringOps.set(TELEMETRY_DISPATCHER + station, ClientTypeEnum.PLANE.getMessage());
            }
        }
    }
}
