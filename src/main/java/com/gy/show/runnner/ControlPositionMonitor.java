package com.gy.show.runnner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import static com.gy.show.constants.CacheConstant.CONTROL_REAL_POSITION;

@Slf4j
@Component
@EnableScheduling
public class ControlPositionMonitor implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("ControlPositionMonitor 启动成功，开始监控任务进度");
    }

    /**
     * 操控端实时位置转发
     */
    @Scheduled(fixedRate = 200)
    public void pushTaskProgress() {
        // 从redis中获取位置
        String position = RedisUtil.StringOps.get(CONTROL_REAL_POSITION + "_ship");

        if (StringUtils.isNotBlank(position)) {
            JSONObject posJson = JSON.parseObject(position);

            String longitude = posJson.getString("longitude");
            String latitude = posJson.getString("latitude");

            log.info("获取到实时位置经度：{}，纬度：{}", longitude, latitude);

            // TODO 转发数据

        }

    }

}
