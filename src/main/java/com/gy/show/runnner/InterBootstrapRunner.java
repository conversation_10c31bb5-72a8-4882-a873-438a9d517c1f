package com.gy.show.runnner;

import com.gy.show.config.ExternalProperties;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.service.MissileService;
import com.gy.show.socket.client.*;
import com.gy.show.socket.server.UdpMulticastSender;
import com.gy.show.util.NetworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Order(2)
@Component
public class InterBootstrapRunner implements ApplicationRunner {

    @Autowired
    private ExternalProperties p;

    @Value("${local.ip:auto}")
    private String localIp;

    @Autowired
    private MissileService missileService;

    @Autowired
    private ThreadPoolTaskScheduler scheduler;

    private Map<String, StationTcpClient> tcpSenders = new HashMap<>();

    private Map<String, UdpMulticastSender> senders = new HashMap<>();

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 动态获取本机IP地址
        String actualLocalIp = getActualLocalIp();
        log.info("使用本机IP地址：{}", actualLocalIp);

        ExternalProperties.Controller controller = p.getController();

        ExternalProperties.Controller.Car car = controller.getCar();
        if (car.isOpen()) {
            for (int i = 0; i < car.getId().size(); i++) {
                // 无人车UDP接收端
                log.info("初始化无人车UDP接收端:{}，host：{}，port：{}", car.getId().get(i), car.getReceive().getHost().get(i), car.getReceive().getPort().get(i));
                AbstractNettyClient controlCarClient = new ControlUdpClient(car.getId().get(i));
                controlCarClient.connect(car.getReceive().getHost().get(i), car.getReceive().getPort().get(i), car.getTer().get(i));

                // 无人车UDP发送端
                log.info("初始化无人车UDP发送端：{}，host：{}，port：{}", car.getId().get(i), car.getSend().getHost().get(i), car.getSend().getPort().get(i));
                UdpMulticastSender controlCarSender = new UdpMulticastSender(car.getSend().getHost().get(i), car.getSend().getPort().get(i));
                controlCarSender.start();
                senders.put(ClientTypeEnum.CAR.getMessage() + car.getId().get(i), controlCarSender);
            }
        } else {
            log.info("无人车UDP未启用链接");
        }

        // 无人艇UDP接收端
        ExternalProperties.Controller.Ship ship = controller.getShip();

        if (ship.isOpen()) {
            for (int i = 0; i < ship.getId().size(); i++) {
                log.info("初始化无人艇UDP接收端，host：{}，port：{}", ship.getReceive().getHost().get(i), ship.getReceive().getPort().get(i));
                AbstractNettyClient controlShipClient = new ControlUdpClient(ship.getId().get(i));
                controlShipClient.connect(ship.getReceive().getHost().get(i), ship.getReceive().getPort().get(i), ship.getTer().get(i));
//        clients.put(ClientTypeEnum.CAR.getMessage(), controlCarClient);

                // 无人艇UDP发送端
                log.info("初始化无人艇UDP发送端，host：{}，port：{}", ship.getSend().getHost().get(i), ship.getSend().getPort().get(i));
                UdpMulticastSender controlShipSender = new UdpMulticastSender(ship.getSend().getHost().get(i), ship.getSend().getPort().get(i));
                controlShipSender.start();
                senders.put(ClientTypeEnum.SHIP.getMessage() + ship.getId().get(i), controlShipSender);
            }
        } else {
            log.info("无人艇UDP未启用链接");
        }

        // 无人机操控端
        ExternalProperties.Controller.Uav uav2 = controller.getUav();

        if (uav2.isOpen()) {
            log.info("初始化无人机操控端UDP接收，host：{}，port：{}", uav2.getReceive().getHost(), uav2.getReceive().getPort());
            AbstractNettyClient controlShipClient = new ControlUdpClient(uav2.getId());
            controlShipClient.connect(uav2.getReceive().getHost().get(0), uav2.getReceive().getPort().get(0), uav2.getTer().get(0));

            // 无人艇UDP发送端
            log.info("初始化无人机操控端UDP发送端，host：{}，port：{}", uav2.getSend().getHost(), uav2.getSend().getPort());
            UdpMulticastSender controlShipSender = new UdpMulticastSender(uav2.getSend().getHost().get(0), uav2.getSend().getPort().get(0));
            controlShipSender.start();
            senders.put(ClientTypeEnum.PLANE.getMessage() + uav2.getId(), controlShipSender);
        } else {
            log.info("无人机操控端UDP未启用链接");
        }

        // 终端连接
        ExternalProperties.Terminal terminal = p.getTerminal();

        if (terminal.isOpen()) {
            // 终端UDP接收端
            log.info("初始化终端UDP接收端，host：{}，port：{}", terminal.getReceive().getHost(), terminal.getReceive().getPort());
            AbstractNettyClient terminalClient = new TerminalUdpClient();
            terminalClient.connect(terminal.getReceive().getHost().get(0), terminal.getReceive().getPort().get(0), 0);
//        clients.put(ClientTypeEnum.TERMINAL.getMessage(), terminalClient);

            // 终端UDP发送端
            log.info("初始化终端UDP发送端，host：{}，port：{}", terminal.getSend().getHost(), terminal.getSend().getPort());
            UdpMulticastSender terminalSender = new UdpMulticastSender(terminal.getSend().getHost().get(0), terminal.getSend().getPort().get(0));
            terminalSender.start();
            senders.put(ClientTypeEnum.TERMINAL.getMessage(), terminalSender);
        } else {
            log.info("终端UDP未启用链接");
        }

        ExternalProperties.Station station = p.getStation();
        /** 航天测控站监控UDP start **/
        ExternalProperties.Station.Space1 space1 = station.getSpace1();

        if (space1.isOpen()) {
//            String[] ids = splitString(stationSpaceStatusSendId);

            // 航天测控站状态UDP接收端
//            String[] stationSpaceStatusReceiveHosts = splitString(stationSpaceStatusReceiveHost);
//            String[] stationSpaceStatusReceivePorts = splitString(stationSpaceStatusReceivePort);

            for (int i = 0; i < space1.getId().size(); i++) {
                log.info("初始化航天测控站:{} 状态UDP接收端，host：{}，port：{}", space1.getId().get(i),
                        space1.getReceive().getHost().get(i), space1.getReceive().getPort().get(i));

                StationStatusUdpClient stationStatusUdpClient = new StationStatusUdpClient(space1.getId().get(i));
                stationStatusUdpClient.joinMultiGroup(space1.getReceive().getHost().get(i), space1.getReceive().getPort().get(i), actualLocalIp);
//            clients.put(ClientTypeEnum.SPACE_STATUS.getMessage(), stationStatusUdpClient);
            }

            // 航天测控站状态UDP发送端
//            String[] stationSpaceStatusSendHosts = splitString(stationSpaceStatusSendHost);
//            String[] stationSpaceStatusSendPorts = splitString(stationSpaceStatusSendPort);

            for (int i = 0; i < space1.getId().size(); i++) {
                log.info("初始化航天测控站状态UDP发送端，host：{}，port：{}", space1.getSend().getHost().get(i), space1.getSend().getPort().get(i));
                UdpMulticastSender stationSpaceStatusSender = new UdpMulticastSender(space1.getSend().getHost().get(i), space1.getSend().getPort().get(i));
                stationSpaceStatusSender.start();
                senders.put(ClientTypeEnum.SPACE_STATUS.getMessage() + space1.getId().get(i), stationSpaceStatusSender);
            }

        } else {
            log.info("航天测控UDP未启用链接");
        }
        /** 航天测控站监控UDP end **/
//
//
        /** 航天测控站数据UDP start **/
        ExternalProperties.Station.Space space = station.getSpace();

        if (space.isOpen()) {
//            String[] stationSpaceIds = splitString(stationSpaceId);

            // 航天测控站UDP接收端
//            String[] stationSpaceReceiveHosts = splitString(stationSpaceReceiveHost);
//            String[] stationSpaceReceivePorts = splitString(stationSpaceReceivePort);

            for (int i = 0; i < space.getId().size(); i++) {
                log.info("初始化航天测控站UDP接收端，host：{}，port：{}", space.getReceive().getHost().get(i), space.getReceive().getPort().get(i));
                StationStatusUdpClient stationUdpClient = new StationUdpClient(space.getId().get(i));
                stationUdpClient.joinMultiGroup(space.getReceive().getHost().get(i), space.getReceive().getPort().get(i), actualLocalIp);
            }

            // 航天测控站TCP发送端
//            String[] stationSpaceSendHosts = splitString(stationSpaceSendHost);
//            String[] stationSpaceSendPorts = splitString(stationSpaceSendPort);

            for (int i = 0; i < space.getId().size(); i++) {
                log.info("初始化航天测控站TCP发送端，host：{}，port：{}", space.getSend().getHost().get(i), space.getSend().getPort().get(i));
                StationTcpClient stationSpaceSender = new StationTcpClient();
                stationSpaceSender.connect(space.getSend().getHost().get(i), space.getSend().getPort().get(i));

                tcpSenders.put(ClientTypeEnum.SPACE_STATUS.getMessage() + space.getId().get(i), stationSpaceSender);
            }

        } else {
            log.info("航天状态UDP未启用链接");
        }

        /** 航天测控站数据UDP end **/

        /** 导弹测控站数据UDP start **/
        ExternalProperties.Station.Missile missile = station.getMissile();

        if (missile.isOpen()) {
            for (int i = 0; i < missile.getId().size(); i++) {
                log.info("初始化导弹UDP接收端，host：{}，port：{}", missile.getReceive().getHost().get(i), missile.getReceive().getPort().get(i));
                AbstractNettyClient missileUdpClient = new MissileUdpClient(missile.getId().get(i));
                missileUdpClient.joinMultiGroup(missile.getReceive().getHost().get(i), missile.getReceive().getPort().get(i), actualLocalIp);

                // 导弹UDP发送端
                log.info("初始化导弹UDP发送端，host：{}，port：{}", missile.getSend().getHost().get(i), missile.getSend().getPort().get(i));
                UdpMulticastSender missileSender = new UdpMulticastSender( missile.getSend().getHost().get(i), missile.getSend().getPort().get(i));
                missileSender.start();
                senders.put(ClientTypeEnum.MISSILE.getMessage(), missileSender);
                sendHeartBeat();
            }
        } else {
            log.info("导弹UDP未启用链接");
        }

        /** 导弹测控站数据UDP end **/

        /** 无人机测控站数据UDP start **/

        ExternalProperties.Station.Uav uav = station.getUav();
        ExternalProperties.Station.Uav1 uav1 = station.getUav1();

        if (uav.isOpen()) {
            for (int i = 0; i < uav.getId().size(); i++) {

                log.info("初始化无人机UDP接收端，host：{}，port：{}", uav.getReceive().getHost().get(i), uav.getReceive().getPort().get(i));
                AbstractNettyClient uavUdpClient = new UavStationUdpClient(uav.getId().get(i));
                uavUdpClient.joinMultiGroup(uav.getReceive().getHost().get(i), uav.getReceive().getPort().get(i), actualLocalIp);

                // 无人机UDP发送端
                log.info("初始化无人机UDP发送端，host：{}，port：{}", uav.getSend().getHost().get(i), uav.getSend().getPort().get(i));
                UdpMulticastSender uavSender = new UdpMulticastSender(uav.getSend().getHost().get(i), uav.getSend().getPort().get(i));
                uavSender.start();
                senders.put(ClientTypeEnum.UAV.getMessage(), uavSender);

                // 无人机遥测数据
                log.info("初始化无人机遥测数据UDP接收端，host：{}，port：{}", uav1.getReceive().getHost().get(i), uav1.getReceive().getPort().get(i));
                AbstractNettyClient uav1UdpClient = new UavStationUdpClient(uav1.getId().get(i));
                uav1UdpClient.joinMultiGroup(uav1.getReceive().getHost().get(i), uav1.getReceive().getPort().get(i), actualLocalIp);
            }
        } else {
            log.info("无人机测控UDP未启用链接");
        }

        /** 无人机测控站数据UDP end **/
    }

    private void sendHeartBeat() {
        scheduler.scheduleAtFixedRate(() -> missileService.sendHeartBeat(), 1000);
    }

    private String[] splitString(String str) {
        return str.split(",");
    }

    /**
     * 获取实际的本机IP地址
     * 支持配置文件指定、自动获取、网段匹配等多种方式
     */
    private String getActualLocalIp() {
        // 如果配置了具体IP地址，直接使用
        if (localIp != null && !localIp.trim().isEmpty() &&
                !"auto".equalsIgnoreCase(localIp.trim()) &&
                !"localhost".equalsIgnoreCase(localIp.trim()) &&
                !"127.0.0.1".equals(localIp.trim())) {
            log.info("使用配置文件指定的IP地址：{}", localIp);
            return localIp.trim();
        }

        // 自动获取首选IP地址
        String preferredIp = NetworkUtils.getPreferredLocalIpAddress();
        if (preferredIp != null && !"127.0.0.1".equals(preferredIp)) {
            return preferredIp;
        }

        // 如果首选IP获取失败，尝试获取192.168网段的IP
        String networkIp = NetworkUtils.getIpByNetworkPrefix("192.168");
        if (networkIp != null) {
            return networkIp;
        }

        // 如果还是获取不到，尝试获取10.0网段的IP
        networkIp = NetworkUtils.getIpByNetworkPrefix("10.0");
        if (networkIp != null) {
            return networkIp;
        }

        // 最后尝试获取172.16-172.31网段的IP
        for (int i = 16; i <= 31; i++) {
            networkIp = NetworkUtils.getIpByNetworkPrefix("172." + i);
            if (networkIp != null) {
                return networkIp;
            }
        }

        // 如果所有方法都失败，返回默认值
        log.warn("无法获取合适的本机IP地址，使用默认值：127.0.0.1");
        return "127.0.0.1";
    }


    public Map<String, UdpMulticastSender> getSenders() {
        return senders;
    }

    public Map<String, StationTcpClient> getTcpSenders() {
        return tcpSenders;
    }
}
