package com.gy.show.runnner;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 */
@Slf4j
@Component
public class PerformanceMonitor {

    private final ConcurrentHashMap<String, AtomicLong> counters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> timers = new ConcurrentHashMap<>();

    /**
     * 开始计时
     */
    public long startTimer(String name) {
        long startTime = System.currentTimeMillis();
        timers.put(name + "_start", new AtomicLong(startTime));
        return startTime;
    }

    /**
     * 结束计时并记录
     */
    public long endTimer(String name) {
        long endTime = System.currentTimeMillis();
        AtomicLong startTimeAtomic = timers.get(name + "_start");
        if (startTimeAtomic != null) {
            long duration = endTime - startTimeAtomic.get();
            timers.put(name + "_duration", new AtomicLong(duration));
            log.info("性能监控 - {}: {}ms", name, duration);
            return duration;
        }
        return 0;
    }

    /**
     * 增加计数器
     */
    public void incrementCounter(String name) {
        counters.computeIfAbsent(name, k -> new AtomicLong(0)).incrementAndGet();
    }

    /**
     * 增加计数器指定值
     */
    public void addToCounter(String name, long value) {
        counters.computeIfAbsent(name, k -> new AtomicLong(0)).addAndGet(value);
    }

    /**
     * 获取计数器值
     */
    public long getCounter(String name) {
        AtomicLong counter = counters.get(name);
        return counter != null ? counter.get() : 0;
    }

    /**
     * 获取计时器值
     */
    public long getTimer(String name) {
        AtomicLong timer = timers.get(name + "_duration");
        return timer != null ? timer.get() : 0;
    }

    /**
     * 重置所有统计
     */
    public void reset() {
        counters.clear();
        timers.clear();
    }

    /**
     * 打印统计信息
     */
    public void printStats() {
        log.info("=== 性能统计 ===");
        counters.forEach((name, value) ->
                log.info("计数器 - {}: {}", name, value.get()));
        timers.entrySet().stream()
                .filter(entry -> entry.getKey().endsWith("_duration"))
                .forEach(entry -> {
                    String name = entry.getKey().replace("_duration", "");
                    log.info("计时器 - {}: {}ms", name, entry.getValue().get());
                });
        log.info("===============");
    }
}
