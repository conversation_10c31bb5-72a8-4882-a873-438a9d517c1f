//package com.gy.show.config;
//
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
//@Component
//public class RedisService {
//
//    @Resource
//    private RedisTemplate redisTemplate;
//
//
//    @Value("${ts.point.timeout}")
//    private Long timeout;
//
//    /**
//     *
//     * 自增
//     * @param key
//     * @return
//     */
//    public int increment(String key) {
//        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(key, redisTemplate.getConnectionFactory());
//        int andIncrement = redisAtomicInteger.getAndIncrement();
//        if ( andIncrement > 0) {
//            redisAtomicInteger.expire(timeout, TimeUnit.SECONDS);
//        }
//        return andIncrement;
//    }
//
//    /*----------------------------- HASH -----------------------------*/
//    public void hashPut(String key, String hashKey, Object value){
//        redisTemplate.opsForHash().put(key, hashKey, value);
//    }
//
//    public Map<String,String> hashFindAll(String key){
//        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
//        Map<String, String> map = new HashMap<>();
//        entries.forEach((k,v)->  map.put(k.toString(),v.toString()));
//        return map;
//    }
//
//    public Object hashGet(String key, String hashKey){
//        return redisTemplate.opsForHash().get(key, hashKey);
//    }
//
//    public void hashPutAllExpire(String key,Map<String,Object> map){
//        redisTemplate.opsForHash().putAll(key,map);
//        redisTemplate.expire(key,7, TimeUnit.DAYS);
//    }
//
//    public void hashPutAll(String key,Map<String,Object> map){
//        redisTemplate.opsForHash().putAll(key,map);
//    }
//
//
//
//    public void hashRemove(String key, String hashKey){
//        redisTemplate.opsForHash().delete(key, hashKey);
//    }
//
//
//    public Boolean remove(String key){
//        return redisTemplate.delete(key);
//    }
//
//    public boolean hasKey(String key){
//        return redisTemplate.hasKey(key);
//    }
//
//}
