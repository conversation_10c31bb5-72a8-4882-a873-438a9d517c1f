package com.gy.show.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
public class ExternalProperties {

    private Controller controller;
    private Terminal terminal;
    private Station station;

    // Getters and Setters
    public Controller getController() { return controller; }
    public void setController(Controller controller) { this.controller = controller; }

    public Terminal getTerminal() { return terminal; }
    public void setTerminal(Terminal terminal) { this.terminal = terminal; }

    public Station getStation() { return station; }
    public void setStation(Station station) { this.station = station; }

    public static class Controller {
        private Car car;
        private Ship ship;
        private Uav uav;

        // Getters and Setters
        public Car getCar() { return car; }
        public void setCar(Car car) { this.car = car; }

        public Ship getShip() { return ship; }
        public void setShip(Ship ship) { this.ship = ship; }

        public Uav getUav() {
            return uav;
        }

        public void setUav(Uav uav) {
            this.uav = uav;
        }

        public static class Car {
            private boolean open;
            private List<String> id;
            private List<Integer> ter;
            private EndpointConfig receive;
            private EndpointConfig send;

            public List<Integer> getTer() {
                return ter;
            }

            public void setTer(List<Integer> ter) {
                this.ter = ter;
            }

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public List<String> getId() { return id; }
            public void setId(List<String> id) { this.id = id; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }

        public static class Ship {
            private boolean open;
            private EndpointConfig receive;
            private EndpointConfig send;

            private List<String> id;

            private List<Integer> ter;

            public List<Integer> getTer() {
                return ter;
            }

            public void setTer(List<Integer> ter) {
                this.ter = ter;
            }

            public List<String> getId() {
                return id;
            }

            public void setId(List<String> id) {
                this.id = id;
            }

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }

        public static class Uav {
            private boolean open;
            private EndpointConfig receive;
            private EndpointConfig send;
            private String id;
            private List<Integer> ter;

            public List<Integer> getTer() {
                return ter;
            }

            public void setTer(List<Integer> ter) {
                this.ter = ter;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }
    }

    public static class Terminal {
        private boolean open;
        private EndpointConfig receive;
        private EndpointConfig send;

        // Getters and Setters
        public boolean isOpen() { return open; }
        public void setOpen(boolean open) { this.open = open; }

        public EndpointConfig getReceive() { return receive; }
        public void setReceive(EndpointConfig receive) { this.receive = receive; }

        public EndpointConfig getSend() { return send; }
        public void setSend(EndpointConfig send) { this.send = send; }
    }

    public static class Station {
        private Uav uav;
        private Uav1 uav1;
        private Missile missile;
        private Space space;
        private Space1 space1;

        // Getters and Setters
        public Uav getUav() { return uav; }
        public void setUav(Uav uav) { this.uav = uav; }

        public Uav1 getUav1() { return uav1; }
        public void setUav1(Uav1 uav1) { this.uav1 = uav1; }

        public Missile getMissile() { return missile; }
        public void setMissile(Missile missile) { this.missile = missile; }

        public Space getSpace() { return space; }
        public void setSpace(Space space) { this.space = space; }

        public Space1 getSpace1() { return space1; }
        public void setSpace1(Space1 space1) { this.space1 = space1; }

        public static class Uav {
            private boolean open;
            private List<String> id;
            private EndpointConfig receive;
            private EndpointConfig send;

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public List<String> getId() { return id; }
            public void setId(List<String> id) { this.id = id; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }

        public static class Uav1 {
            private boolean open;
            private List<String> id;
            private EndpointConfig receive;

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public List<String> getId() { return id; }
            public void setId(List<String> id) { this.id = id; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }
        }

        public static class Missile {
            private boolean open;
            private List<String> id;
            private EndpointConfig receive;
            private EndpointConfig send;

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public List<String> getId() { return id; }
            public void setId(List<String> id) { this.id = id; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }

        public static class Space {
            private boolean open;
            private List<String> id;
            private EndpointConfig receive;
            private EndpointConfig send;

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public List<String> getId() { return id; }
            public void setId(List<String> id) { this.id = id; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }

        public static class Space1 {
            private boolean open;
            private List<String> id;
            private EndpointConfig receive;
            private EndpointConfig send;

            // Getters and Setters
            public boolean isOpen() { return open; }
            public void setOpen(boolean open) { this.open = open; }

            public List<String> getId() { return id; }
            public void setId(List<String> id) { this.id = id; }

            public EndpointConfig getReceive() { return receive; }
            public void setReceive(EndpointConfig receive) { this.receive = receive; }

            public EndpointConfig getSend() { return send; }
            public void setSend(EndpointConfig send) { this.send = send; }
        }
    }

    public static class EndpointConfig {
        private List<String> host;
        private List<Integer> port;

        // Getters and Setters
        public List<String> getHost() { return host; }
        public void setHost(List<String> host) { this.host = host; }

        public List<Integer> getPort() { return port; }
        public void setPort(List<Integer> port) { this.port = port; }
    }
}
