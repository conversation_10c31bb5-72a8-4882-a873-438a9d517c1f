package com.gy.show.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.websocket.server.ServerEndpointConfig;


public class MySpringConfigurator extends ServerEndpointConfig.Configurator implements ApplicationContextAware {


    private static volatile BeanFactory factory;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        MySpringConfigurator.factory = applicationContext;
    }
}
