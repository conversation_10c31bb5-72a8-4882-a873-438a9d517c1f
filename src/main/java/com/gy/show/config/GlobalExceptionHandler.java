package com.gy.show.config;

import com.gy.show.common.Result;
import com.gy.show.common.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;

import javax.xml.bind.ValidationException;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

/**
 *  全局异常处理器
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler{

    private static final String ERROR_MESSAGE = "系统异常，请稍后再试";


    @ExceptionHandler(value = Exception.class)
    public Result internalErrorHandler(Exception e){
        Result r;
        if(e instanceof ServiceException){
            r=Result.error().message(e.getMessage());
        }else if(e instanceof NumberFormatException){
            r= Result.error().message(e.getMessage());
        }else if(e instanceof MethodArgumentNotValidException){
            r= Result.error().message(e.getMessage());
        }else if(e instanceof HttpMessageNotReadableException){
            r= Result.error().message(e.getMessage());
        }else if(e instanceof BindException){
            r= Result.error().message(e.getMessage());
        } else{
            r= Result.error().message(ERROR_MESSAGE);
        }
        log.error("系统发生异常", e);
     return r;
    }

    @ExceptionHandler(IOException.class)
    public Result ioErrorHandler(IOException e) {
        e.printStackTrace();
        return Result.error().message( e.getMessage());
    }

    @ExceptionHandler(ServiceException.class)
    public Result paramErrorHandler(ServiceException e) {
        e.printStackTrace();
        return Result.error().message(e.getMessage());
    }

    @ExceptionHandler(ClassCastException.class)
    public Result paramErrorHandler(ClassCastException e) {
        e.printStackTrace();
        return Result.error().message(e.getMessage());
    }

    /**
     * 方法校检异常
     * @param e
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result paramErrorHandler(MethodArgumentNotValidException e){
        BindingResult exceptions = e.getBindingResult();
        //判断异常中是否有错误信息,如果存在就使用异常信息，否则使用默认信息
        if(exceptions.hasErrors()){
            List<ObjectError> errors = exceptions.getAllErrors();
            if(!errors.isEmpty()){
                FieldError fieldError =(FieldError) errors.get(0);
                return Result.error().message(fieldError.getDefaultMessage());
            }
        }
        e.printStackTrace();
        return Result.error().message(e.getMessage());
    }
    
    private String getMessage(Exception e){
        StringWriter sw = new StringWriter();
       try(PrintWriter pw = new PrintWriter(sw)){
           e.printStackTrace();
           pw.flush();
           sw.flush();
       }
      return sw.toString();
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result paramErrorHandler(MissingServletRequestParameterException e) {
        e.printStackTrace();
        return Result.error().message(e.getParameterName()+"不能为空");
    }


    @ExceptionHandler(BindException.class)
    public Result handlerBindException(BindException e) {
        e.printStackTrace();
        return Result.error().message(e.getAllErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(MultipartException.class)
    public Result handlerMultipartException(MultipartException e) {
        e.printStackTrace();
        return Result.error().message("文件解析失败");
    }

    @ExceptionHandler(ValidationException.class)
    public Result handleValidationException(ValidationException e) {
        e.printStackTrace();
        return Result.error().message(e.getMessage());
    }
}
