//package com.gy.show.redis;
//
//
//import lombok.Data;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import redis.clients.jedis.JedisSentinelPool;
//
//import java.util.Arrays;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
///**
// * @ClassName: ims-backend
// * @Description: 配置redis相关信息
// * <AUTHOR>
// * @Date 2020/11/20 14:27
// */
//@Data
//@Configuration
//public class JRedisConfig {
//	/**
//	 * 节点组
//	 */
//	@Value("${redis.nodes}")
//	private String nodes;
//	/**
//	 * master
//	 */
//	@Value("${redis.master}")
//	private String master;
//    /**
//     * 密码
//     */
//    @Value("${redis.password}")
//    private String password;
//    /**
//     * 数据库索引
//     */
//    @Value("${redis.indexDb}")
//    private Integer indexDb;
//
//
//
//	/**
//	 * master
//	 */
//	@Value("${redis.ip}")
//	private String ip;
//	/**
//	 * master
//	 */
//	@Value("${redis.port}")
//	private String port;
//	/**
//	 * master
//	 */
//	@Value("${redis.user}")
//	private String user;
//
//
//	@Value("${redis.modle}")
//	private String modle;
//
//
//	/**
//	 * 初始化redis
//	 */
//	@Bean
//	public void initRedisSentinel()
//	{
//		//初始化redis
//		JedisUtil.init(this);
//	}
//
//    /**
//     * 初始化redisPool-Dataexch数据监控使用（消息发布订阅）
//     *
//     * @return
//     */
//    @Bean
//	public JedisSentinelPool initRedisPool(){
////       JedisPool jedisPool = new JedisPool(new JedisPoololConfig(),ip,port,10000,password);
//		if(modle.equals("SINGER")){
//			return null;
//		}else {
//			Set<String> hostSet = new HashSet<>();
//			List<String> nodeList = Arrays.asList(nodes.split(","));
//			hostSet.addAll(nodeList);
//
////		hostSet.add(ip);
//			JedisSentinelPool jedisSentinelPool = new JedisSentinelPool(master, hostSet, password);
//			return jedisSentinelPool;
//		}
//	}
//
//}
