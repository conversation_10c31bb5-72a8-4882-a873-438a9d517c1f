//package com.gy.radio.redis;
//
//
//import org.apache.commons.lang3.StringUtil;
//import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.connection.*;
//import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
//import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.serializer.RedisSerializer;
//import org.springframework.data.redis.serializer.SerializationException;
//import org.springframework.stereotype.Component;
//
//import java.io.*;
//import java.util.Arrays;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * redis配置类
// * 本配置类适配了redis单机版 集群版 哨兵模式
// * 通过配置 yml 文件实现自动切换
// */
//
//@Configuration
//@Component
//public class TRedisConfig {
//
//    //可配置多节点版本
//    @Value("${spring.redis.redis-model:}")
//    private String redisModel;
//
//    //单机节点配置
//    @Value("${spring.redis.host:}")
//    private String host;
//    @Value("${spring.redis.database:}")
//    private String database;
//    @Value("${spring.redis.password:}")
//    private String password;
//    @Value("${spring.redis.port:}")
//    private String port;
//
//    //哨兵节点
//    @Value("${spring.redis.sentinel.master:}")
//    private String master;
//    @Value("${spring.redis.sentinel.nodes:}")
//    private String sentinelNodes;
//
//    //集群节点配置
//    @Value("${spring.redis.cluster.max-redirects}")
//    private String maxRedirects;
//    @Value("${psring.redis.cluster.nodes:}")
//    private String clusterNodes;
//
//    //连接池信息配置
////    @Value("spring.redis.lettuce.pool.max-active")
////    private Integer maxActive;
//    @Value("${spring.redis.lettuce.pool.min-idle}")
//    private Integer minIdel;
//    @Value("${spring.redis.lettuce.pool.max-idle}")
//    private Integer maxIdle;
//    @Value("${spring.redis.lettuce.pool.max-wait}")
//    private Long maxWaitMillis;
//
//    //连接池配置
//    @Bean
//    public LettuceConnectionFactory lettuceConnectionFactory() {
//        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
//        poolConfig.setMaxIdle(maxIdle == null ? 8 : maxIdle);
//        poolConfig.setMinIdle(minIdel == null ? 1 : minIdel);
//        poolConfig.setMaxWaitMillis(maxWaitMillis == null ? 5000L : maxWaitMillis);
//        LettucePoolingClientConfiguration buildClient = LettucePoolingClientConfiguration
//                .builder()
//                .poolConfig(poolConfig)
//                .build();
//        //开始适配连接池 判断是否 单机 集群 哨兵
//        if (redisModel.equalsIgnoreCase("SINGLE")) {
//            //单机redis
//            RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
//            redisConfig.setHostName(host == null || "".equals(host) ? "127.0.0.1" : host);
//            redisConfig.setPort(Integer.valueOf(port == null || "".equals(port) ? "6379" : port));
//            redisConfig.setDatabase(Integer.valueOf(database == null || "".equals(database) ? "0" : database));
//            if (StringUtil.isNotEmpty(password)) {
//                redisConfig.setPassword(RedisPassword.of(password));
//            }
//            return new LettuceConnectionFactory(redisConfig, buildClient);
//
//        } else if (redisModel.equalsIgnoreCase("SENTINEL")) {
//            //哨兵 redis
//            RedisSentinelConfiguration redisConfig = new RedisSentinelConfiguration();
//            redisConfig.sentinel(host, Integer.valueOf(port));
//            redisConfig.setMaster(master);
//            redisConfig.setPassword(RedisPassword.of(password));
//            if (sentinelNodes != null) {
//                List<RedisNode> redisNodeList = Arrays.stream(sentinelNodes
//                        .split(","))
//                        .map(item -> {
//                            String[] arr = item.split(":");
//                            return new RedisNode(arr[0], Integer.parseInt(arr[1]));
//                        }).collect(Collectors.toList());
//                redisConfig.setSentinels(redisNodeList);
//            }
//            return new LettuceConnectionFactory(redisConfig,buildClient);
//        }else {
//            //redis 集群版
//            RedisClusterConfiguration redisConfig = new RedisClusterConfiguration();
//            Set<RedisNode> nodes = new HashSet<>();
//            List<String> list = Arrays.asList(clusterNodes.split(","));
//            for (String sen : list) {
//                String[] arr = sen.split(":");
//                nodes.add(new RedisNode(arr[0],Integer.parseInt(arr[1])));
//            }
//            redisConfig.setClusterNodes(nodes);
//            //跨集群执行命令时要遵循的最大重定向数量
//            redisConfig.setMaxRedirects(Integer.valueOf(maxRedirects));
//            redisConfig.setPassword(RedisPassword.of(password));
//            return  new LettuceConnectionFactory(redisConfig,buildClient);
//        }
//    }
//
//    @Bean
//    public RedisTemplate<String,Object> redisTemplate (LettuceConnectionFactory lettuceConnectionFactory) {
//        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
//        redisTemplate.setConnectionFactory(lettuceConnectionFactory);
//
//        //序列化类
//        MyRedisSerializer serializer = new MyRedisSerializer();
//        redisTemplate.setKeySerializer(serializer);
//        redisTemplate.setValueSerializer(serializer);
//        // value hash序列化
//        redisTemplate.setHashValueSerializer(serializer);
//
//        return redisTemplate;
//    }
//
//    //自定义redis序列化器
//    static class MyRedisSerializer implements RedisSerializer<Object> {
//
//        @Override
//        public byte[] serialize(Object o) throws SerializationException {
//            return serializeObj(o);
//        }
//
//        @Override
//        public Object deserialize(byte[] bytes) throws SerializationException {
//            return deserializeObj(bytes);
//        }
//
//        /**
//         * 序列化
//         * param object
//         * @return serializeObj
//         */
//
//        private static byte[] serializeObj (Object obj) {
//            ObjectOutputStream oos = null;
//            ByteArrayOutputStream baos = null;
//            try {
//
//                baos = new ByteArrayOutputStream();
//                oos = new ObjectOutputStream(baos);
//                oos.writeObject(obj);
//                return baos.toByteArray();
//            } catch (IOException e) {
//                throw new RuntimeException("序列化失败");
//            }
//        }
//
//        /**
//         * 反序列化
//         * param byte
//         * @return deserializeObj
//         */
//        private static Object deserializeObj (byte[] bytes) {
//            if (bytes==null) {
//                return null;
//            }
//            ByteArrayInputStream bais = null;
//            try {
//                bais = new ByteArrayInputStream(bytes);
//                ObjectInputStream stream = new ObjectInputStream(bais);
//                return stream.readObject();
//            } catch (Exception e) {
//               throw  new RuntimeException("反序列化失败");
//            }
//        }
//    }
//}
//
