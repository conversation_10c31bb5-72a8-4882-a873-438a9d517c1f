//package com.gy.show.redis;
//
//import org.springframework.stereotype.Repository;
//import redis.clients.jedis.Jedis;
//
//import java.util.*;
//
///**
// * redis通用
// */
//@Repository
//public class RedisDao {
//
//
//    /**
//     * 查询数组
//     *
//     * @param key 键值
//     * @return 查询结果
//     */
//    public List<String> queryList(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        List<String> list = jedis.lrange(key, 0, -1);
//        jedis.close();
//        return list;
//    }
//
//    /**
//     * 查询zset
//     *
//     * @param key 键值
//     * @return 查询结果
//     */
//    public Set<String> queryZset(String key, int start, int end) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Set<String> set = jedis.zrange(key, start, end);
//        jedis.close();
//        return set;
//    }
//
//    /**
//     * 统计zset
//     *
//     * @param key 键值
//     * @return 查询结果
//     */
//    public long countZset(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long total = jedis.zcard(key);
//        jedis.close();
//        return total;
//    }
//
//    /**
//     * 查询hash
//     *
//     * @param key 键值
//     * @return 查询结果
//     */
//    public Map<String, String> queryHash(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Map<String, String> map = jedis.hgetAll(key);
//        jedis.close();
//        return map;
//    }
//
//    /**
//     * 查询hash
//     *
//     * @param key   键值
//     * @param field 字段
//     * @return 查询结果
//     */
//    public String queryHash(String key, String field) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String map = jedis.hget(key, field);
//        jedis.close();
//        return map;
//    }
//
//    /**
//     * 查询字符串
//     *
//     * @param key 键值
//     * @return 查询结果
//     */
//    public String queryString(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String str = jedis.get(key);
//        jedis.close();
//        return str;
//    }
//
//    /**
//     * 新增数组
//     *
//     * @param key   键值
//     * @param value 插入的值
//     * @return 是否成功
//     */
//    public synchronized boolean addList(String key, String value) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long length = jedis.llen(key);
//        Long newLength = jedis.rpush(key, value);
//        jedis.close();
//        if (newLength > length) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 新增zset
//     *
//     * @param key   键值
//     * @param score 分值
//     * @param value 插入的值
//     * @return 是否成功
//     */
//    public synchronized boolean addZset(String key, double score, String value) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long length = jedis.zadd(key, score, value);
//        jedis.close();
//        if (length > 0) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 新增set
//     *
//     * @param key     键值
//     * @param members 插入的元素
//     * @return 是否成功
//     */
//    public synchronized Long addSet(String key, String... members) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long result = jedis.sadd(key, members);
//        jedis.close();
//        return result;
//    }
//
//    /**
//     * 查询set
//     *
//     * @param key 键值
//     * @return 查询结果
//     */
//    public Set<String> querySet(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Set<String> result = jedis.smembers(key);
//        jedis.close();
//        return result;
//    }
//
//    /**
//     * 删除
//     *
//     * @param key 键值
//     * @return 是否成功
//     */
//    public boolean del(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long result = jedis.del(key);
//        jedis.close();
//        if (result > 0) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 新增字符串
//     *
//     * @param key   键值
//     * @param value 插入的值
//     * @return 是否成功
//     */
//    public synchronized boolean addString(String key, String value) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String set = jedis.set(key, value);
//        jedis.close();
//        if (set.equals("OK")) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 新增字符串
//     *
//     * @param key    键值
//     * @param value  插入的值
//     * @param second 过期时间(s)
//     * @return 是否成功
//     */
//    public synchronized boolean addStringExpire(String key, String value, int second) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String set = jedis.set(key, value);
//        Long expire = jedis.expire(key, second);
//        jedis.close();
//        if (set.equals("OK") && (expire > 0)) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 设置过期时间
//     *
//     * @param key    键值
//     * @param second 过期时间(s)
//     * @return 是否成功
//     */
//    public synchronized boolean expire(String key, int second) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long expire = jedis.expire(key, second);
//        jedis.close();
//        if (expire > 0) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 新增hash
//     *
//     * @param key   键值
//     * @param field 字段
//     * @param value 值
//     * @return 是否成功
//     */
//    public synchronized boolean addHash(String key, String field, String value) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        jedis.hset(key, field, value);
//        jedis.close();
//        return true;
//    }
//
//    /**
//     * 新增hash并expire
//     *
//     * @param key    键值
//     * @param field  字段
//     * @param value  值
//     * @param second 过期时间(s)
//     * @return 是否成功
//     */
//    public synchronized boolean addHashAndExpire(String key, String field, String value, int second) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        jedis.hset(key, field, value);
//        jedis.expire(key, second);
//        jedis.close();
//        return true;
//    }
//
//    /**
//     * 修改数组
//     *
//     * @param key   键值
//     * @param value 修改的值
//     * @param index 下标
//     * @return 是否成功
//     */
//    public synchronized boolean updateList(String key, String value, int index) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String res = jedis.lset(key, index, value);
//        jedis.close();
//        if (res.equals("OK")) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 修改字符串
//     *
//     * @param key   键值
//     * @param value 修改的值
//     * @return 是否成功
//     */
//    public synchronized boolean updateString(String key, String value) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String set = jedis.set(key, value);
//        jedis.close();
//        if (set.equals("OK")) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 删除
//     *
//     * @param key   键值
//     * @param value 删除的值
//     * @return 是否成功
//     */
//    public synchronized boolean delete(String key, String value) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long res = jedis.lrem(key, 0, value);
//        jedis.close();
//        if (res.equals("OK")) {
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 删除
//     *
//     * @param key   键值
//     * @param field 删除的字段
//     * @return 是否成功
//     */
//    public synchronized boolean hdelete(String key, String field) {
//        Jedis jedis = JedisUtil.getJedisResouces();
////        jedis.hdel(key, field);
////        List<Satellite> objects = JSON.parseArray(jedis.hgetAll(key).get(field), Satellite.class);
////
////        List<Satellite> isData = new ArrayList<>();
////        List<Satellite> notData = new ArrayList<>();
////
////        Calendar calendar = Calendar.getInstance();
////        calendar.setTime(new Date());
////        calendar.add(Calendar.DATE, -5);
////
////
////        objects.forEach(k -> {
////            if (k.getDataTime().getTime() >= calendar.getTime().getTime()) {
////                isData.add(k);
////            } else {
////                notData.add(k);
////            }
////        });
////        if (isData.size() > 0 && notData.size() > 0) {
////            del(key);
////            addHash(key, field, JSON.toJSONString(isData));
////        }
//        jedis.close();
//        return true;
//    }
//
//    /**
//     * 返回key
//     *
//     * @param key 查询的key
//     * @return 返回对应的key
//     */
//    public Set<String> keys(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Set<String> keys = jedis.keys(key);
//        jedis.close();
//        return keys;
//    }
//
//    public String flushDB() {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String s = jedis.flushDB();
//        return s;
//    }
//
//    /**
//     * 在原有数据上+1
//     *
//     * @param key 键值
//     * @return 返回结果
//     */
//    public synchronized long query(String key) {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        Long sequence = jedis.incr(key);
//        jedis.close();
//        return sequence;
//    }
//
//    public String random() {
//        Jedis jedis = JedisUtil.getJedisResouces();
//        String randomKey = jedis.randomKey();
//        jedis.close();
//        return randomKey;
//    }
//}
