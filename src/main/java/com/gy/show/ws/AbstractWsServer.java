package com.gy.show.ws;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PreDestroy;
import javax.websocket.CloseReason;
import javax.websocket.Session;
import java.io.IOException;

import static javax.websocket.CloseReason.CloseCodes.CLOSED_ABNORMALLY;
import static javax.websocket.CloseReason.CloseCodes.SERVICE_RESTART;

public abstract class AbstractWsServer {

    protected final static Logger LOGGER = LoggerFactory.getLogger(AbstractWsServer.class);

    protected Session session;

    public void onClose() throws IOException {
        LOGGER.info("客户端 [{}] 断开", session.getId());
        close(CLOSED_ABNORMALLY, "服务异常关闭");
    }

    public void onMessage(String message) {
        LOGGER.debug("接收数据 - [{}]", message);
    }

    public void onError(Throwable e) throws IOException {
        LOGGER.error("连接异常: ", e);
        onClose();
    }

    @PreDestroy
    public void destroy() throws IOException {
        close(SERVICE_RESTART, "服务关闭");
    }

    protected void close(CloseReason.CloseCode code, String reason) throws IOException {
        if (session.isOpen())
            session.close(new CloseReason(code, reason));
    }

    public abstract void sendAll(String message);

}
