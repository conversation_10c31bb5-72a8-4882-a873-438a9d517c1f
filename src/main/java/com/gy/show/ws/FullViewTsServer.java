package com.gy.show.ws;

import com.alibaba.fastjson.JSON;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.enums.WebSocketTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static javax.websocket.CloseReason.CloseCodes.RESERVED;

@ServerEndpoint("/ws/panoramic/situation/{clientId}")
@Component
@Slf4j
public class FullViewTsServer extends AbstractWsServer {

    private static final Map<String, FullViewTsServer> CLIENT_MAP = new ConcurrentHashMap<>(64);

    private String clientId;

    private ExecutorService executor = Executors.newSingleThreadExecutor();

    @OnOpen
    public void onOpen(Session session, @PathParam(value = "clientId") String clientId) throws IOException {
        this.session = session;
        if (CLIENT_MAP.containsKey(clientId)) {
            close(RESERVED, "客户端重复连接");
        }
        CLIENT_MAP.put(clientId, this);
        this.clientId = clientId;
        log.info("客户端 [{}] 连接", session.getId());
    }

    @OnClose
    public void onClose() throws IOException {
        super.onClose();
        CLIENT_MAP.remove(clientId);
    }

    @OnError
    public void onError(Throwable e) throws IOException {
        super.onError(e);
    }

    @OnMessage
    public void OnMessage(String message, Session session) {
        this.session = session;
        log.info("收到来自客户端[{}]的消息，消息为:[{}]", clientId, message);
//        sendAll(message);
    }

    public void sendMsg(String clientId, String msg) {
        executor.submit(() -> {
            if (CLIENT_MAP.containsKey(clientId)) {
                try {
                    CLIENT_MAP.get(clientId).session.getBasicRemote().sendText(msg);
                } catch (IOException e) {
                    log.error("websocket发生异常", e);
                }
            } else {
                log.warn("不存在的 client id [{}]", clientId);
            }
        });
    }

    private void sendAllMessage(String message) {
        for (Map.Entry<String, FullViewTsServer> entry : CLIENT_MAP.entrySet()) {
            FullViewTsServer server = entry.getValue();
            server.sendMsg(entry.getKey(), message);
        }
    }

    @Override
    public void sendAll(String message) {
        log.info("给所有客户端发送消息,{}", message);
        sendAllMessage(message);
    }

    /**
     * 发送交互日志
     * @param message
     */
    public void sendInteractionLog(String message) {
        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(message);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_INTERACTION_LOG.getCode());

        sendAll(JSON.toJSONString(dto));
    }

    /**
     * 发送资源站点接收信息日志
     * @param message
     */
    public void sendStationLog(String message) {
        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(message);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_STATION_LOG.getCode());

        sendAll(JSON.toJSONString(dto));
    }

    /**
     * 发送态势测试时间
     * @param message
     */
    public void sendSituationTest(String message) {
        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(message);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_STATION_TEST.getCode());

        sendAll(JSON.toJSONString(dto));
    }

    /**
     * 发送任务进度数据
     * @param taskProgressData 任务进度数据
     */
    public void sendTaskProgress(Object taskProgressData) {
        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(taskProgressData);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_TASK_PROGRESS.getCode());

        sendAll(JSON.toJSONString(dto));
    }

}


