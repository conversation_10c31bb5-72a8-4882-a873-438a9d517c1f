package com.gy.show.ws;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static javax.websocket.CloseReason.CloseCodes.RESERVED;

@ServerEndpoint("/ws/global/{clientId}")
@Component
@Slf4j
public class GlobalServer extends AbstractWsServer {

    private static final Map<String, GlobalServer> CLIENT_MAP = new ConcurrentHashMap<>(64);

    private String clientId;

    private ExecutorService executor = Executors.newSingleThreadExecutor();

    @OnOpen
    public void onOpen(Session session, @PathParam(value = "clientId") String clientId) throws IOException {
        this.session = session;
        if (CLIENT_MAP.containsKey(clientId)) {
            close(RESERVED, "客户端重复连接");
        }
        CLIENT_MAP.put(clientId, this);
        this.clientId = clientId;
        log.info("客户端 [{}] 连接", session.getId());
    }

    @OnClose
    public void onClose() throws IOException {
        super.onClose();
        CLIENT_MAP.remove(clientId);
    }

    @OnError
    public void onError(Throwable e) throws IOException {
        super.onError(e);
    }

    public void sendMsg(String clientId, String msg) {
        executor.submit(() -> {
            if (CLIENT_MAP.containsKey(clientId)) {
                try {
                    CLIENT_MAP.get(clientId).session.getBasicRemote().sendText(msg);
                } catch (IOException e) {
                    log.error("websocket发生异常", e);
                }
            } else {
                log.warn("不存在的 client id [{}]", clientId);
            }
        });
    }

    private void sendAllMessage(String message) {
        for (Map.Entry<String, GlobalServer> entry : CLIENT_MAP.entrySet()) {
            GlobalServer server = entry.getValue();
            server.sendMsg(entry.getKey(), message);
        }
    }

    @Override
    public void sendAll(String message) {
        log.info("给所有客户端发送消息,{}", message);
        sendAllMessage(message);
    }

}


