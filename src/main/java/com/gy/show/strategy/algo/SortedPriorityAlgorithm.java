package com.gy.show.strategy.algo;

import cn.hutool.core.collection.CollUtil;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dos.SysDictionary;
import com.gy.show.entity.dto.InvokeAlgorithmDTO;
import com.gy.show.service.DataGeneralService;
import com.gy.show.service.RequirementTaskService;
import com.gy.show.service.SysDictionaryService;
import com.gy.show.service.TaskTargetRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SortedPriorityAlgorithm extends AbstractAlgorithmTemplate {

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private SysDictionaryService dictionaryService;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    private static LinkedList<Integer> defaultPriorityList = new LinkedList<>();

    @PostConstruct
    public void init() {
        // 1 固定站 2 移动站
        defaultPriorityList.add(1);
    }


    @Override
    protected Map<String, List<Map<String, Object>>> doInvokeSelfAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO, Map<String, List<Map<String, Object>>> filterResult) {
        // 获取用户制定优先级队列，若为空则按默认优先级进行排序
        LinkedList<Integer> priorityList = invokeAlgorithmDTO.getPriorityList();
        if (CollUtil.isEmpty(priorityList)) {
            priorityList = defaultPriorityList;
        }

        Map<String, List<SysDictionary>> dics = dictionaryService.list()
                .stream()
                .collect(Collectors.groupingBy(SysDictionary::getDictName));

        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        Map<String, List<Map<String, Object>>> equipments = invokeAlgorithmDTO.getCoverEquipment();

        Map<String, List<DataGeneral>> collect = dataGeneralService.list()
                .stream()
                .collect(Collectors.groupingBy(DataGeneral::getId));
        for (Map.Entry<String, List<Map<String, Object>>> entry : equipments.entrySet()) {
            List<Map<String, Object>> equipment = entry.getValue();

//            TaskTargetRelation taskTargetRelation = taskTargetRelationService.getById(entry.getKey());

            RequirementTask task = requirementTaskService.getById(entry.getKey());

            LinkedList<Integer> finalPriorityList = priorityList;
            List<Map<String, Object>> isCover = equipment.stream()
                    .filter(e -> e.get("isCover") != null && Integer.parseInt(e.get("isCover").toString()) == 1)
                    .sorted((e1, e2) -> {
                        List<DataGeneral> dataGenerals1 = collect.get(e1.get("generalId").toString());
                        List<DataGeneral> dataGenerals2 = collect.get(e2.get("generalId").toString());

                        DataGeneral general1 = dataGenerals1.get(0);
                        DataGeneral general2 = dataGenerals2.get(0);

                        log.info("当前优先级队列类型为：{}, 该设备类型为：{}, {}", finalPriorityList, general1.getTableComment(), general2.getTableComment());

                        List<SysDictionary> dictionaries1 = dics.get(general1.getTableComment());
                        List<SysDictionary> dictionaries2 = dics.get(general2.getTableComment());

                        Integer type1 = -1;
                        Integer type2 = -1;
                        if (CollUtil.isNotEmpty(dictionaries1)) {
                            type1 = dictionaries1.get(0).getDictValue();
                        }
                        if (CollUtil.isNotEmpty(dictionaries2)) {
                            type2 = dictionaries2.get(0).getDictValue();
                        }

                        // 根据type值来排序，在finalPriorityList中的优先
                        int index1 = finalPriorityList.indexOf(type1);
                        int index2 = finalPriorityList.indexOf(type2);

                        return Integer.compare(index1, index2);
                    })
                    .peek(e -> {
                        e.put("polygon", null);
                        e.put("task", task);
                        e.put("marked", 1);
                    })
                    .collect(Collectors.toList());

            result.put(task.getId(), isCover);
        }

        return result;
    }
}
