package com.gy.show.strategy.algo;

import cn.hutool.core.bean.BeanUtil;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.CoordinateWithTimeDTO;
import com.gy.show.entity.dto.InvokeAlgorithmDTO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.service.DataGeneralService;
import com.gy.show.service.RequirementTaskService;
import com.gy.show.service.ScheduleService;
import com.gy.show.util.GISUtils;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务操作模板类
 */
@Slf4j
public abstract class AbstractAlgorithmTemplate {

    @Resource
    private ScheduleService scheduleService;

    @Resource
    private RequirementTaskService requirementTaskService;

    @Resource
    private DataGeneralService dataGeneralService;

    /**
     * 调用基础调度算法
     * @param invokeAlgorithmDTO
     */
    public Map<String, List<Map<String, Object>>> invokeBasicAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO) {
        filterCoverSource(invokeAlgorithmDTO);

        // 数据去重以及数据加工
        Map<String, List<Map<String, Object>>> result = dataHandler(invokeAlgorithmDTO);

        return doInvokeSelfAlgorithm(invokeAlgorithmDTO, result);
    }

    /**
     * 按设备可执行的任务类型进行过滤
     * @param result
     */
    protected Map<String, List<Map<String, Object>>> filterTaskType(Map<String, List<Map<String, Object>>> result) {
        Map<String, List<Map<String, Object>>> filterResult = new HashMap<>(result.size());

        for (Map.Entry<String, List<Map<String, Object>>> entry : result.entrySet()) {
            List<Map<String, Object>> equipments = entry.getValue();

            RequirementTask task = (RequirementTask) equipments.get(0).get("task");
            // 目标任务类型
            Integer taskType = task.getTaskType();

            List<Map<String, Object>> targetEquipments = equipments.stream()
                    .filter(e -> {
                        String businessType = e.get("businessType").toString();
                        String[] bz = businessType.split(",");
                        List<String> bzList = Arrays.asList(bz);

                        return bzList.contains(taskType.toString());
                    })
                    .collect(Collectors.toList());

            filterResult.put(entry.getKey(), targetEquipments);
        }

        return filterResult;
    }

    /**
     * 数据去重以及数据加工
     * @param invokeAlgorithmDTO
     * @return
     */
    private Map<String, List<Map<String, Object>>> dataHandler(InvokeAlgorithmDTO invokeAlgorithmDTO) {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        Map<String, List<Map<String, Object>>> coverEquipment = invokeAlgorithmDTO.getCoverEquipment();

        for (Map.Entry<String, List<Map<String, Object>>> entry : coverEquipment.entrySet()) {
            String taskId = entry.getKey();

            RequirementTask task = requirementTaskService.getById(taskId);

            List<Map<String, Object>> es = entry.getValue();
            List<Map<String, Object>> isCover = es.stream()
                    .filter(e -> e.get("isCover") != null && Integer.parseInt(e.get("isCover").toString()) == 1)
                    .peek(e -> {
                        String generalId = e.get("generalId").toString();
                        DataGeneral dataGeneral = dataGeneralService.getById(generalId);
//                        e.put("polygon", null);
                        e.put("task", task);
                        e.put("dataType", dataGeneral.getDataType());

                        // 保存原始时间信息，用于后续恢复
                        if (e.get("entryTime") != null && e.get("exitTime") != null) {
                            e.put("originalEntryTime", e.get("entryTime").toString());
                            e.put("originalExitTime", e.get("exitTime").toString());
                        }
                    })
                    .collect(Collectors.toList());

            result.computeIfAbsent(task.getId(), k -> new ArrayList<>()).addAll(isCover);
        }

        return result;
    }

    protected void distinctResult(Map<String, List<Map<String, Object>>> result) {
        // 根据特定 key 的值去重
        String dupKey = "id";
        result.forEach((key, value) -> {
            Set<Object> seenValues = new HashSet<>();
            List<Map<String, Object>> uniqueList = new ArrayList<>();

            for (Map<String, Object> map : value) {
                Object keyValue = map.get(dupKey);
                if (keyValue != null && !seenValues.contains(keyValue)) {
                    seenValues.add(keyValue);
                    uniqueList.add(map);
                }
            }

            result.put(key, uniqueList);
        });
    }

    /**
     * 调用三方调度算法
     * @param invokeAlgorithmDTO
     */
    public void invokeThirdAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO) {
        // 过滤覆盖了航迹的资源设备
        filterCoverSource(invokeAlgorithmDTO);

        // 对过滤的设备进行优先级排序
//        doInvokeSelfAlgorithm(invokeAlgorithmDTO);
    }

    protected abstract Map<String, List<Map<String, Object>>> doInvokeSelfAlgorithm(InvokeAlgorithmDTO invokeAlgorithmDTO, Map<String, List<Map<String, Object>>> filterResult);

    /**
     * 需要先过滤出覆盖了航迹的资源设备
     */
    private void filterCoverSource(InvokeAlgorithmDTO invokeAlgorithmDTO) {
        // 天基、地基、空基的侦察方式不一样，且范围也不同，所以需要分开判断
        Map<String, CoordinateWithTimeDTO[]> coordinateMaps = scheduleService.buildCoordinateMaps(invokeAlgorithmDTO.getTracks(), invokeAlgorithmDTO.getTask());

        // 构建航迹
        Map<String, LineString> tracks = scheduleService.buildTrackMap0(coordinateMaps);
        invokeAlgorithmDTO.setLineStringMap(tracks);

        // 构建区域 key -> id, value -> 区域
        scheduleService.buildPolygon(invokeAlgorithmDTO.getEquipments());

        // 判断航迹是否被设备侦察覆盖
        Map<String, List<Map<String, Object>>> coverTrack = isCoverTrack(tracks, invokeAlgorithmDTO.getEquipments(), coordinateMaps, invokeAlgorithmDTO.getTask());
        invokeAlgorithmDTO.setCoverEquipment(coverTrack);
    }

    private Map<String, List<Map<String, Object>>> isCoverTrack(Map<String, LineString> tracks, Map<String, List<Map<String, Object>>> equipments, Map<String,
            CoordinateWithTimeDTO[]> coordinateMaps, RequirementTask task) {
        Map<String, Map<String, Double>> equipmentCoverages = new HashMap<>();
        Map<String, Map<String, Integer>> equipmentCounts = new HashMap<>();
        Map<String, List<Map<String, Object>>> coverEquipments = new HashMap<>();

        for (Map.Entry<String, LineString> lineEntry : tracks.entrySet()) {
            LineString lineString = lineEntry.getValue();
            String relationId = lineEntry.getKey();
//            RequirementTask task = scheduleService.queryTaskByRelationId(relationId);
            // 航迹转换的坐标点
            CoordinateWithTimeDTO[] coordinates = coordinateMaps.get(lineEntry.getKey());

            for (Map.Entry<String, List<Map<String, Object>>> entry : equipments.entrySet()) {
                List<Map<String, Object>> equipmentList = entry.getValue();

                for (Map<String, Object> equip : equipmentList) {
                    List<Map<String, Object>> tempList = new ArrayList<>();
//                    Polygon polygon;
//                    if (entry.getKey().equals(DataTypeEnum.SKY_PLATFORM.getCode().toString())) {
//                        polygon = scheduleService.getSatellitePolygon(relationId, equip, task);
//                    } else {
//                        polygon = (Polygon) equip.get("polygon");
//                    }
                    Polygon polygon = (Polygon) equip.get("polygon");
                    String equipmentId = (String) equip.get("id");
                    if (polygon == null) continue;

                    boolean trackInPolygon = GISUtils.isTrackInPolygon(lineString, polygon);
                    if (trackInPolygon) {
                        double coverRate = GISUtils.calculateCoverageRate(lineString, polygon);
                        equip.put("coverRate", coverRate);
                        log.info("设备ID:{},relationId:{},任务:{},覆盖率{}", equipmentId, relationId, task.getTaskName(), coverRate);

                        // Group by taskId instead of relationId
                        String taskId = task.getId(); // Assuming you have taskId in RequirementTask
                        equip.put("isCover", 1);


                        /** 计算进出时间start **/
                        Geometry intersection = lineString.intersection(polygon);

                        if (intersection instanceof LineString) {
                            LineString intersectionLine = (LineString) intersection;
                            Coordinate start = intersectionLine.getStartPoint().getCoordinate();
                            Coordinate end = intersectionLine.getEndPoint().getCoordinate();

                            // 计算最终的进出时间
                            Instant entryTime = GISUtils.interpolateTime(coordinates, start);
                            Instant exitTime = GISUtils.interpolateTime(coordinates, end);

                            equip.put("entryTime", LocalDateTime.ofInstant(entryTime, ZoneId.systemDefault()));
                            equip.put("exitTime", LocalDateTime.ofInstant(exitTime, ZoneId.systemDefault()));

                            log.info("Entry time: {}", entryTime);
                            log.info("Exit time:  {}", exitTime);
                        }
                        // 多条交互线的情况
                        if (intersection instanceof MultiLineString) {
                            MultiLineString multiLineString = (MultiLineString) intersection;
                            LocalDateTime earlyDate = null, laterDate = null;

                            List<LineString> lineStrings = new ArrayList<>();
                            for (int i = 0; i < multiLineString.getNumGeometries(); i++) {
                                LineString intersectionLine = (LineString) multiLineString.getGeometryN(i);

                                lineStrings.add(intersectionLine);
                            }
                            List<LineString> mergeLineStrings = scheduleService.mergeLineStrings(lineStrings);

                            for (int i = 0; i < mergeLineStrings.size(); i++) {
//                                LineString intersectionLine = (LineString) multiLineString.getGeometryN(i);
                                LineString intersectionLine = mergeLineStrings.get(i);

                                Coordinate start = intersectionLine.getStartPoint().getCoordinate();
                                Coordinate end = intersectionLine.getEndPoint().getCoordinate();

                                // 计算最终的进出时间
                                Instant entryTime = GISUtils.interpolateTime(coordinates, start);
                                Instant exitTime = GISUtils.interpolateTime(coordinates, end);

//                                LocalDateTime startTime = LocalDateTime.ofInstant(entryTime, ZoneId.systemDefault());
//                                if (earlyDate == null) {
//                                    earlyDate = startTime;
//                                } else {
//                                    earlyDate = earlyDate.isAfter(startTime) ? startTime : earlyDate;
//                                }
//
//                                LocalDateTime endTime = LocalDateTime.ofInstant(exitTime, ZoneId.systemDefault());
//                                if (laterDate == null) {
//                                    laterDate = endTime;
//                                } else {
//                                    laterDate = laterDate.isAfter(endTime) ? laterDate : endTime;
//                                }

                                if (equip.get("entryTime") != null) {
                                    // 拷贝当前设备
                                    Map<String, Object> newEquipment = new HashMap<>(equip.size());
                                    BeanUtil.copyProperties(equip, newEquipment);

                                    newEquipment.put("entryTime", LocalDateTime.ofInstant(entryTime, ZoneId.systemDefault()));
                                    newEquipment.put("exitTime", LocalDateTime.ofInstant(exitTime, ZoneId.systemDefault()));
//                                    newEquipment.put("randomKey", IdWorker.getIdStr());

                                    tempList.add(newEquipment);
                                } else {
                                    equip.put("entryTime", LocalDateTime.ofInstant(entryTime, ZoneId.systemDefault()));
                                    equip.put("exitTime", LocalDateTime.ofInstant(exitTime, ZoneId.systemDefault()));
                                }

//                                equip.put("entryTime", earlyDate);
//                                equip.put("exitTime", laterDate);

                                log.info("MultiLineString Entry time: {}", entryTime);
                                log.info("MultiLineString Exit time:  {}", exitTime);
                            }

                        }
                        /** 计算进出时间end **/

                        Map<String, Object> newEquipment = new HashMap<>();
                        BeanUtil.copyProperties(equip, newEquipment);
                        coverEquipments.computeIfAbsent(taskId, k -> new ArrayList<>()).add(newEquipment);
                        coverEquipments.computeIfAbsent(taskId, k -> new ArrayList<>()).addAll(tempList);
//
//                        equip.put(taskId + "_isCover", 1);
//                        equip.put(taskId + "_coverRate", coverRate);
//
//                        // Update equipment coverages and counts by taskId
//                        equipmentCoverages.computeIfAbsent(equipmentId, k -> new HashMap<>())
//                                .merge(taskId, coverRate, Double::sum);
//                        equipmentCounts.computeIfAbsent(equipmentId, k -> new HashMap<>())
//                                .merge(taskId, 1, Integer::sum);


                    } else {
//                        equip.put(task.getId() + "_isCover", 0);
                        equip.put("isCover", 0);
                    }
                }
            }
        }

        // Calculate average coverages for each equipment under each taskId
//        Map<String, Double> averageCoverages = new HashMap<>();
//        for (String equipmentId : equipmentCoverages.keySet()) {
//            for (String taskId : equipmentCoverages.get(equipmentId).keySet()) {
//                double totalCoverage = equipmentCoverages.get(equipmentId).get(taskId);
//                int count = equipmentCounts.get(equipmentId).get(taskId);
//                double averageCoverage = count > 0 ? totalCoverage / count : 0.0;
//                averageCoverages.put(equipmentId + "_" + taskId, averageCoverage);
//            }
//        }
//
//        // Set average coverage in coverEquipments map
//        for (Map.Entry<String, List<Map<String, Object>>> entry : coverEquipments.entrySet()) {
//            List<Map<String, Object>> equipmentList = entry.getValue();
//            String taskId = entry.getKey();
//            for (Map<String, Object> equip : equipmentList) {
//                String equipmentId = (String) equip.get("id");
//                Double averageCoverage = averageCoverages.get(equipmentId + "_" + taskId);
//                if (averageCoverage != null) {
//                    equip.put("averageCoverage", averageCoverage);
//                }
//            }
//        }

        return coverEquipments;
    }


//    private Map<String, List<Map<String, Object>>> isCoverTrack(Map<String, LineString> tracks, Map<String, List<Map<String, Object>>> equipments) {
//        // 存储每个设备的总覆盖率和计数
//        Map<String, Map<String, Double>> equipmentCoverages = new HashMap<>();
//        Map<String, Map<String, Integer>> equipmentCounts = new HashMap<>();
//
//        Map<String, List<Map<String, Object>>> coverEquipments = new HashMap<>();
//
//        for (Map.Entry<String, LineString> lineEntry : tracks.entrySet()) {
//            LineString lineString = lineEntry.getValue();
//            // relationId
//            String relationId = lineEntry.getKey();
//            RequirementTask task = scheduleService.queryTaskByRelationId(relationId);
//
//            // equipments这个Map的key是DataGeneral表中的dataType
//            for (Map.Entry<String, List<Map<String, Object>>> entry : equipments.entrySet()) {
//                List<Map<String, Object>> equipment = entry.getValue();
//                for (Map<String, Object> equip : equipment) {
//                    Polygon polygon;
//                    // 卫星需要单独计算,因为需要任务时间来计算卫星的当前位置
//                    if (entry.getKey().equals(DataTypeEnum.SKY_PLATFORM.getCode().toString())) {
//                        polygon = scheduleService.getSatellitePolygon(relationId, equip);
//                    } else {
//                        polygon = (Polygon) equip.get("polygon");
//                    }
//                    String equipmentId = (String) equip.get("id");
//                    if (polygon == null) continue;
//
//                    boolean trackInPolygon = GISUtils.isTrackInPolygon(lineString, polygon);
//                    if (trackInPolygon) {
//                        double coverRate = GISUtils.calculateCoverageRate(lineString, polygon);
//                        equip.put("coverRate", coverRate);
//                        log.info("设备ID:{},任务:{},覆盖率{}", equipmentId, task.getTaskName(), coverRate);
//                        Map<String, Object> newEquipment = new HashMap<>();
//                        BeanUtil.copyProperties(equip, newEquipment);
//
//                        coverEquipments.computeIfAbsent(relationId, k -> new ArrayList<>()).add(newEquipment);
//
//                        equip.put(relationId + "_isCover", 1);
//                        equip.put("isCover", 1);
//                        equip.put(relationId + "_coverRate", coverRate);
//
//                        // 更新设备的覆盖率和计数
//                        equipmentCoverages.computeIfAbsent(equipmentId, k -> new HashMap<>())
//                                .merge(relationId, coverRate, Double::sum);
//                        equipmentCounts.computeIfAbsent(equipmentId, k -> new HashMap<>())
//                                .merge(relationId, 1, Integer ::sum);
//
//                    } else {
//                        equip.put(relationId + "_isCover", 0);
//                    }
//                }
//            }
//        }
//
//        // 计算每个设备的平均覆盖率
//        Map<String, Object> averageCoverages = new HashMap<>();
//        for (String equipmentId : equipmentCoverages.keySet()) {
//            double totalCoverage = equipmentCoverages.get(equipmentId).values().stream().mapToDouble(Double::doubleValue).sum();
//            int count = equipmentCounts.get(equipmentId).values().stream().mapToInt(Integer::intValue).sum();
//            double averageCoverage = count > 0 ? totalCoverage / count : 0.0;
//            averageCoverages.put(equipmentId, averageCoverage);
//        }
//
//        for (Map.Entry<String, List<Map<String, Object>>> entry : coverEquipments.entrySet()) {
//            List<Map<String, Object>> equipment = entry.getValue();
//            for (Map<String, Object> equip : equipment) {
//                String id = equip.get("id").toString();
//                if (averageCoverages.get(id) != null) {
//                    equip.put("averageCoverage", averageCoverages.get(id));
//                }
//            }
//        }
//
//        return coverEquipments;
//    }

//    private void buildPolygon(Map<String, List<Map<String, Object>>> equipments) {
//
//        for (Map.Entry<String, List<Map<String, Object>>> entry : equipments.entrySet()) {
//            List<Map<String, Object>> eps = entry.getValue();
//            // 需要根据不同的设备类型来进行区域判断
//            EquipmentEnum type = EquipmentEnum.getEnumByCode(Integer.parseInt(entry.getKey()));
//            switch (type) {
//                case LAND_BASED:
//                    // 获取底面区域，只判断二维距离即可
//                    eps.forEach(map -> {
//                        double longitude = Double.parseDouble(map.get("longitude").toString());
//                        double latitude = Double.parseDouble(map.get("latitude").toString());
//                        double altitude = Double.parseDouble(map.get("altitude").toString());
//                        double radius = Double.parseDouble(map.get("radius").toString());
//
//                        Coordinate center = new Coordinate(longitude, latitude, altitude);
//                        Polygon polygon = GISUtils.createCircle(center, radius, geometryFactory);
//
//                        // 由于是动态查询,所有这里必须要将具体的参数返回,不然后面无法查询
//                        map.put("polygon", polygon);
//                    });
//                    break;
//                case SPACE_BASED:
////                    eps.forEach(map -> {
////                        double longitude = Double.parseDouble(map.get("longitude").toString());
////                        double latitude = Double.parseDouble(map.get("latitude").toString());
////                        double altitude = Double.parseDouble(map.get("altitude").toString());
////                        double radius = Double.parseDouble(map.get("bottom_radius").toString());
////
////                        Coordinate center = new Coordinate(longitude, latitude, altitude);
////                        Polygon polygon = GISUtils.createCircle(center, radius, geometryFactory);
////                        result.put(entry.getKey(), polygon);
////                    });
//                    break;
//                case VACANT_PART:
//                    break;
//                default:
//                    break;
//            }
//        }
//
//    }

//    private Map<String, LineString> buildTrackMap(Map<String, List<RequirementTargetTrack>> tracks) {
//        Map<String, LineString> result = new HashMap<>();
//        for (Map.Entry<String, List<RequirementTargetTrack>> entry : tracks.entrySet()) {
//            List<RequirementTargetTrack> trackList = entry.getValue();
//
//            // 转换
//            Coordinate[] coordinates = trackList.stream()
//                    .map(track -> new Coordinate(track.getLongitude().doubleValue(), track.getLatitude().doubleValue(), track.getAltitude().doubleValue()))
//                    .toArray(Coordinate[]::new);
//            // 创建
//            LineString line = geometryFactory.createLineString(coordinates);
//
//            result.put(entry.getKey(), line);
//        }
//
//        return result;
//    }

}
