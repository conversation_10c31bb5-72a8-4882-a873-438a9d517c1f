package com.gy.show.strategy.algo;

import com.gy.show.enums.AlgorithmContextEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class AlgorithmContext {

    @Resource(name = "sortedCoverRateAlgorithm")
    private AbstractAlgorithmTemplate sortedCoverRateAlgorithm;

    @Resource(name = "sortedPriorityAlgorithm")
    private AbstractAlgorithmTemplate sortedPriorityAlgorithm;

    @Resource(name = "coverCalculateAlgorithm")
    private AbstractAlgorithmTemplate coverCalculateAlgorithm;

    @Resource(name = "taskTimePriorityAlgorithm")
    private AbstractAlgorithmTemplate taskTimePriorityAlgorithm;


    public AbstractAlgorithmTemplate getInstance(AlgorithmContextEnum typeEnum) {
        AbstractAlgorithmTemplate instance = null;
        switch (typeEnum) {
            case SORTED_COVER:
                instance = sortedCoverRateAlgorithm;
                break;
            case SORTED_PRIORITY:
                instance = sortedPriorityAlgorithm;
                break;
            case THIRD_PART:
                break;
            case CAVER_CALCULATE:
                instance = coverCalculateAlgorithm;
                break;
            case CONTINUOUS_PRIORITY:
                instance = taskTimePriorityAlgorithm;
                break;
            default:
                log.error("invalid arguments");
                break;
        }
        return instance;
    }
}
