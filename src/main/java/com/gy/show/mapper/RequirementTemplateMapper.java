package com.gy.show.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gy.show.entity.dos.RequirementTemplate;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface RequirementTemplateMapper extends BaseMapper<RequirementTemplate> {
    int deleteByPrimaryKey(String id);

    int insert(RequirementTemplate record);

    int insertSelective(RequirementTemplate record);

    RequirementTemplate selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(RequirementTemplate record);

    int updateByPrimaryKeyWithBLOBs(RequirementTemplate record);

    int updateByPrimaryKey(RequirementTemplate record);
}