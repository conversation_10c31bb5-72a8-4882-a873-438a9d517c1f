package com.gy.show.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gy.show.entity.dos.DataGeneralAreaRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * DataGeneralAreaRelationMapper继承基类
 */
@Mapper
public interface DataGeneralAreaRelationMapper extends BaseMapper<DataGeneralAreaRelation> {

    @Select("SELECT dg.* FROM data_general_area_relation dg LEFT JOIN data_area da ON dg.area_id = da.id WHERE da.area_type = #{areaType}")
    List<DataGeneralAreaRelation> queryAreaRelationByType(@Param("areaType") Integer areaType);
}