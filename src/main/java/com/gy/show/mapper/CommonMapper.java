package com.gy.show.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface CommonMapper {

    /**
     * 执行
     *
     * @param sql sql
     */
    void exec(String sql);

    /**
     * 创建表
     *
     * @param tableName 表名
     * @return 表
     */
    Map<String, String> getCreateTableSql(String tableName);

    /**
     * 获取表头
     *
     * @param tableName 表名
     * @return 表头
     */
    List<Map<String, Object>> getCols(String tableName);

    /**
     * 保存数据
     *
     * @param tableName 表名
     * @param data      数据
     */
    void save(String tableName, Map<String, Object> data);

    /**
     * 获取数据
     *
     * @param tableName 表名
     * @param keyword   关键字
     * @param cols      搜索列
     * @param page      分页对象
     * @return 数据
     */
    IPage<Map<String, Object>> getList(IPage<Map<String, Object>> page, String tableName, String keyword, List<String> cols);


    /**
     * 获取数据
     *
     * @param tableName 表名
     * @param keyword   关键字
     * @param cols      搜索列
     * @param page      分页对象
     * @param args      参数(列名：值)
     * @return 数据
     */
    IPage<Map<String, Object>> getData(IPage<Map<String, Object>> page, String tableName, String keyword, List<String> cols, Map<String, Object> args);

    /**
     * 获取数据
     *
     * @param tableName 表名
     * @param colName   列名
     * @param colValue  值
     * @return 数据
     */
    List<Map<String, Object>> getListByCol(String tableName, String colName, String colValue);

    /**
     * 获取数据
     *
     * @param tableName 表名
     * @param colName   列名
     * @param colValues 值
     * @return 数据
     */
    List<Map<String, Object>> getListByColumn(String tableName, String colName, List<String> colValues);

    /**
     * 更新数据
     *
     * @param tableName      表名
     * @param data           数据
     * @param primaryKey     主键
     */
    void update(String tableName, Map<String, Object> data, Object primaryKey);

    /**
     * 删除信号
     *
     * @param tableName      表名
     * @param ids            ID 集合
     */
    void delete(String tableName, List<String> ids);

    /**
     * 根据表名,主键名,id获取单个设备详情
     *
     * @param tableName
     * @param dataId
     * @return
     */
    Map<String, Object> getOne(String tableName, @Param("dataId") String dataId);

    /**
     * 获取展示字段详情
     *
     * @return
     */
    Map<String, Object> getShowCols(String tableName, List<String> clos);

    /**
     * 根据表名获取所有数据总和
     *
     * @param tableName
     * @return
     */
    Integer getCount(String tableName, Map<String, Object> args);

    /**
     * 根据表名获取所有数据总和
     *
     * @param tableName 表名
     * @param args      参数(列名：值)
     * @return count
     */
    Integer getCountByField(String tableName, Map<String, Object> args, String sql);

    /**
     * 根据字段模糊查询数据
     *
     * @param page      分页对象
     * @param tableName 表名
     * @param fieldName 字段名
     * @param fieldValue 字段值（支持模糊查询）
     * @param args      其他精确匹配条件
     * @return 数据
     */
    IPage<Map<String, Object>> getDataByFieldLike(IPage<Map<String, Object>> page, String tableName, String fieldName, String fieldValue, Map<String, Object> args);
}
