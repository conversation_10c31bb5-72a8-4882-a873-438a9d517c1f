package com.gy.show.util;

public class TZYSUtils {

    public static String getTztz(String tzys){
// "调制方式 0，未知；1，AM；2，DSB；3，SSB；4，FM；5，PCM-FM；6，FM-FM:；" +
//                "7，PCM-FSK-PM；8，PCM-BPSK-PM；9，PCM-QPSK-PM；10，FSK；11，PCM-CDMA-BPSK；12，PCM-BPSK；" +
//                "13，PCM-QPSK；14，PCM-UQPSK；15，PCM-OQPSK ；16，PCM-FQPSK、；17，MSK；" +
//                "18，8PSK。19 ：16QAM；20 ：16APSK；21 ：32APSK；22 ：GMSK，23，8QAM";
        String tzysStr = null;
        switch (tzys){
            case "0":tzysStr = "未知";break;
            case "1":tzysStr = "AM";break;
            case "2":tzysStr = "DSB";break;
            case "3":tzysStr = "SSB";break;
            case "4":tzysStr = "FM";break;
            case "5":tzysStr = "PCM-FM";break;
            case "6":tzysStr = "FM-FM";break;
            case "7":tzysStr = "PCM-FSK-PM";break;
            case "8":tzysStr = "PCM-BPSK-PM";break;
            case "9":tzysStr = "PCM-QPSK-PM";break;
            case "10":tzysStr = "FSK";break;
            case "11":tzysStr = "PCM-CDMA-BPSK";break;
            case "12":tzysStr = "PCM-BPSK";break;
            case "13":tzysStr = "PCM-QPSK";break;
            case "14":tzysStr = "PCM-UQPSK";break;
            case "15":tzysStr = "PCM-OQPSK";break;
            case "16":tzysStr = "PCM-FQPSK";break;
            case "17":tzysStr = "MSK";break;
            case "18":tzysStr = "8PSK";break;
            case "19":tzysStr = "16QAM";break;
            case "20":tzysStr = "16APSK";break;
            case "21":tzysStr = "32APSK";break;
            case "22":tzysStr = "GMSK";break;
            case "23":tzysStr = "64QAM";break;
            case "24":tzysStr = "8QAM";break;
        }
        return tzysStr;
    }

    public static String getTzys(String tztz){
        String tztzStr = null;
        switch (tztz){
            case "0":tztzStr = "未知";break;
            case "1":tztzStr = "线性调频";break;
            case "2":tztzStr = "非线性调频";break;
            case "3":tztzStr = "相位编码";break;
        }
        return tztzStr;
    }
}
