package com.gy.show.util;

public class CoordinateConversion {
    public static void main(String[] args) {
        // 纬度、经度、高度
        double[] llaSys = {30.62341333, 103.5505125, 559.0};
        double[] llaTar = {30.62440218, 108.54882998, 592.38};

        // 计算高度角
        double ele = funcEle(llaSys, llaTar);
        System.out.println("Elevation: " + ele + " degrees");
    }

    // lla转ecef坐标
    public static double[] funcLla2Ecef(double[] lla) {
        double lat = lla[0];
        double lon = lla[1];
        double h = lla[2];

        // 常量
        double a = 6378137; // 地球半径（赤道）
        double f = 1 / 298.257223563; // 扁率
        double e2 = f * (2 - f); // 椭球率的平方

        // 计算N
        double sinLat = Math.sin(Math.toRadians(lat));
        double N = a / Math.sqrt(1 - e2 * sinLat * sinLat);

        // 计算ECEF坐标
        double[] ecef = new double[3];
        ecef[0] = (N + h) * Math.cos(Math.toRadians(lat)) * Math.cos(Math.toRadians(lon));
        ecef[1] = (N + h) * Math.cos(Math.toRadians(lat)) * Math.sin(Math.toRadians(lon));
        ecef[2] = (N * (1 - e2) + h) * Math.sin(Math.toRadians(lat));

        return ecef;
    }

    // 计算高度角
    public static double funcEle(double[] llaSys, double[] llaTar) {
        // 转换为ECEF坐标
        double[] ecefSys = funcLla2Ecef(llaSys);
        double[] ecefTar = funcLla2Ecef(llaTar);

        // 纬度和经度
        double lonSys = llaSys[1];
        double latSys = llaSys[0];

        // 将经度和纬度转换为弧度
        double lonSysRad = Math.toRadians(lonSys);
        double latSysRad = Math.toRadians(latSys);

        // 构建转换矩阵
        double[][] Gamma = {
                {-Math.sin(lonSysRad), Math.cos(lonSysRad), 0},
                {-Math.sin(latSysRad) * Math.cos(lonSysRad), -Math.sin(latSysRad) * Math.sin(lonSysRad), Math.cos(latSysRad)},
                {Math.cos(latSysRad) * Math.cos(lonSysRad), Math.cos(latSysRad) * Math.sin(lonSysRad), Math.sin(latSysRad)}
        };

        // 计算ecefTar - ecefSys
        double[] deltaEcef = {
                ecefTar[0] - ecefSys[0],
                ecefTar[1] - ecefSys[1],
                ecefTar[2] - ecefSys[2]
        };

        // 计算ENU坐标
        double[] enu = matrixVectorMultiply(Gamma, deltaEcef);

        // 将ENU转换为球坐标，并计算高度角
        double r = Math.sqrt(enu[0] * enu[0] + enu[1] * enu[1] + enu[2] * enu[2]);
        double ele = Math.asin(enu[2] / r); // 高度角（弧度）

        // 转换为角度
        return Math.toDegrees(ele);
    }

    // 矩阵与向量相乘
    public static double[] matrixVectorMultiply(double[][] matrix, double[] vector) {
        double[] result = new double[3];
        for (int i = 0; i < 3; i++) {
            result[i] = 0;
            for (int j = 0; j < 3; j++) {
                result[i] += matrix[i][j] * vector[j];
            }
        }
        return result;
    }
}
