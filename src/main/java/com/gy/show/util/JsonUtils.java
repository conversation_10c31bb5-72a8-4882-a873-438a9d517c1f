package com.gy.show.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @since 2021/7/5
 */
public class JsonUtils {

    public static <T> List<T> json2List(String jsonStr, Class<T> elementClasses) {
        if (null == jsonStr) return Collections.emptyList();
        ObjectMapper mapper = new ObjectMapper();
        JavaType javaType = mapper.getTypeFactory().constructParametricType(ArrayList.class, elementClasses);
        try {
            return mapper.readValue(jsonStr, javaType);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Map<String, Object> getInnerMap(String jsonStr) {
        return JSON.parseObject(jsonStr).getInnerMap();
    }


}
