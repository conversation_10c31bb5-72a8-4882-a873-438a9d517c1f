package com.gy.show.util;

import com.cetc10.spaceflight.orbitpre.orbit.LLAPredict;

import java.time.LocalDateTime;
import java.util.List;

public class SatelliteUtil {

    public static List<LLAPredict> calculateOrbit0(String filePath, LocalDateTime startTime, LocalDateTime endTime, double step) {
        List<String> lineStr = SatelliteOrbitCalculator.readTLEFile(filePath);
        startTime = startTime == null ? LocalDateTime.now() : startTime;
        endTime = endTime == null ? startTime.plusHours(2) : endTime;

        return GISUtils.getLlaPredicts(lineStr, startTime, endTime.plusSeconds(1), step);
    }
}
