//package com.gy.show.util;
//
//import com.gy.show.entity.dos.RequirementInfo;
//import com.gy.show.entity.dto.BatchRequirementCreateDTO;
//import com.gy.show.enums.ScheduleTypeEnum;
//import com.gy.show.service.BatchRequirementService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.Arrays;
//
///**
// * 批量目标创建演示类
// * 演示如何在一个需求中批量创建1000个目标
// */
//@Slf4j
//@Component
//public class BatchTargetCreationDemo {
//
//    @Autowired
//    private BatchRequirementService batchRequirementService;
//
//    /**
//     * 演示创建包含1000个目标的需求
//     */
//    public RequirementInfo demo1000Targets() {
//        log.info("开始演示：在一个需求中创建1000个目标");
//
//        // 1. 创建配置
//        BatchRequirementCreateDTO config = new BatchRequirementCreateDTO();
//        config.setRequirementName("演示需求-包含1000个目标");
//        config.setRequirementType(ScheduleTypeEnum.YGH.getCode()); // 预规划任务
//        config.setImportance(1); // 一般重要程度
//        config.setStartTime(LocalDateTime.now().plusHours(1)); // 1小时后开始
//        config.setEndTime(LocalDateTime.now().plusDays(1)); // 1天后结束
//        config.setRequirementComment("演示在一个需求中批量创建1000个目标的功能");
//        config.setTargetCount(1000); // 创建1000个目标
//
//        // 2. 设置任务配置
//        BatchRequirementCreateDTO.TaskConfig taskConfig = new BatchRequirementCreateDTO.TaskConfig();
//        taskConfig.setTaskTypes(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量（会被合并为测控任务）
//        taskConfig.setTaskStartOffset(0); // 任务与需求同时开始
//        taskConfig.setTaskDuration(120); // 任务持续2小时
//        taskConfig.setRepeatType(0); // 仅一次
//        config.setTaskConfig(taskConfig);
//
//        // 3. 设置航迹配置
//        BatchRequirementCreateDTO.TrackConfig trackConfig = new BatchRequirementCreateDTO.TrackConfig();
//        trackConfig.setTrackStartOffset(0); // 航迹与需求同时开始
//        trackConfig.setUseExistingTrack(true); // 使用现有航迹
//        config.setTrackConfig(trackConfig);
//
//        try {
//            // 4. 执行批量创建
//            RequirementInfo requirement = batchRequirementService.batchCreateTargets(config);
//
//            log.info("演示完成！成功创建需求：{}", requirement.getRequirementName());
//            log.info("需求ID：{}", requirement.getId());
//            log.info("包含目标数量：{}", config.getTargetCount());
//
//            return requirement;
//
//        } catch (Exception e) {
//            log.error("演示失败", e);
//            throw new RuntimeException("批量创建目标演示失败", e);
//        }
//    }
//
//    /**
//     * 演示创建包含测控任务的目标
//     */
//    public RequirementInfo demoControlTargets() {
//        log.info("开始演示：创建包含测控任务的1000个目标");
//
//        BatchRequirementCreateDTO config = new BatchRequirementCreateDTO();
//        config.setRequirementName("测控任务需求-包含1000个目标");
//        config.setRequirementType(ScheduleTypeEnum.YGH.getCode());
//        config.setImportance(2); // 重要
//        config.setStartTime(LocalDateTime.now().plusHours(2));
//        config.setEndTime(LocalDateTime.now().plusDays(2));
//        config.setRequirementComment("演示创建包含测控任务的1000个目标");
//        config.setTargetCount(1000);
//
//        // 测控任务配置
//        BatchRequirementCreateDTO.TaskConfig taskConfig = new BatchRequirementCreateDTO.TaskConfig();
//        taskConfig.setTaskTypes(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量
//        taskConfig.setTaskStartOffset(30); // 任务延迟30分钟开始
//        taskConfig.setTaskDuration(180); // 任务持续3小时
//        taskConfig.setRepeatType(1); // 每日重复
//        config.setTaskConfig(taskConfig);
//
//        BatchRequirementCreateDTO.TrackConfig trackConfig = new BatchRequirementCreateDTO.TrackConfig();
//        trackConfig.setTrackStartOffset(0);
//        trackConfig.setUseExistingTrack(true);
//        config.setTrackConfig(trackConfig);
//
//        try {
//            RequirementInfo requirement = batchRequirementService.batchCreateTargets(config);
//            log.info("测控任务演示完成！需求：{}", requirement.getRequirementName());
//            return requirement;
//        } catch (Exception e) {
//            log.error("测控任务演示失败", e);
//            throw new RuntimeException("测控任务演示失败", e);
//        }
//    }
//
//    /**
//     * 演示创建包含数传任务的目标
//     */
//    public RequirementInfo demoDataTransmissionTargets() {
//        log.info("开始演示：创建包含数传任务的1000个目标");
//
//        BatchRequirementCreateDTO config = new BatchRequirementCreateDTO();
//        config.setRequirementName("数传任务需求-包含1000个目标");
//        config.setRequirementType(ScheduleTypeEnum.YGH.getCode());
//        config.setImportance(1);
//        config.setStartTime(LocalDateTime.now().plusHours(3));
//        config.setEndTime(LocalDateTime.now().plusDays(3));
//        config.setRequirementComment("演示创建包含数传任务的1000个目标");
//        config.setTargetCount(1000);
//
//        // 数传任务配置
//        BatchRequirementCreateDTO.TaskConfig taskConfig = new BatchRequirementCreateDTO.TaskConfig();
//        taskConfig.setTaskTypes(Arrays.asList(4)); // 数传
//        taskConfig.setTaskStartOffset(60); // 任务延迟1小时开始
//        taskConfig.setTaskDuration(90); // 任务持续1.5小时
//        taskConfig.setRepeatType(0); // 仅一次
//        config.setTaskConfig(taskConfig);
//
//        BatchRequirementCreateDTO.TrackConfig trackConfig = new BatchRequirementCreateDTO.TrackConfig();
//        trackConfig.setTrackStartOffset(0);
//        trackConfig.setUseExistingTrack(true);
//        config.setTrackConfig(trackConfig);
//
//        try {
//            RequirementInfo requirement = batchRequirementService.batchCreateTargets(config);
//            log.info("数传任务演示完成！需求：{}", requirement.getRequirementName());
//            return requirement;
//        } catch (Exception e) {
//            log.error("数传任务演示失败", e);
//            throw new RuntimeException("数传任务演示失败", e);
//        }
//    }
//
//    /**
//     * 运行所有演示
//     */
//    public void runAllDemos() {
//        log.info("开始运行所有批量目标创建演示");
//
//        try {
//            // 演示1：基础1000个目标
//            RequirementInfo req1 = demo1000Targets();
//            log.info("演示1完成，需求ID：{}", req1.getId());
//
//            // 演示2：测控任务目标
//            RequirementInfo req2 = demoControlTargets();
//            log.info("演示2完成，需求ID：{}", req2.getId());
//
//            // 演示3：数传任务目标
//            RequirementInfo req3 = demoDataTransmissionTargets();
//            log.info("演示3完成，需求ID：{}", req3.getId());
//
//            log.info("所有演示完成！共创建了3个需求，每个需求包含1000个目标");
//
//        } catch (Exception e) {
//            log.error("演示过程中出现错误", e);
//            throw e;
//        }
//    }
//}
