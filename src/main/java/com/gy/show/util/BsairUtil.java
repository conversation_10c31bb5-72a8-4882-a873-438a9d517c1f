package com.gy.show.util;

public class BsairUtil {

    /**
     * @param poss      贝塞尔曲线控制点坐标
     * @param precision 精度，需要计算的该条贝塞尔曲线上的点的数目
     * @return 该条贝塞尔曲线上的点（二维坐标）
     */
    public static double[][] calculate(double[][] poss, int precision) {
        //维度，坐标轴数（二维坐标，三维坐标...）
        int dimersion = poss[0].length;
        //贝塞尔曲线控制点数（阶数）
        int number = poss.length;
        //控制点数不小于 2 ，至少为二维坐标系
        if ((number < 2) || (dimersion < 2)) {
            return null;
        }
        double[][] result = new double[precision][dimersion];
        //计算杨辉三角
        int[] mi = new int[number];
        mi[0] = mi[1] = 1;
        for (int i = 3; i <= number; i++) {

            int[] t = new int[i - 1];
            for (int j = 0; j < t.length; j++) {
                t[j] = mi[j];
            }
            mi[0] = mi[i - 1] = 1;
            for (int j = 0; j < i - 2; j++) {
                mi[j + 1] = t[j] + t[j + 1];
            }
        }
        //计算坐标点
        for (int i = 0; i < precision; i++) {
            float t = (float) i / precision;
            for (int j = 0; j < dimersion; j++) {
                float temp = 0.0f;
                for (int k = 0; k < number; k++) {
                    temp += Math.pow(1 - t, number - k - 1) * poss[k][j] * Math.pow(t, k) * mi[k];
                }
                result[i][j] = temp;
            }
        }
        return result;
    }

    /**
     * 获取角度
     *
     * @param longitude1
     * @param latitude1
     * @param longitude2
     * @param latitude2
     * @return double
     */
    public static double getAngle(double longitude1, double latitude1, double longitude2, double latitude2) {
        double x = Math.cos(latitude1) * Math.sin(latitude2) -
                Math.sin(latitude1) * Math.cos(latitude2) * Math.cos(longitude2 - longitude1);
        double y = Math.sin(longitude2 - longitude1) * Math.cos(latitude2);
        double angle = Math.atan2(y, x);
        angle = Math.toDegrees(angle);
        if (angle < 0) {
            angle += 360;
        }
        return angle;
    }

    public static double getPointAngle(double lon1, double lat1, double lon2, double lat2) {
        double d = StrictMath.toDegrees(StrictMath.atan2(lat2 - lat1, lon2 - lon1));
        d = d < 0 ? d + 360 : d;
        if ((lon2 >= lon1) && (lat2 >= lat1)) {
            d = 90 - d;
        } else {
            d = 450 - d;
        }
        return d;
    }

    public static void main(String[] args) {
        double lon1 = 93.6957d;
        double lat1 = 37.36196;
        double lon2 = 98.26601;
        double lat2 = 40.08657;
        System.out.println(getPointAngle(lon1, lat1, lon2, lat2));
    }

    public static double getAngle2(double lng1, double lat1, double lng2, double lat2) {
        double dRotateAngle = Math.atan2(Math.abs(lng1 - lng2), Math.abs(lat1 - lat2));
        if (lng2 >= lng1) {
            if (lat2 >= lat1) {
            } else {
                dRotateAngle = Math.PI - dRotateAngle;
            }
        } else {
            if (lat2 >= lat1) {
                dRotateAngle = 2 * Math.PI - dRotateAngle;
            } else {
                dRotateAngle = Math.PI + dRotateAngle;
            }
        }
        dRotateAngle = dRotateAngle * 180 / Math.PI;
        return dRotateAngle;
    }

    /**
     * 获取贝塞尔曲线插值点
     *
     * @param points 关键点坐标集合
     * @param scale  控制点收缩系数
     * @param step   步长,决定曲线疏密度
     * @return
     */
//    public static List<PointVo> getBeisaierPoints(List<PointVo> points, float scale, float step) {
//        List<PointVo> result = new ArrayList<>();
//        int originCount = points.size();
//        List<PointVo> midpoints = initSizeList(originCount);
//        //生成中点
//        for (int i = 0; i < originCount; i++) {
//            int nexti = (i + 1) % originCount;
//            PointVo midPoint = new PointVo();
//            midPoint.setX((points.get(i).getX() + points.get(nexti).getX()) / 2);
//            midPoint.setY((points.get(i).getY() + points.get(nexti).getY()) / 2);
//        }
//        //平移中点
//        List<PointVo> extrapoints = initSizeList(2 * originCount);
//        for (int i = 0; i < originCount; i++) {
//            int backi = (i + originCount - 1) % originCount;
//            PointVo midinmid = new PointVo();
//            midinmid.setX((midpoints.get(i).getX() + midpoints.get(backi).getX()) / 2);
//            midinmid.setY((midpoints.get(i).getY() + midpoints.get(backi).getY()) / 2);
//            double offsetx = points.get(i).getX() - midinmid.getX();
//            double offsety = points.get(i).getY() - midinmid.getY();
//            int extraindex = 2 * i;
//            extrapoints.get(extraindex).setX(midpoints.get(backi).getX() + offsetx);
//            extrapoints.get(extraindex).setY(midpoints.get(backi).getY() + offsety);
//            //朝 originPoint[i]方向收缩
//            double addx = (extrapoints.get(extraindex).getX() - points.get(i).getX()) * scale;
//            double addy = (extrapoints.get(extraindex).getY() - points.get(i).getY()) * scale;
//            extrapoints.get(extraindex).setX(points.get(i).getX() + addx);
//            extrapoints.get(extraindex).setY(points.get(i).getY() + addy);
//            int extranexti = (extraindex + 1) % (2 * originCount);
//            extrapoints.get(extranexti).setX(midpoints.get(i).getX() + offsetx);
//            extrapoints.get(extranexti).setY(midpoints.get(i).getY() + offsety);
//            //朝 originPoint[i]方向收缩
//            addx = (extrapoints.get(extranexti).getX() - points.get(i).getX()) * scale;
//            addy = (extrapoints.get(extranexti).getY() - points.get(i).getY()) * scale;
//            extrapoints.get(extranexti).setX(points.get(i).getX() + addx);
//            extrapoints.get(extranexti).setY(points.get(i).getY() + addy);
//        }
//        List<PointVo> controlPoint = initSizeList(4);
//        //生成4控制点，产生贝塞尔曲线
//        for (int i = 0; i < originCount; i++) {
//            controlPoint.get(0).setX(points.get(i).getX());
//            controlPoint.get(0).setY(points.get(i).getY());
//            int extraindex = 2 * i;
//            controlPoint.get(1).setX(extrapoints.get(extraindex + 1).getX());
//            controlPoint.get(1).setY(extrapoints.get(extraindex + 1).getY());
//            int extranexti = (extraindex + 2) % (2 * originCount);
//            controlPoint.get(2).setX(extrapoints.get(extranexti).getX());
//            controlPoint.get(2).setY(extrapoints.get(extranexti).getY());
//            int nexti = (i + 1) % originCount;
//            controlPoint.get(3).setX(points.get(nexti).getX());
//            controlPoint.get(3).setY(points.get(nexti).getY());
//            float u = 1;
//            while (u >= 0) {
//                double px = bezier3funcX(u, controlPoint);
//                double py = bezier3funcY(u, controlPoint);
//                //u的步长决定曲线的疏密
//                u -= step;
//                PointVo point = new PointVo(px, py, null, null, null,null);
//                //存入曲线点
//                result.add(point);
//            }
//        }
//        return result;
//    }
//
//    private static double bezier3funcX(float uu, List<PointVo> controlPoint) {
//        double part0 = controlPoint.get(0).getX() * uu * uu * uu;
//        double part1 = 3 * controlPoint.get(1).getX() * uu * uu * (1 - uu);
//        double part2 = 3 * controlPoint.get(2).getX() * uu * (1 - uu) * (1 - uu);
//        double part3 = controlPoint.get(3).getX() * (1 - uu) * (1 - uu) * (1 - uu);
//        return part0 + part1 + part2 + part3;
//    }
//
//    private static double bezier3funcY(float uu, List<PointVo> controlPoint) {
//        double part0 = controlPoint.get(0).getY() * uu * uu * uu;
//        double part1 = 3 * controlPoint.get(1).getY() * uu * uu * (1 - uu);
//        double part2 = 3 * controlPoint.get(2).getY() * uu * (1 - uu) * (1 - uu);
//        double part3 = controlPoint.get(3).getY() * (1 - uu) * (1 - uu) * (1 - uu);
//        return part0 + part1 + part2 + part3;
//    }
//
//    private static List<PointVo> initSizeList(int size) {
//        List<PointVo> points = new ArrayList<>();
//        for (int i = 0; i < size; i++) {
//            PointVo vo = new PointVo();
//            points.add(vo);
//        }
//        return points;
//    }


}
