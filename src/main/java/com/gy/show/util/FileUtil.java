package com.gy.show.util;


import com.gy.show.common.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
public class FileUtil {

    /**
     * 文件上传分片写入
     *
     * @param file         上传文件
     * @param outFilePath  输出路径
     * @return
     * @throws IOException
     */
    public static void writeFile(MultipartFile file, String outFilePath) {
        File newFile = new File(outFilePath);
        try (RandomAccessFile randomAccessFile = new RandomAccessFile(newFile, "rw")) {
            File outFile = new File(outFilePath);
            // 如果不存在文件夹则先创建
            if (!Files.exists(Paths.get(outFile.getParent()))) {
                log.info("目录不存在，创建目录：{}", outFile.getName());
                Files.createDirectories(Paths.get(outFile.getParent()));
            }
            randomAccessFile.write(file.getBytes());
            randomAccessFile.getFD().sync();
        } catch (Exception e) {
            log.error("写入文件异常", e);
            throw new ServiceException("上传文件时写入失败");
        }
    }

}
