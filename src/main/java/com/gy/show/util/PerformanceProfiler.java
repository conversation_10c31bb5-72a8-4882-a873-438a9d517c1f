package com.gy.show.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Comparator;

/**
 * 性能分析器
 * 用于详细分析方法执行时间和性能瓶颈
 */
@Slf4j
@Component
public class PerformanceProfiler {

    private final ConcurrentHashMap<String, MethodProfile> methodProfiles = new ConcurrentHashMap<>();
    private final ThreadLocal<String> currentMethod = new ThreadLocal<>();

    /**
     * 开始性能分析
     */
    public void startProfiling(String methodName) {
        long startTime = System.nanoTime();
        currentMethod.set(methodName);

        MethodProfile profile = methodProfiles.computeIfAbsent(methodName, k -> new MethodProfile(methodName));
        profile.recordStart(startTime);
    }

    /**
     * 结束性能分析
     */
    public long endProfiling(String methodName) {
        long endTime = System.nanoTime();

        MethodProfile profile = methodProfiles.get(methodName);
        if (profile != null) {
            long duration = profile.recordEnd(endTime);

            // 如果执行时间超过100ms，记录警告
            if (duration > 100_000_000) { // 100ms in nanoseconds
                log.warn("性能警告 - 方法 {} 执行时间: {}ms", methodName, duration / 1_000_000);
            }

            return duration;
        }

        currentMethod.remove();
        return 0;
    }

    /**
     * 获取性能报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("\n=== 性能分析报告 ===\n");

        List<MethodProfile> profiles = new ArrayList<>(methodProfiles.values());
        profiles.sort(Comparator.comparing(MethodProfile::getAverageTime).reversed());

        for (MethodProfile profile : profiles) {
            report.append(String.format(
                    "方法: %-30s | 调用次数: %-6d | 总时间: %-8.2fms | 平均时间: %-8.2fms | 最大时间: %-8.2fms\n",
                    profile.getMethodName(),
                    profile.getCallCount(),
                    profile.getTotalTime() / 1_000_000.0,
                    profile.getAverageTime() / 1_000_000.0,
                    profile.getMaxTime() / 1_000_000.0
            ));
        }

        report.append("==================\n");
        return report.toString();
    }

    /**
     * 重置所有统计数据
     */
    public void reset() {
        methodProfiles.clear();
        currentMethod.remove();
    }

    /**
     * 获取最慢的方法
     */
    public List<String> getSlowestMethods(int count) {
        return methodProfiles.values().stream()
                .sorted(Comparator.comparing(MethodProfile::getAverageTime).reversed())
                .limit(count)
                .map(profile -> String.format("%s (%.2fms)",
                        profile.getMethodName(),
                        profile.getAverageTime() / 1_000_000.0))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 方法性能统计
     */
    private static class MethodProfile {
        private final String methodName;
        private final AtomicLong callCount = new AtomicLong(0);
        private final AtomicLong totalTime = new AtomicLong(0);
        private final AtomicLong maxTime = new AtomicLong(0);
        private volatile long lastStartTime = 0;

        public MethodProfile(String methodName) {
            this.methodName = methodName;
        }

        public void recordStart(long startTime) {
            this.lastStartTime = startTime;
        }

        public long recordEnd(long endTime) {
            long duration = endTime - lastStartTime;

            callCount.incrementAndGet();
            totalTime.addAndGet(duration);

            // 更新最大时间
            long currentMax = maxTime.get();
            while (duration > currentMax && !maxTime.compareAndSet(currentMax, duration)) {
                currentMax = maxTime.get();
            }

            return duration;
        }

        public String getMethodName() {
            return methodName;
        }

        public long getCallCount() {
            return callCount.get();
        }

        public long getTotalTime() {
            return totalTime.get();
        }

        public long getAverageTime() {
            long count = callCount.get();
            return count > 0 ? totalTime.get() / count : 0;
        }

        public long getMaxTime() {
            return maxTime.get();
        }
    }
}
