package com.gy.show.util;

import lombok.Data;

import java.util.Random;


public class TargetDataSimulator {

    private static Random random = new Random();

    // 随机生成char
    private static char getRandomChar() {
        return (char) (random.nextInt(26) + 'A');
    }

    // 随机生成范围内的int32
    private static int getRandomInt32(int min, int max) {
        return random.nextInt(max - min + 1) + min;
    }

    // 随机生成float
    private static float getRandomFloat(float min, float max) {
        return min + random.nextFloat() * (max - min);
    }

    @Data
    public static class TargetDData {
        private char targetCode;                // 目标代号
        private char domain;                    // 所属域
        private int longitude;                  // 经度
        private int latitude;                   // 纬度
        private float altitude;                 // 高度 (km)
        private float pitch;                    // 俯仰角 (-90 ~ 90°)
        private float yaw;                      // 偏航角 (-180 ~ 180°)
        private float roll;                     // 滚转角 (-180 ~ 180°)
        private float speed;                    // 综合速度 (km/s)

        public TargetDData(char targetCode, char domain, int longitude, int latitude, float altitude,
                           float pitch, float yaw, float roll, float speed) {
            this.targetCode = targetCode;
            this.domain = domain;
            this.longitude = longitude;
            this.latitude = latitude;
            this.altitude = altitude;
            this.pitch = pitch;
            this.yaw = yaw;
            this.roll = roll;
            this.speed = speed;
        }

        @Override
        public String toString() {
            return "TargetData{" +
                    "targetCode=" + targetCode +
                    ", domain=" + domain +
                    ", longitude=" + longitude +
                    ", latitude=" + latitude +
                    ", altitude=" + altitude +
                    ", pitch=" + pitch +
                    ", yaw=" + yaw +
                    ", roll=" + roll +
                    ", speed=" + speed +
                    '}';
        }
    }

    public static TargetDData generateRandomTargetData() {
        char targetCode = getRandomChar();                // 目标代号
        char domain = getRandomChar();                    // 所属域
        int longitude = getRandomInt32(-647999999, 648000000); // 经度
        int latitude = getRandomInt32(-324000000, 324000000);  // 纬度
        float altitude = getRandomFloat(0, 100);          // 高度 (km)
        float pitch = getRandomFloat(-90, 90);            // 俯仰角 (-90 ~ 90°)
        float yaw = getRandomFloat(-180, 180);            // 偏航角 (-180 ~ 180°)
        float roll = getRandomFloat(-180, 180);           // 滚转角 (-180 ~ 180°)
        float speed = getRandomFloat(0, 8);               // 综合速度 (km/s)

        return new TargetDData(targetCode, domain, longitude, latitude, altitude, pitch, yaw, roll, speed);
    }

    public static void main(String[] args) {
        // 生成并输出随机的目标数据
        TargetDData targetData = generateRandomTargetData();
        System.out.println(targetData);
    }
}


