package com.gy.show.util;

import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.enums.ScheduleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 批量目标创建配置示例
 * 演示如何按数据类型分组创建目标和匹配航迹
 */
@Slf4j
@Component
public class BatchRequirementConfigExample {

    /**
     * 创建混合类型目标的需求配置示例
     * 包含：30个无人机 + 20个无人艇 + 15个无人车 + 5个弹 = 70个目标
     */
    public BatchRequirementCreateDTO createMixedTargetsExample() {
        BatchRequirementCreateDTO batchConfig = new BatchRequirementCreateDTO();

        // 设置需求基本信息
        batchConfig.setRequirementName("混合类型目标需求");
        batchConfig.setRequirementType(ScheduleTypeEnum.YGH.getCode()); // 预规划任务
        batchConfig.setImportance(2); // 重要
        batchConfig.setStartTime(LocalDateTime.now().plusHours(1)); // 1小时后开始
        batchConfig.setEndTime(LocalDateTime.now().plusDays(1)); // 1天后结束
        batchConfig.setRequirementComment("按数据类型分组创建混合目标");
        batchConfig.setTargetCount(70); // 总共70个目标

        // 设置目标类型配置
        List<BatchRequirementCreateDTO.TargetTypeConfig> targetTypeConfigs = new ArrayList<>();

        // 无人机配置
        BatchRequirementCreateDTO.TargetTypeConfig uavConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        uavConfig.setDataType(4); // 无人机
        uavConfig.setCount(30);
        uavConfig.setTrackNamePrefix("无人机航迹"); // 匹配"无人机航迹1"、"无人机航迹2"等
        uavConfig.setTargetNamePrefix("无人机"); // 匹配包含"无人机"的目标名称

        // 无人机任务配置
        BatchRequirementCreateDTO.TaskConfig uavTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        uavTaskConfig.setTaskTypes(Arrays.asList(1, 2, 3, 4)); // 遥控、遥测、测量、数传
        uavTaskConfig.setTaskStartOffset(15); // 无人机任务延迟15分钟开始
        uavTaskConfig.setTaskDuration(180); // 无人机任务持续3小时
        uavTaskConfig.setRepeatType(0); // 仅一次
        uavConfig.setTaskConfig(uavTaskConfig);

        // 无人机航迹配置
        BatchRequirementCreateDTO.TrackConfig uavTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        uavTrackConfig.setTrackStartOffset(0); // 无人机航迹与需求同时开始
        uavTrackConfig.setUseExistingTrack(true);
        uavConfig.setTrackConfig(uavTrackConfig);

        targetTypeConfigs.add(uavConfig);

        // 无人艇配置
        BatchRequirementCreateDTO.TargetTypeConfig boatConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        boatConfig.setDataType(5); // 无人艇
        boatConfig.setCount(20);
        boatConfig.setTrackNamePrefix("无人艇航迹"); // 匹配"无人艇航迹1"、"无人艇航迹2"等
        boatConfig.setTargetNamePrefix("无人艇"); // 匹配包含"无人艇"的目标名称

        // 无人艇任务配置
        BatchRequirementCreateDTO.TaskConfig boatTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        boatTaskConfig.setTaskTypes(Arrays.asList(1, 2)); // 遥控、遥测
        boatTaskConfig.setTaskStartOffset(30); // 无人艇任务延迟30分钟开始
        boatTaskConfig.setTaskDuration(120); // 无人艇任务持续2小时
        boatTaskConfig.setRepeatType(0); // 仅一次
        boatConfig.setTaskConfig(boatTaskConfig);

        // 无人艇航迹配置
        BatchRequirementCreateDTO.TrackConfig boatTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        boatTrackConfig.setTrackStartOffset(10); // 无人艇航迹延迟10分钟开始
        boatTrackConfig.setUseExistingTrack(true);
        boatConfig.setTrackConfig(boatTrackConfig);

        targetTypeConfigs.add(boatConfig);

        // 无人车配置
        BatchRequirementCreateDTO.TargetTypeConfig carConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        carConfig.setDataType(6); // 无人车
        carConfig.setCount(15);
        carConfig.setTrackNamePrefix("无人车航迹"); // 匹配"无人车航迹1"、"无人车航迹2"等
        carConfig.setTargetNamePrefix("无人车"); // 匹配包含"无人车"的目标名称

        // 无人车任务配置
        BatchRequirementCreateDTO.TaskConfig carTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        carTaskConfig.setTaskTypes(Arrays.asList(1, 3)); // 遥控、测量
        carTaskConfig.setTaskStartOffset(45); // 无人车任务延迟45分钟开始
        carTaskConfig.setTaskDuration(90); // 无人车任务持续1.5小时
        carTaskConfig.setRepeatType(0); // 仅一次
        carConfig.setTaskConfig(carTaskConfig);

        // 无人车航迹配置
        BatchRequirementCreateDTO.TrackConfig carTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        carTrackConfig.setTrackStartOffset(20); // 无人车航迹延迟20分钟开始
        carTrackConfig.setUseExistingTrack(true);
        carConfig.setTrackConfig(carTrackConfig);

        targetTypeConfigs.add(carConfig);

        // 弹配置
        BatchRequirementCreateDTO.TargetTypeConfig missileConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        missileConfig.setDataType(7); // 弹
        missileConfig.setCount(5);
        missileConfig.setTrackNamePrefix("弹航迹"); // 匹配"弹航迹1"、"弹航迹2"等
        missileConfig.setTargetNamePrefix("弹"); // 匹配包含"弹"的目标名称

        // 弹任务配置
        BatchRequirementCreateDTO.TaskConfig missileTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        missileTaskConfig.setTaskTypes(Arrays.asList(2, 3)); // 遥测、测量
        missileTaskConfig.setTaskStartOffset(60); // 弹任务延迟1小时开始
        missileTaskConfig.setTaskDuration(60); // 弹任务持续1小时
        missileTaskConfig.setRepeatType(0); // 仅一次
        missileConfig.setTaskConfig(missileTaskConfig);

        // 弹航迹配置
        BatchRequirementCreateDTO.TrackConfig missileTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        missileTrackConfig.setTrackStartOffset(30); // 弹航迹延迟30分钟开始
        missileTrackConfig.setUseExistingTrack(true);
        missileConfig.setTrackConfig(missileTrackConfig);

        targetTypeConfigs.add(missileConfig);

        batchConfig.setTargetTypeConfigs(targetTypeConfigs);

        log.info("创建混合类型目标需求配置：{}", batchConfig);
        return batchConfig;
    }

    /**
     * 创建单一类型目标的需求配置示例
     * 仅包含100个无人机目标
     */
    public BatchRequirementCreateDTO createUavOnlyExample() {
        BatchRequirementCreateDTO batchConfig = new BatchRequirementCreateDTO();

        // 设置需求基本信息
        batchConfig.setRequirementName("无人机专项需求");
        batchConfig.setRequirementType(ScheduleTypeEnum.YGH.getCode());
        batchConfig.setImportance(1); // 一般
        batchConfig.setStartTime(LocalDateTime.now().plusHours(2));
        batchConfig.setEndTime(LocalDateTime.now().plusDays(2));
        batchConfig.setRequirementComment("专门针对无人机的批量目标创建");
        batchConfig.setTargetCount(100); // 100个无人机目标

        // 设置目标类型配置
        List<BatchRequirementCreateDTO.TargetTypeConfig> targetTypeConfigs = new ArrayList<>();

        // 无人机配置
        BatchRequirementCreateDTO.TargetTypeConfig uavConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        uavConfig.setDataType(4); // 无人机
        uavConfig.setCount(100);
        uavConfig.setTrackNamePrefix("无人机"); // 匹配包含"无人机"的航迹名称

        // 无人机任务配置
        BatchRequirementCreateDTO.TaskConfig uavTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        uavTaskConfig.setTaskTypes(Arrays.asList(1, 2, 3, 4)); // 遥控、遥测、测量、数传
        uavTaskConfig.setTaskStartOffset(0); // 任务与需求同时开始
        uavTaskConfig.setTaskDuration(180); // 任务持续3小时
        uavTaskConfig.setRepeatType(1); // 每日重复
        uavConfig.setTaskConfig(uavTaskConfig);

        // 无人机航迹配置
        BatchRequirementCreateDTO.TrackConfig uavTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        uavTrackConfig.setTrackStartOffset(0);
        uavTrackConfig.setUseExistingTrack(true);
        uavConfig.setTrackConfig(uavTrackConfig);

        targetTypeConfigs.add(uavConfig);

        batchConfig.setTargetTypeConfigs(targetTypeConfigs);

        return batchConfig;
    }

    /**
     * 创建大规模目标的需求配置示例
     * 包含：500个无人机 + 300个无人艇 + 150个无人车 + 50个弹 = 1000个目标
     */
    public BatchRequirementCreateDTO createLargeScaleExample() {
        BatchRequirementCreateDTO batchConfig = new BatchRequirementCreateDTO();

        // 设置需求基本信息
        batchConfig.setRequirementName("大规模混合目标需求");
        batchConfig.setRequirementType(ScheduleTypeEnum.YGH.getCode());
        batchConfig.setImportance(2); // 重要
        batchConfig.setStartTime(LocalDateTime.now().plusHours(3));
        batchConfig.setEndTime(LocalDateTime.now().plusDays(3));
        batchConfig.setRequirementComment("大规模1000个目标的批量创建");
        batchConfig.setTargetCount(1000); // 1000个目标

        // 设置目标类型配置
        List<BatchRequirementCreateDTO.TargetTypeConfig> targetTypeConfigs = new ArrayList<>();

        // 无人机配置 - 500个
        BatchRequirementCreateDTO.TargetTypeConfig uavConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        uavConfig.setDataType(4);
        uavConfig.setCount(500);
        uavConfig.setTrackNamePrefix("无人机航迹");

        // 无人机任务配置
        BatchRequirementCreateDTO.TaskConfig uavTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        uavTaskConfig.setTaskTypes(Arrays.asList(1, 2, 3, 4)); // 遥控、遥测、测量、数传
        uavTaskConfig.setTaskStartOffset(30); // 无人机任务延迟30分钟开始
        uavTaskConfig.setTaskDuration(300); // 无人机任务持续5小时
        uavTaskConfig.setRepeatType(0); // 仅一次
        uavConfig.setTaskConfig(uavTaskConfig);

        // 无人机航迹配置
        BatchRequirementCreateDTO.TrackConfig uavTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        uavTrackConfig.setTrackStartOffset(0);
        uavTrackConfig.setUseExistingTrack(true);
        uavConfig.setTrackConfig(uavTrackConfig);

        targetTypeConfigs.add(uavConfig);

        // 无人艇配置 - 300个
        BatchRequirementCreateDTO.TargetTypeConfig boatConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        boatConfig.setDataType(5);
        boatConfig.setCount(300);
        boatConfig.setTrackNamePrefix("无人艇航迹");

        // 无人艇任务配置
        BatchRequirementCreateDTO.TaskConfig boatTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        boatTaskConfig.setTaskTypes(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量
        boatTaskConfig.setTaskStartOffset(60); // 无人艇任务延迟1小时开始
        boatTaskConfig.setTaskDuration(240); // 无人艇任务持续4小时
        boatTaskConfig.setRepeatType(0); // 仅一次
        boatConfig.setTaskConfig(boatTaskConfig);

        // 无人艇航迹配置
        BatchRequirementCreateDTO.TrackConfig boatTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        boatTrackConfig.setTrackStartOffset(15);
        boatTrackConfig.setUseExistingTrack(true);
        boatConfig.setTrackConfig(boatTrackConfig);

        targetTypeConfigs.add(boatConfig);

        // 无人车配置 - 150个
        BatchRequirementCreateDTO.TargetTypeConfig carConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        carConfig.setDataType(6);
        carConfig.setCount(150);
        carConfig.setTrackNamePrefix("无人车航迹");

        // 无人车任务配置
        BatchRequirementCreateDTO.TaskConfig carTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        carTaskConfig.setTaskTypes(Arrays.asList(1, 3)); // 遥控、测量
        carTaskConfig.setTaskStartOffset(90); // 无人车任务延迟1.5小时开始
        carTaskConfig.setTaskDuration(180); // 无人车任务持续3小时
        carTaskConfig.setRepeatType(0); // 仅一次
        carConfig.setTaskConfig(carTaskConfig);

        // 无人车航迹配置
        BatchRequirementCreateDTO.TrackConfig carTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        carTrackConfig.setTrackStartOffset(30);
        carTrackConfig.setUseExistingTrack(true);
        carConfig.setTrackConfig(carTrackConfig);

        targetTypeConfigs.add(carConfig);

        // 弹配置 - 50个
        BatchRequirementCreateDTO.TargetTypeConfig missileConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        missileConfig.setDataType(7);
        missileConfig.setCount(50);
        missileConfig.setTrackNamePrefix("弹航迹");

        // 弹任务配置
        BatchRequirementCreateDTO.TaskConfig missileTaskConfig = new BatchRequirementCreateDTO.TaskConfig();
        missileTaskConfig.setTaskTypes(Arrays.asList(2, 3)); // 遥测、测量
        missileTaskConfig.setTaskStartOffset(120); // 弹任务延迟2小时开始
        missileTaskConfig.setTaskDuration(120); // 弹任务持续2小时
        missileTaskConfig.setRepeatType(0); // 仅一次
        missileConfig.setTaskConfig(missileTaskConfig);

        // 弹航迹配置
        BatchRequirementCreateDTO.TrackConfig missileTrackConfig = new BatchRequirementCreateDTO.TrackConfig();
        missileTrackConfig.setTrackStartOffset(45);
        missileTrackConfig.setUseExistingTrack(true);
        missileConfig.setTrackConfig(missileTrackConfig);

        targetTypeConfigs.add(missileConfig);

        batchConfig.setTargetTypeConfigs(targetTypeConfigs);

        return batchConfig;
    }
}
