//package com.gy.show.util;
//
//import com.gy.show.entity.dto.BatchRequirementCreateDTO;
//import com.gy.show.enums.ScheduleTypeEnum;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.Arrays;
//
///**
// * 批量目标创建示例
// * 演示如何在一个需求中创建1000个目标
// */
//@Slf4j
//@Component
//public class BatchRequirementExample {
//
//    /**
//     * 创建包含1000个目标的需求配置示例
//     */
//    public BatchRequirementCreateDTO create1000TargetsExample() {
//        BatchRequirementCreateDTO batchConfig = new BatchRequirementCreateDTO();
//
//        // 设置需求基本信息
//        batchConfig.setRequirementName("包含1000个目标的需求");
//        batchConfig.setRequirementType(ScheduleTypeEnum.YGH.getCode()); // 预规划任务
//        batchConfig.setImportance(1); // 一般重要程度
//        batchConfig.setStartTime(LocalDateTime.now().plusHours(1)); // 1小时后开始
//        batchConfig.setEndTime(LocalDateTime.now().plusDays(1)); // 1天后结束
//        batchConfig.setRequirementComment("在一个需求中批量创建1000个目标");
//        batchConfig.setTargetCount(1000); // 创建1000个目标
//
//        // 设置任务配置
//        BatchRequirementCreateDTO.TaskConfig taskConfig = new BatchRequirementCreateDTO.TaskConfig();
//        taskConfig.setTaskTypes(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量
//        taskConfig.setTaskStartOffset(30); // 任务开始时间相对需求开始时间延迟30分钟
//        taskConfig.setTaskDuration(120); // 任务持续2小时
//        taskConfig.setRepeatType(0); // 仅一次
//        batchConfig.setTaskConfig(taskConfig);
//
//        // 设置航迹配置
//        BatchRequirementCreateDTO.TrackConfig trackConfig = new BatchRequirementCreateDTO.TrackConfig();
//        trackConfig.setTrackStartOffset(0); // 航迹开始时间与需求开始时间一致
//        trackConfig.setUseExistingTrack(true); // 使用现有航迹
//        batchConfig.setTrackConfig(trackConfig);
//
//        log.info("创建包含1000个目标的需求配置：{}", batchConfig);
//        return batchConfig;
//    }
//
//    /**
//     * 创建包含测控任务的1000个目标的需求配置示例
//     */
//    public BatchRequirementCreateDTO createControlTaskExample() {
//        BatchRequirementCreateDTO batchConfig = new BatchRequirementCreateDTO();
//
//        // 设置需求基本信息
//        batchConfig.setRequirementName("包含1000个测控目标的需求");
//        batchConfig.setRequirementType(ScheduleTypeEnum.YGH.getCode()); // 预规划任务
//        batchConfig.setImportance(2); // 重要
//        batchConfig.setStartTime(LocalDateTime.now().plusHours(2)); // 2小时后开始
//        batchConfig.setEndTime(LocalDateTime.now().plusDays(2)); // 2天后结束
//        batchConfig.setRequirementComment("在一个需求中创建1000个测控目标");
//        batchConfig.setTargetCount(1000); // 创建1000个目标
//
//        // 设置任务配置 - 测控任务（遥控、遥测、测量会被合并为测控任务）
//        BatchRequirementCreateDTO.TaskConfig taskConfig = new BatchRequirementCreateDTO.TaskConfig();
//        taskConfig.setTaskTypes(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量（会被合并为测控）
//        taskConfig.setTaskStartOffset(0); // 任务与需求同时开始
//        taskConfig.setTaskDuration(180); // 任务持续3小时
//        taskConfig.setRepeatType(1); // 每日重复
//        batchConfig.setTaskConfig(taskConfig);
//
//        // 设置航迹配置
//        BatchRequirementCreateDTO.TrackConfig trackConfig = new BatchRequirementCreateDTO.TrackConfig();
//        trackConfig.setTrackStartOffset(0); // 航迹开始时间与需求开始时间一致
//        trackConfig.setUseExistingTrack(true); // 使用现有航迹
//        batchConfig.setTrackConfig(trackConfig);
//
//        return batchConfig;
//    }
//
//    /**
//     * 创建包含数传任务的1000个目标的需求配置示例
//     */
//    public BatchRequirementCreateDTO createDataTransmissionExample() {
//        BatchRequirementCreateDTO batchConfig = new BatchRequirementCreateDTO();
//
//        // 设置需求基本信息
//        batchConfig.setRequirementName("包含1000个数传目标的需求");
//        batchConfig.setRequirementType(ScheduleTypeEnum.YGH.getCode()); // 预规划任务
//        batchConfig.setImportance(1); // 一般
//        batchConfig.setStartTime(LocalDateTime.now().plusHours(3)); // 3小时后开始
//        batchConfig.setEndTime(LocalDateTime.now().plusDays(3)); // 3天后结束
//        batchConfig.setRequirementComment("在一个需求中创建1000个数传目标");
//        batchConfig.setTargetCount(1000); // 创建1000个目标
//
//        // 设置任务配置 - 数传任务
//        BatchRequirementCreateDTO.TaskConfig taskConfig = new BatchRequirementCreateDTO.TaskConfig();
//        taskConfig.setTaskTypes(Arrays.asList(4)); // 数传
//        taskConfig.setTaskStartOffset(60); // 任务开始时间相对需求开始时间延迟1小时
//        taskConfig.setTaskDuration(90); // 任务持续1.5小时
//        taskConfig.setRepeatType(0); // 仅一次
//        batchConfig.setTaskConfig(taskConfig);
//
//        // 设置航迹配置
//        BatchRequirementCreateDTO.TrackConfig trackConfig = new BatchRequirementCreateDTO.TrackConfig();
//        trackConfig.setTrackStartOffset(0); // 航迹开始时间与需求开始时间一致
//        trackConfig.setUseExistingTrack(true); // 使用现有航迹
//        batchConfig.setTrackConfig(trackConfig);
//
//        return batchConfig;
//    }
//}
