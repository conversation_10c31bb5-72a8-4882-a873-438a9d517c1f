package com.gy.show.util;

import lombok.Data;

import java.util.Random;

public class MaritimeDataSimulator {

    private static Random random = new Random();

    // 随机生成char
    private static char getRandomChar() {
        return (char) (random.nextInt(26) + 'A');
    }

    // 随机生成范围内的int32
    private static int getRandomInt32(int min, int max) {
        return random.nextInt(max - min + 1) + min;
    }

    // 随机生成状态信息
    private static String getRandomFromOptions(String[] options) {
        return options[random.nextInt(options.length)];
    }

    @Data
    public static class MaritimeData {
        private char targetCode;                  // 目标代号
        private char domain;                      // 所属域
        private int latitude;                     // 纬度
        private int longitude;                    // 经度
        private int speed;                        // 航速
        private int heading;                     // 航向
        private int bowDirection;                // 艏向
        private int turnRate;                     // 转向速率
        private int roll;                         // 横滚
        private int pitch;                        // 俯仰
        private int windSpeed;                    // 风速
        private char windDirection;               // 风向
        private String positioningDeviceStatus;   // 定位设备状态
        private String compassDeviceStatus;       // 罗经设备状态
        private String windDirectionDeviceStatus; // 风向设备状态

        public MaritimeData(char targetCode, char domain, int latitude, int longitude, int speed, int heading, int bowDirection,
                            int turnRate, int roll, int pitch, int windSpeed, char windDirection, String positioningDeviceStatus,
                            String compassDeviceStatus, String windDirectionDeviceStatus) {
            this.targetCode = targetCode;
            this.domain = domain;
            this.latitude = latitude;
            this.longitude = longitude;
            this.speed = speed;
            this.heading = heading;
            this.bowDirection = bowDirection;
            this.turnRate = turnRate;
            this.roll = roll;
            this.pitch = pitch;
            this.windSpeed = windSpeed;
            this.windDirection = windDirection;
            this.positioningDeviceStatus = positioningDeviceStatus;
            this.compassDeviceStatus = compassDeviceStatus;
            this.windDirectionDeviceStatus = windDirectionDeviceStatus;
        }

        @Override
        public String toString() {
            return "MaritimeData{" +
                    "targetCode=" + targetCode +
                    ", domain=" + domain +
                    ", latitude=" + latitude +
                    ", longitude=" + longitude +
                    ", speed=" + speed +
                    ", heading=" + heading +
                    ", bowDirection=" + bowDirection +
                    ", turnRate=" + turnRate +
                    ", roll=" + roll +
                    ", pitch=" + pitch +
                    ", windSpeed=" + windSpeed +
                    ", windDirection=" + windDirection +
                    ", positioningDeviceStatus='" + positioningDeviceStatus + '\'' +
                    ", compassDeviceStatus='" + compassDeviceStatus + '\'' +
                    ", windDirectionDeviceStatus='" + windDirectionDeviceStatus + '\'' +
                    '}';
        }
    }

    // 模拟生成数据
    public static MaritimeData generateRandomMaritimeData() {
        char targetCode = getRandomChar();                 // 目标代号
        char domain = getRandomChar();                     // 所属域
        int latitude = getRandomInt32(-90000000, 90000000);   // 纬度
        int longitude = getRandomInt32(-180000000, 180000000); // 经度
        int speed = getRandomInt32(0, 500);                // 航速
        int heading = getRandomInt32(-45, 45);                     // 航向
        int bowDirection = getRandomInt32(-45, 45);               // 艏向
        int turnRate = getRandomInt32(-100, 100);          // 转向速率
        int roll = getRandomInt32(-45, 45);                // 横滚
        int pitch = getRandomInt32(-30, 30);               // 俯仰
        int windSpeed = getRandomInt32(0, 150);            // 风速
        char windDirection = getRandomChar();              // 风向
        String positioningDeviceStatus = getRandomFromOptions(new String[]{"正常", "故障"}); // 定位设备状态
        String compassDeviceStatus = getRandomFromOptions(new String[]{"正常", "故障"});     // 罗经设备状态
        String windDirectionDeviceStatus = getRandomFromOptions(new String[]{"正常", "故障"}); // 风向设备状态

        return new MaritimeData(targetCode, domain, latitude, longitude, speed, heading, bowDirection, turnRate, roll, pitch,
                windSpeed, windDirection, positioningDeviceStatus, compassDeviceStatus, windDirectionDeviceStatus);
    }

    public static void main(String[] args) {
        // 生成并输出随机的船舶数据
        MaritimeData maritimeData = generateRandomMaritimeData();
        System.out.println(maritimeData);
    }
}


