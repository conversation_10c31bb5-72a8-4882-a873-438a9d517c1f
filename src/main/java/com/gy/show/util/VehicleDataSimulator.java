package com.gy.show.util;

import lombok.Data;

import java.util.Random;

public class VehicleDataSimulator {

    private static Random random = new Random();

    // 随机生成char
    private static char getRandomChar() {
        return (char) (random.nextInt(26) + 'A');
    }

    // 随机生成范围内的int
    private static int getRandomInt(int min, int max) {
        return random.nextInt(max - min + 1) + min;
    }

    // 随机生成范围内的short
    private static short getRandomShort(int min, int max) {
        return (short) (random.nextInt(max - min + 1) + min);
    }

    // 随机生成0-1之间的char值
    private static char getRandomCharRange(int min, int max) {
        return (char) getRandomInt(min, max);
    }

    // 随机生成状态信息
    private static String getRandomFromOptions(String[] options) {
        return options[random.nextInt(options.length)];
    }

    @Data
    public static class VehicleData {
        private String targetCode;                     // 目标代号
        private String domain;                         // 所属域
        private String pitchAngle;                     // 俯仰角
        private String rollAngle;                      // 侧倾角
        private int headingAngle;                    // 航向角
        private long longitude;                      // 经度
        private long latitude;                       // 纬度
        private short trackingDeviation;             // 跟踪偏差
        private int realTimeSpeed;                   // 实时速度
        private String parkingState;                 // 驻车状态
        private String gear;                         // 档位
        private String emergencyBrakeState;          // 紧急停车状态
        private String drivingMode;                  // 驾驶模式
        private short vehicleSpeed;                  // 车速
        private String batteryVoltage;                 // 系统电压（电池状态电压）
        private String batterySoc;                     // 电池SOC
        private int batteryCurrent;                  // 电池电流
        private int mileage;                         // 车辆行驶里程
        private String batteryStatus;                // 电池状态信息
        private String batteryAlarm;                 // 电池告警信息
        private int engineWaterTemp;                 // 发动机水温
        private int generatorControllerTemp;         // 发电机控制器温度
        private int generatorTemp;                   // 发电机温度
        private int generatorDCVoltage;              // 发电机直流侧电压
        private String generatorFault;               // 发电机故障
        private String generatorStatusFault;         // 发电机状态故障
        private String generatorCommunicationFault;  // 发电机通信故障
        private String engineCommunicationFault;     // 发动机通信故障
        private String apuCommunicationAnomaly;      // APU通信异常
        private String engineTempAnomaly;            // 发动机温度异常
        private String engineOilPressureAnomaly;     // 发动机机油压异常

        public VehicleData(String targetCode, String domain, String pitchAngle, String rollAngle, int headingAngle, long longitude, long latitude,
                           short trackingDeviation, int realTimeSpeed, String parkingState, String gear, String emergencyBrakeState,
                           String drivingMode, short vehicleSpeed, String batteryVoltage, String batterySoc, int batteryCurrent, int mileage,
                           String batteryStatus, String batteryAlarm, int engineWaterTemp, int generatorControllerTemp, int generatorTemp,
                           int generatorDCVoltage, String generatorFault, String generatorStatusFault, String generatorCommunicationFault,
                           String engineCommunicationFault, String apuCommunicationAnomaly, String engineTempAnomaly, String engineOilPressureAnomaly) {
            this.targetCode = targetCode;
            this.domain = domain;
            this.pitchAngle = pitchAngle;
            this.rollAngle = rollAngle;
            this.headingAngle = headingAngle;
            this.longitude = longitude;
            this.latitude = latitude;
            this.trackingDeviation = trackingDeviation;
            this.realTimeSpeed = realTimeSpeed;
            this.parkingState = parkingState;
            this.gear = gear;
            this.emergencyBrakeState = emergencyBrakeState;
            this.drivingMode = drivingMode;
            this.vehicleSpeed = vehicleSpeed;
            this.batteryVoltage = batteryVoltage;
            this.batterySoc = batterySoc;
            this.batteryCurrent = batteryCurrent;
            this.mileage = mileage;
            this.batteryStatus = batteryStatus;
            this.batteryAlarm = batteryAlarm;
            this.engineWaterTemp = engineWaterTemp;
            this.generatorControllerTemp = generatorControllerTemp;
            this.generatorTemp = generatorTemp;
            this.generatorDCVoltage = generatorDCVoltage;
            this.generatorFault = generatorFault;
            this.generatorStatusFault = generatorStatusFault;
            this.generatorCommunicationFault = generatorCommunicationFault;
            this.engineCommunicationFault = engineCommunicationFault;
            this.apuCommunicationAnomaly = apuCommunicationAnomaly;
            this.engineTempAnomaly = engineTempAnomaly;
            this.engineOilPressureAnomaly = engineOilPressureAnomaly;
        }

        @Override
        public String toString() {
            return "VehicleData{" +
                    "targetCode=" + targetCode +
                    ", domain=" + domain +
                    ", pitchAngle=" + pitchAngle +
                    ", rollAngle=" + rollAngle +
                    ", headingAngle=" + headingAngle +
                    ", longitude=" + longitude +
                    ", latitude=" + latitude +
                    ", trackingDeviation=" + trackingDeviation +
                    ", realTimeSpeed=" + realTimeSpeed +
                    ", parkingState='" + parkingState + '\'' +
                    ", gear='" + gear + '\'' +
                    ", emergencyBrakeState='" + emergencyBrakeState + '\'' +
                    ", drivingMode='" + drivingMode + '\'' +
                    ", vehicleSpeed=" + vehicleSpeed +
                    ", batteryVoltage=" +  batteryVoltage +
                    ", batterySoc=" +  batterySoc +
                    ", batteryCurrent=" + batteryCurrent +
                    ", mileage=" + mileage +
                    ", batteryStatus='" + batteryStatus + '\'' +
                    ", batteryAlarm='" + batteryAlarm + '\'' +
                    ", engineWaterTemp=" + engineWaterTemp +
                    ", generatorControllerTemp=" + generatorControllerTemp +
                    ", generatorTemp=" + generatorTemp +
                    ", generatorDCVoltage=" + generatorDCVoltage +
                    ", generatorFault='" + generatorFault + '\'' +
                    ", generatorStatusFault='" + generatorStatusFault + '\'' +
                    ", generatorCommunicationFault='" + generatorCommunicationFault + '\'' +
                    ", engineCommunicationFault='" + engineCommunicationFault + '\'' +
                    ", apuCommunicationAnomaly='" + apuCommunicationAnomaly + '\'' +
                    ", engineTempAnomaly='" + engineTempAnomaly + '\'' +
                    ", engineOilPressureAnomaly='" + engineOilPressureAnomaly + '\'' +
                    '}';
        }
    }

    public static VehicleData generateRandomVehicleData() {
        String targetCode = getRandomChar() + "";
        String domain = getRandomChar() + "";
        String pitchAngle = getRandomInt(-90, 90) + "";
        String rollAngle = getRandomInt(-90, 90)  + "";
        int headingAngle = getRandomInt(0, 360);
        long longitude = getRandomInt(0, 180);
        long latitude = getRandomInt(0, 90);
        short trackingDeviation = getRandomShort(-100, 100);
        int realTimeSpeed = getRandomInt(0, 100);
        String parkingState = getRandomFromOptions(new String[]{"驻车", "无驻车"});
        String gear = getRandomFromOptions(new String[]{"空挡", "前进低挡", "倒车低挡", "前进中挡", "倒车中挡", "前进高挡", "倒车高挡", "中心转向"});
        String emergencyBrakeState = getRandomFromOptions(new String[]{"紧急停车中", "非紧急停车状态"});
        String drivingMode = getRandomFromOptions(new String[]{"停车", "自主", "远程遥控", "近程遥控"});
        short vehicleSpeed = getRandomShort(-800, 800);
        String batteryVoltage = getRandomInt(150, 400) + "";
        String batterySoc = getRandomInt(0, 100) + "";
        int batteryCurrent = getRandomInt(-600, 600);
        int mileage = getRandomInt(0, 65000);
        String batteryStatus = getRandomFromOptions(new String[]{"正常工作", "快速充电"});
        String batteryAlarm = getRandomFromOptions(new String[]{"正常", "1级报警", "2级报警", "3级报警"});
        int engineWaterTemp = getRandomInt(-50, 200);
        int generatorControllerTemp = getRandomInt(-50, 200);
        int generatorTemp = getRandomInt(-50, 200);
        int generatorDCVoltage = getRandomInt(0, 500);
        String generatorFault = getRandomFromOptions(new String[]{"正常", "故障"});
        String generatorStatusFault = getRandomFromOptions(new String[]{"正常", "故障"});
        String generatorCommunicationFault = getRandomFromOptions(new String[]{"正常", "故障"});
        String engineCommunicationFault = getRandomFromOptions(new String[]{"正常", "故障"});
        String apuCommunicationAnomaly = getRandomFromOptions(new String[]{"正常", "异常"});
        String engineTempAnomaly = getRandomFromOptions(new String[]{"正常", "异常"});
        String engineOilPressureAnomaly = getRandomFromOptions(new String[]{"正常", "异常"});

        return new VehicleData(targetCode, domain, pitchAngle, rollAngle, headingAngle, longitude, latitude, trackingDeviation, realTimeSpeed, parkingState,
                gear, emergencyBrakeState, drivingMode, vehicleSpeed, batteryVoltage, batterySoc, batteryCurrent, mileage, batteryStatus, batteryAlarm, engineWaterTemp,
                generatorControllerTemp, generatorTemp, generatorDCVoltage, generatorFault, generatorStatusFault, generatorCommunicationFault, engineCommunicationFault,
                apuCommunicationAnomaly, engineTempAnomaly, engineOilPressureAnomaly);

    }
}
