package com.gy.show.util;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class BubbleUtil {

    /**
     * 冒泡排序，返回字符串
     * @param arr
     * @return
     */

    public static String bubble(int[] arr){
        int len =arr.length;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i <len-1 ; i++) {
            for (int j = 0; j <len-i-1 ; j++) {
                if(arr[j]>arr[j+1]){
                    int tmp=0;
                    tmp=arr[j];
                    arr[j]=arr[j+1];
                    arr[j+1]=tmp;
                }
            }
        }
        for (int k = 0; k <len ; k++) {
            sb.append(arr[k]);
            if(k!=len-1){
                sb.append(",");
            }
        }
        return sb.toString();
    }

    /**
     * 字符串数值转Integer
     * @param arr
     * @return
     */
    public static int[] stringToInt(String[] arr){
        int[] array=new int[arr.length];
        for (int i = 0; i <arr.length; i++) {
            array[i]= Integer.parseInt(arr[i]);
        }
        return array;
    }

    /**
     * 将字符串写在文件中
     *
     */

    public static void writeDateToFile(String str){
        File writeFile;
        BufferedWriter bw;
        boolean append=true;
        String path="D:\\point.txt";
        writeFile = new File(path);
        if(writeFile.exists()==false){
            try {
                writeFile.createNewFile();
                writeFile=new File(path);
            }catch (IOException e) {
                e.printStackTrace();
            }
        }else {
            writeFile.delete();
            try {
                writeFile.createNewFile();
                writeFile=new File(path);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            FileWriter fileWriter = new FileWriter(writeFile);
            bw=new BufferedWriter(fileWriter);
            fileWriter.write(str);
            fileWriter.flush();
            fileWriter.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
