package com.gy.show.util;

import com.gy.show.common.Const;

import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class StringUtil {

    /*  高字节放高位处理 */
    public static String byte2HexFX(byte[] bytes){
        byte[] newBytes = getBytesConvert(bytes);
        StringBuilder stringBuilder = new StringBuilder();
        String temp;
        for (byte newByte : newBytes) {
            temp = Integer.toHexString(newByte & 0xFF);
            if (temp.length() == 1) {
                stringBuilder.append("0");
            }
            stringBuilder.append(temp);
        }
        return stringBuilder.toString();
    }

    /* 字节顺序 */
    public static byte[] getBytesConvert(byte[] bytes) {
        byte[] newBytes = new byte[bytes.length];
        for (int i = 0; i < bytes.length; i++) {
            newBytes[bytes.length - 1 - i ] = bytes[i];
        }
        return newBytes;
    }

    /*  高字节放低位处理（正常处理） */
    public static String byte2HexA(byte[] bytes){
        StringBuilder stringBuilder = new StringBuilder();
        String temp;
        for (byte newByte : bytes) {
            temp = Integer.toHexString(newByte & 0xFF);
            if (temp.length() == 1) {
                stringBuilder.append("0");
            }
            stringBuilder.append(temp);
        }
        return stringBuilder.toString();
    }

    /* byte 转 long */
    public static Long byteArray2Long(byte[] bytes)throws Exception{
        ByteBuffer buffer = ByteBuffer.allocate(8);
        buffer.put(bytes,0,bytes.length);
        buffer.flip();
        return buffer.getLong();
    }

    /* byte4字节 转 long */
    public static Long byteArray4Length2Long(byte[] bytes){
        return ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN).getInt()& 0xFFFFFFFFL;
    }

    /* long 转 byte */
    public static byte[] long2ByteArray(Long x){
        ByteBuffer buffer = ByteBuffer.allocate(8);
        buffer.putLong(0,x);
        return buffer.array();
    }

    /* byte 转 int */
    public static Integer byte2Int(byte[] bytes){
        return bytes[3]<<24 | (bytes[2]&0xff)<<16 | (bytes[1]&0xff)<<8 | (bytes[0]&0xff);
    }

    /* byte 转 short */
    public static Short byte2Short(byte[] bytes){
        short s0 = (short)(bytes[1] & 0xff);
        short s1 = (short)(bytes[0] & 0xff);
        s0 <<= 8;
        return  (short)(s1 | s0);
    }

    /* byte 转 short */
    public static long byte2DoubleBM(byte[] bytes){
        // 高位不动  然后取反 在 加1
        for (byte b : bytes){
            System.out.println(b);
        }
        return 1;
    }

    /* byte 转 int */
    public static byte[] int2byte(int number){
        byte[] bytes = new byte[4];
        bytes[3] = (byte)number;
        bytes[2] = (byte)((number >> 8)  & 0xFF);
        bytes[1] = (byte)((number >> 16) & 0xFF);
        bytes[0] = (byte)((number >> 24) & 0xFF);
        return bytes;
    }



    /* byte 定长 */
    public static byte[] intToByteArray(int number,int length){
        byte[] bytes = new byte[length];
        for (int i = 0; i < length; i++) {
            bytes[length - i - 1] = (byte)((number >> 8 * i) & 0xFF);
        }
        return bytes;
    }


    /* 16进制字符串 转 String 未测试*/
    public static String toStringHex(String s){
        byte[] baKeyword = new byte[s.length() /2];
        for (int i = 0; i < baKeyword.length; i++) {
            try {
                baKeyword[i] = (byte)(0xff & Integer.parseInt(s.substring(i*2, i*2+2),16));
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        try {
            s = new String(baKeyword,"unicode");
        }catch (Exception e1){
            e1.printStackTrace();
        }
        return s;
    }

    /* 字符串转16进制字符串*/
    public static String string2HexString(String s){
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            int ch = s.charAt(i);
            String s1 = Integer.toHexString(ch);
            str.append(s1);
        }
        return str.toString();
    }



    /*16进制字符串转byte[]数组 */
    public static byte[] hexStringToByteArray(String s){
        int length = s.length();
        byte[] b = new byte[length/2];
        for (int i = 0; i < length; i+=2) {
            // 两位一组，表示一个字节，把这样表示的16进制字符串，还原成一个字节
            b[i/2] = (byte)((Character.digit(s.charAt(i),16) << 4) + Character.digit(s.charAt(i+1),16));
        }
        return b;
    }



    /* byte[] 转16进制字符串 */
    public static String bytesToHexString(byte[] array){
        StringBuilder sb = new StringBuilder(array.length);
        String sTemp;
        for (byte b : array) {
            sTemp = Integer.toHexString(0xff & b);
            if (sTemp.length() < 2)
                sb.append(0);
            sb.append(sTemp.toUpperCase());
        }
        return sb.toString();
    }

    /* 经纬度装换 */
    public static double num2JD(Long number){
        BigDecimal bigDecimal = new BigDecimal(number);
        return  bigDecimal.multiply(new BigDecimal(Const.JWD_JD)).divide(new BigDecimal(numCF(Const.JWD_CF)))
                .setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /* 次方计算*/
    private static Double numCF(int n){
        int num = 1;
        double d = 2;
        while (n > num){
            d = d*2;
            n -= 1;
        }
        return d;
    }

    /* String 转定长 byte */
    public static List<Integer> stringList2IntList(List<String> stringList){
        List<Integer> integerList = new ArrayList<>();
        for (String s : stringList){
            integerList.add(Integer.parseInt(s));
        }
        return integerList;
    }
    /* String 转定长 byte */
    public static byte[] string2ByteLength(String str, Integer length){
        byte[] bytes = new byte[length];
        byte[] byteStr = str.getBytes();
        System.arraycopy(byteStr,0,bytes,0,byteStr.length);
        return bytes;
    }

    /* 字符串转定长的byte */
    public static byte[] stringChar2ByteLength(String str, Integer length){
        byte[] bytes = new byte[length];
        char[] c = str.toCharArray();
        for (int i = 0; i < c.length; i++) {
            bytes[i] = (byte) c[i];
        }
        return bytes;
    }

    public static String upperFirstCase(String str){
        char[] chars = str.toCharArray();
        chars[0] -= 32;
        return new String(chars);
    }

    /**
     * 将下划线字符串转换为驼峰字符串
     *
     * @param str 需要转换的下划线字符串
     * @return 转换后的驼峰字符串
     */
    public static String underscoreToCamel(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        StringBuilder sb = new StringBuilder();
        boolean nextUpperCase = false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    sb.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    sb.append(Character.toLowerCase(c));
                }
            }
        }
        return sb.toString();
    }


    /* byte 转 int */
    public static int byte2int(byte[] arr){
        int a = 0;
        a = a|(arr[0] & 0xff) << 0;
        a = a|(arr[1] & 0xff) << 8;
        a = a|(arr[2] & 0xff) << 16;
        a = a|(arr[3] & 0xff) << 24;
        return a;
    }

    /* byte8字节 转 long */
    public static Long byteArray8Length2Long(byte[] bytes){
        ByteBuffer byteBuffer = ByteBuffer.wrap(bytes);
        return byteBuffer.getLong();
    }


    public static void main(String[] args) {
//        LocalDateTime a = LocalDateTime.now();
//        LocalDateTime b = a.plusMinutes(-1);
//        System.out.println(b.isBefore(a));
    }

//    String[] GJ = new String[]{
//        "HAIYANG-1B;1 31113U 07010A   23250.75249261  .00000118  00000+0  61241-4 0  9992;2 31113  98.2977 260.7846 0013059 331.7519  28.2955 14.30463895856455",
//            "","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//            "","","","","","","","","","",
//    };

}
