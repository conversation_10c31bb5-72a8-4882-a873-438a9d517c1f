package com.gy.show.util;

import com.alibaba.fastjson.JSON;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.ws.AbstractWsServer;
import lombok.extern.slf4j.Slf4j;
import org.hipparchus.ode.ODEIntegrator;
import org.orekit.bodies.GeodeticPoint;
import org.orekit.bodies.OneAxisEllipsoid;
import org.orekit.data.DataContext;
import org.orekit.data.DataProvidersManager;
import org.orekit.data.ZipJarCrawler;
import org.orekit.forces.gravity.HolmesFeatherstoneAttractionModel;
import org.orekit.forces.gravity.potential.GravityFieldFactory;
import org.orekit.forces.gravity.potential.NormalizedSphericalHarmonicsProvider;
import org.orekit.frames.Frame;
import org.orekit.frames.FramesFactory;
import org.orekit.orbits.Orbit;
import org.orekit.orbits.OrbitType;
import org.orekit.propagation.SpacecraftState;
import org.orekit.propagation.analytical.tle.TLE;
import org.orekit.propagation.analytical.tle.TLEPropagator;
import org.orekit.propagation.conversion.DormandPrince853IntegratorBuilder;
import org.orekit.propagation.numerical.NumericalPropagator;
import org.orekit.time.AbsoluteDate;
import org.orekit.time.TimeScale;
import org.orekit.time.TimeScalesFactory;
import org.orekit.utils.Constants;
import org.orekit.utils.IERSConventions;
import org.orekit.utils.PVCoordinates;
import org.orekit.utils.TimeStampedPVCoordinates;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SatelliteOrbitCalculator {

    private static OneAxisEllipsoid earth;
    private static DataProvidersManager manager;

//    static {
//        try {
//            // 初始化Orekit库
//            URL resource = SatelliteOrbitCalculator.class.getResource("/lib/Orekit-12.1.1.zip");
//            File orekitData = new File(resource.toURI());
//            manager = DataContext.getDefault().getDataProvidersManager();
//            manager.addProvider(new ZipJarCrawler(orekitData));
//
//            // 创建地球模型
//            Frame itrf = FramesFactory.getITRF(IERSConventions.IERS_2010, true);
//            earth = new OneAxisEllipsoid(Constants.WGS84_EARTH_EQUATORIAL_RADIUS,
//                    Constants.WGS84_EARTH_FLATTENING, itrf);
//        } catch (Exception e) {
//            log.error("Orekit初始化异常: ", e);
//        }
//    }

    private static TLE createTle(String filePath) {
        List<String> tleLines = readTLEFile(filePath);
        if (tleLines.size() < 2) {
            log.error("TLE文件格式不正确");
            throw new ServiceException("TLE文件格式不正确");
        }

        // 定义TLE数据
        String line1 = tleLines.get(0);
        String line2 = tleLines.get(1);
        return new TLE(line1, line2);
    }

    public static List<String> readTLEFile(String filePath) {
        List<String> tleLines = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    tleLines.add(line.trim());
                }
            }
        } catch (IOException e) {
            log.error("读取文件失败", e);
        }
        return tleLines;
    }

    /**
     * 计算轨道
     * @param filePath
     * @param duration
     * @return
     */
    public static List<double[]> calculateOrbit(String filePath, double duration) {
        List<double[]> trackPoints = new ArrayList<>();
        try {
            // 创建TLE传播器
            TLE tle = createTle(filePath);
            TLEPropagator propagator = TLEPropagator.selectExtrapolator(tle);

            // 计算轨道周期
            double mu = Constants.IERS2010_EARTH_MU;
            Orbit initialOrbit = propagator.getInitialState().getOrbit();
            double semiMajorAxis = initialOrbit.getA();
            double orbitPeriod = 2 * Math.PI * Math.sqrt(Math.pow(semiMajorAxis, 3) / mu);

            AbsoluteDate startDate = getCurrentDate();
            AbsoluteDate stepDate = startDate;
            AbsoluteDate endDate = startDate.shiftedBy(orbitPeriod);

            while (stepDate.compareTo(endDate) <= 0) {
                PVCoordinates pvCoordinates = propagator.getPVCoordinates(stepDate, FramesFactory.getEME2000());
                GeodeticPoint geoPoint = earth.transform(pvCoordinates.getPosition(), FramesFactory.getEME2000(), stepDate);

                double longitude = Math.toDegrees(geoPoint.getLongitude());
                double latitude = Math.toDegrees(geoPoint.getLatitude());
                double altitude = geoPoint.getAltitude();

                trackPoints.add(new double[]{longitude, latitude, altitude});

                stepDate = stepDate.shiftedBy(60); // 每分钟
            }

        } catch (Exception e) {
            log.error("轨道计算异常", e);
        }

        return trackPoints;
    }

    public static class SatellitePropagator {
        private boolean running;
        private final String filePath;
        private AbsoluteDate startDate;
        private final double duration;
        private final String equipmentId;

        private AbstractWsServer wsServer;

        NumericalPropagator numericalPropagator;

        public SatellitePropagator(String filePath, double duration, AbstractWsServer wsServer, String equipmentId) {
            this.filePath = filePath;
            this.startDate = getCurrentDate();
            this.duration = duration;
            this.running = true;
            this.wsServer = wsServer;
            this.equipmentId = equipmentId;
            initNumericalForecast();
        }

        public void initNumericalForecast() {
            if (numericalPropagator == null) {

                // 读取TLE文件
                TLE tle = createTle(filePath);

                // 创建TLE传播器
                TLEPropagator propagator = TLEPropagator.selectExtrapolator(tle);

                // 数值预报设置
                double minStep = 0.001;
                double maxStep = 300;
                double positionTolerance = 10.0;
                ODEIntegrator integrator = new DormandPrince853IntegratorBuilder(minStep, maxStep, positionTolerance)
                        .buildIntegrator(propagator.getInitialState().getOrbit(), OrbitType.KEPLERIAN);
                numericalPropagator = new NumericalPropagator(integrator);
                numericalPropagator.setInitialState(new SpacecraftState(propagator.getInitialState().getOrbit(), 1000));

                // 添加重力场模型
                NormalizedSphericalHarmonicsProvider nshp = GravityFieldFactory.getNormalizedProvider(2, 0);
                HolmesFeatherstoneAttractionModel gravityField = new HolmesFeatherstoneAttractionModel(earth.getBodyFrame(), nshp);
                numericalPropagator.addForceModel(gravityField);
            } else {
                log.warn("当前卫星已初始化完成");
            }
        }

        public Map<String, Object> getNumericalForecastResult(AbsoluteDate stepDate) {
            TimeStampedPVCoordinates pvCoordinates = numericalPropagator.getPVCoordinates(stepDate, FramesFactory.getEME2000());
            GeodeticPoint geoPoint = earth.transform(pvCoordinates.getPosition(), FramesFactory.getEME2000(), stepDate);

            Map<String, Object> result = new HashMap<>();
            result.put("time", stepDate.toString());
            result.put("longitude", Math.toDegrees(geoPoint.getLongitude()));
            result.put("latitude", Math.toDegrees(geoPoint.getLatitude()));
            result.put("altitude", geoPoint.getAltitude());
            result.put("equipmentId", equipmentId);

            return result;
        }

        public List<double[]> numericalForecast() {
            List<double[]> trackPoints = new ArrayList<>();
            try {
                initNumericalForecast();

                // 进行预报，每分钟一次
                AbsoluteDate stepDate = startDate;
                AbsoluteDate endDate = startDate.shiftedBy(duration);

                while (stepDate.compareTo(endDate) <= 0 & running) {
                    TimeStampedPVCoordinates pvCoordinates = numericalPropagator.getPVCoordinates(stepDate, FramesFactory.getEME2000());
                    GeodeticPoint geoPoint = earth.transform(pvCoordinates.getPosition(), FramesFactory.getEME2000(), stepDate);

                    log.info("时间（UTC）: {}", stepDate);
                    log.info("经度: {}", Math.toDegrees(geoPoint.getLongitude()));
                    log.info("纬度: {}", Math.toDegrees(geoPoint.getLatitude()));
                    log.info("高度: {}", geoPoint.getAltitude());

                    stepDate = stepDate.shiftedBy(1); // 每秒
                    sendMessage(stepDate, Math.toDegrees(geoPoint.getLongitude()), Math.toDegrees(geoPoint.getLatitude()), geoPoint.getAltitude());
                    Thread.sleep(1000);
                }
                log.info("当前卫星预报已结束推送");
            } catch (Exception e) {
                log.error("Orekit异常: ", e);
            }

            return trackPoints;
        }

        private void sendMessage(AbsoluteDate stepDate, double longitude, double latitude, double altitude) {
            Map<String, Object> msg = new HashMap<>();
            msg.put("time", stepDate.toString());
            msg.put("longitude", longitude);
            msg.put("latitude", latitude);
            msg.put("altitude", altitude);
            msg.put("equipmentId", equipmentId);

            WsMessageDTO wsMessageDTO = new WsMessageDTO();
            wsMessageDTO.setData(JSON.toJSONString(msg));
            wsMessageDTO.setType(WebSocketTypeEnum.SATELLITE.getCode());

            wsServer.sendAll(JSON.toJSONString(wsMessageDTO));
        }

        public void stopForecast() {
            running = false;
        }
    }

    private static AbsoluteDate getCurrentDate() {
        ZonedDateTime nowBeijing = ZonedDateTime.now(ZoneId.systemDefault());

        LocalDateTime localDateTime = nowBeijing.toLocalDateTime();
        return localDateTime2AbsoluteDate(localDateTime);
    }

    public static AbsoluteDate localDateTime2AbsoluteDate(LocalDateTime localDateTime) {
        int year = localDateTime.getYear();
        int month = localDateTime.getMonthValue();
        int day = localDateTime.getDayOfMonth();
        int hour = localDateTime.getHour();
        int minute = localDateTime.getMinute();
        double second = localDateTime.getSecond() + localDateTime.getNano() / 1.0e9;

        // 获取UTC时间尺度
        TimeScale utc = TimeScalesFactory.getUTC();

        // 创建AbsoluteDate对象
        return new AbsoluteDate(year, month, day, hour, minute, second, utc);
    }

}

