package com.gy.show.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dto.TaskWithTargets;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * JSON转换工具类，处理缓存数据的类型转换问题
 */
@Slf4j
public class JsonConvertUtil {

    /**
     * 安全地从Map中获取TaskWithTargets列表
     */
    @SuppressWarnings("unchecked")
    public static List<TaskWithTargets> getTaskWithTargetsList(Map<String, Object> data) {
        Object targetsObj = data.get("targets");
        if (targetsObj == null) {
            return new ArrayList<>();
        }

        try {
            // 如果已经是正确的类型，直接返回
            if (targetsObj instanceof List) {
                List<?> list = (List<?>) targetsObj;
                if (!list.isEmpty() && list.get(0) instanceof TaskWithTargets) {
                    return (List<TaskWithTargets>) targetsObj;
                }
            }

            // 如果是从JSON反序列化的Map/List结构，需要重新转换
            String jsonString = JSON.toJSONString(targetsObj);
            return JSON.parseObject(jsonString, new TypeReference<List<TaskWithTargets>>() {});

        } catch (Exception e) {
            log.error("转换TaskWithTargets列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 安全地从Map中获取DataEquipmentOccupancy列表
     */
    @SuppressWarnings("unchecked")
    public static List<DataEquipmentOccupancy> getDataEquipmentOccupancyList(Map<String, Object> data) {
        Object equipmentsObj = data.get("equipments");
        if (equipmentsObj == null) {
            return new ArrayList<>();
        }

        try {
            // 如果已经是正确的类型，直接返回
            if (equipmentsObj instanceof List) {
                List<?> list = (List<?>) equipmentsObj;
                if (!list.isEmpty() && list.get(0) instanceof DataEquipmentOccupancy) {
                    return (List<DataEquipmentOccupancy>) equipmentsObj;
                }
            }

            // 如果是从JSON反序列化的Map/List结构，需要重新转换
            String jsonString = JSON.toJSONString(equipmentsObj);
            return JSON.parseObject(jsonString, new TypeReference<List<DataEquipmentOccupancy>>() {});

        } catch (Exception e) {
            log.error("转换DataEquipmentOccupancy列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 安全地将对象转换为指定类型
     */
    public static <T> T convertObject(Object obj, Class<T> clazz) {
        if (obj == null) {
            return null;
        }

        try {
            // 如果已经是正确的类型，直接返回
            if (clazz.isInstance(obj)) {
                return clazz.cast(obj);
            }

            // 通过JSON转换
            String jsonString = JSON.toJSONString(obj);
            return JSON.parseObject(jsonString, clazz);

        } catch (Exception e) {
            log.error("转换对象失败，目标类型: {}", clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 安全地将对象转换为指定类型的列表
     */
    public static <T> List<T> convertList(Object obj, Class<T> clazz) {
        if (obj == null) {
            return new ArrayList<>();
        }

        try {
            // 如果已经是正确的类型，直接返回
            if (obj instanceof List) {
                List<?> list = (List<?>) obj;
                if (!list.isEmpty() && clazz.isInstance(list.get(0))) {
                    @SuppressWarnings("unchecked")
                    List<T> result = (List<T>) obj;
                    return result;
                }
            }

            // 通过JSON转换
            String jsonString = JSON.toJSONString(obj);
            return JSON.parseArray(jsonString, clazz);

        } catch (Exception e) {
            log.error("转换列表失败，目标类型: {}", clazz.getSimpleName(), e);
            return new ArrayList<>();
        }
    }
}
