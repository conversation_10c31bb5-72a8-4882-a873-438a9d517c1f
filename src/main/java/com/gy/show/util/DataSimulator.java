package com.gy.show.util;

import lombok.Data;

import java.util.Random;

public class DataSimulator {

    private static Random random = new Random();

    // 随机生成char
    private static char getRandomChar() {
        return (char) (random.nextInt(26) + 'A');
    }

    // 随机生成范围内的int32
    private static int getRandomInt32(int min, int max) {
        return random.nextInt(max - min + 1) + min;
    }

    // 随机生成范围内的uint16
    private static int getRandomUInt16(int max) {
        return random.nextInt(max + 1);
    }

    // 随机生成范围内的int16
    private static short getRandomInt16(int min, int max) {
        return (short) (random.nextInt(max - min + 1) + min);
    }

    @Data
    public static class TargetData {
        private char targetCode;        // 目标代号
        private char domain;            // 所属域
        private int longitude;          // 经度
        private int latitude;           // 纬度
        private int airspeed;           // 空速
        private int groundSpeed;        // 地速
        private int altitude;           // 海拔高度
        private short pitchAngle;       // 俯仰角
        private short rollAngle;        // 横滚角
        private int headingAngle;       // 航向角

        public TargetData(char targetCode, char domain, int longitude, int latitude, int airspeed, int groundSpeed, int altitude, short pitchAngle, short rollAngle, int headingAngle) {
            this.targetCode = targetCode;
            this.domain = domain;
            this.longitude = longitude;
            this.latitude = latitude;
            this.airspeed = airspeed;
            this.groundSpeed = groundSpeed;
            this.altitude = altitude;
            this.pitchAngle = pitchAngle;
            this.rollAngle = rollAngle;
            this.headingAngle = headingAngle;
        }

        @Override
        public String toString() {
            return "TargetData{" +
                    "targetCode=" + targetCode +
                    ", domain=" + domain +
                    ", longitude=" + longitude +
                    ", latitude=" + latitude +
                    ", airspeed=" + airspeed +
                    ", groundSpeed=" + groundSpeed +
                    ", altitude=" + altitude +
                    ", pitchAngle=" + pitchAngle +
                    ", rollAngle=" + rollAngle +
                    ", headingAngle=" + headingAngle +
                    '}';
        }
    }

    // 模拟生成数据
    public static TargetData generateRandomTargetData() {
        char targetCode = getRandomChar();               // 目标代号
        char domain = getRandomChar();                   // 所属域
        int longitude = getRandomInt32(-647999999, 648000000);  // 经度
        int latitude = getRandomInt32(-324000000, 324000000);   // 纬度
        int airspeed = getRandomUInt16(600);                 // 空速
        int groundSpeed = getRandomUInt16(600);              // 地速
        int altitude = getRandomUInt16(65535);                 // 海拔高度
        short pitchAngle = getRandomInt16(-90, 90);        // 俯仰角
        short rollAngle = getRandomInt16(-180, 180);       // 横滚角
        int headingAngle = getRandomUInt16(360);             // 航向角

        return new TargetData(targetCode, domain, longitude, latitude, airspeed, groundSpeed, altitude, pitchAngle, rollAngle, headingAngle);
    }

    public static void main(String[] args) {
        // 生成并输出随机的目标数据
        TargetData targetData = generateRandomTargetData();
        System.out.println(targetData);
    }
}


