package com.gy.show.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.gy.show.constants.CacheConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;

import static com.gy.show.constants.CacheConstant.NODE_SELECT;
import static com.gy.show.constants.CacheConstant.TERMINAL_MODE_STATUS;

@Slf4j
public class NodeSelection {

    private static final Integer LockDurParam = 10;

    /**
     * 终端
     */
    @Data
    public static class Terminal {
        /**
         * 终端序号
         */
        int id;

        /**
         * 纬度、经度、高度
         */
        double[] pos; // {latitude, longitude, altitude}

        /**
         * 对应 抗干扰状态帧 --> 有用信号有无
         */
        int rss_en;

        /**
         * 对应 抗干扰状态帧 --> 有用信号强度 dB
         */
        double rss;

        /**
         *  对应 建链申请数据帧 --> 建链申请
         */
        int req;

        /**
         *  对应 抗干扰状态帧 --> 抗干扰手段决策结果
         */
        int antj;

        /**
         *  对应 抗干扰状态帧 --> 抗干扰执行情况
         */
        int antjen;

        /**
         * 当前终端状态
         */
        int status;

        /**
         * 当前终端体制
         */
        Map<String, Object> mode;

        /**
         *  对应 抗干扰状态帧 --> 干扰信号有无
         */
        int grxhyw;

        public Terminal(int id, double[] pos, int rss_en, double rss, int req, int antj, int antjen, int status, Map<String, Object> mode, int grxhyw) {
            this.id = id;
            this.pos = pos;
            this.rss_en = rss_en;
            this.rss = rss;
            this.req = req;
            this.antj = antj;
            this.antjen = antjen;
            this.status = status;
            this.mode = mode;
            this.grxhyw = grxhyw;
        }
    }

    /**
     * 节点（站）
     */
    @Data
    public static class Node {
        /**
         * 纬度、经度、高度
         */
        double[] pos; // {latitude, longitude, altitude}

        /**
         * 节点参数，节点ID，测控距离，是否在使用 0 已使用 1 未使用
         */
        long[] para;  // {id, maxDistance, AvailEn}

        public Node(double[] pos, long[] para) {
            this.pos = pos;
            this.para = para;
        }
    }

    @Data
    public static class Output implements Serializable {
        /**
         * 对应抗干扰控制帧 结果使能 如果为1才进行抗干扰下发
         */
        int SlctEn;

        /**
         * 终端ID
         */
        int TarID;

        /**
         * 需要接入节点ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        String NodeID;

        /**
         * 需退网的节点
         */
        String LastNode;

        /**
         * 链路切换方式
         */
        int AccHandEn;

        /**
         * 输出至前端系统弹性抗干扰决策结果-->开启状态
         */
        int SysAntj;

        /**
         * 需要切换的体制编号
         */
        int TerMode;

        public Output(int SlctEn, int TarID, String NodeID, String LastNode, int AccHandEn, int SysAntj, int TerMode) {
            this.SlctEn = SlctEn;
            this.TarID = TarID;
            this.NodeID = NodeID;
            this.LastNode = LastNode;
            this.AccHandEn = AccHandEn;
            this.SysAntj = SysAntj;
            this.TerMode = TerMode;
        }
    }

    public static double[] func_lla2ecef(double[] lla) {
        double a = 6378137.0; // Semi-major axis
        double f = 1.0 / 298.257223563; // Flattening

        double wd = Math.toRadians(lla[0]);
        double jd = Math.toRadians(lla[1]);
        double h = lla[2];

        double e2 = f * (2 - f);
        double N = a / Math.sqrt(1 - e2 * Math.sin(wd) * Math.sin(wd));

        double x = (N + h) * Math.cos(wd) * Math.cos(jd);
        double y = (N + h) * Math.cos(wd) * Math.sin(jd);
        double z = (N * (1 - e2) + h) * Math.sin(wd);

        return new double[]{x, y, z};
    }

    /**
     * 随遇接入、切换
     * @param InTer 实时上报的终端信息
     * @param InNode 实时上报的节点信息
     * @param InSchT 已使用的终端序号数组
     * @param InCurN 当前正在使用的站ID，初始值为0
     * @param ThrRSS 信号强度门限值
     * @param ebn0
     * @return
     */
    public static Output func_syjr(Terminal InTer, Node[] InNode, int[] InSchT, long InCurN, double ThrRSS, double ebn0) {

        /**
         * mode使用示例
         *  Map<String, Object> mode = InTer.mode;
         *  Integer KP2WaveLockYk = (Integer) mode.get("KP2WaveLockYk");
         */
        log.info("节点切换输入参数InTer：{}，InNode：{}，InSchT：{}，InCurN：{}", InTer, InNode, InSchT, InCurN);

        int UnLockState = 0;
        Map<String, Object> mode = InTer.mode;

        Map<String, Object> data = (Map<String, Object>) mode.get("data");

        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String key = entry.getKey();
            if(entry.getKey().equals("reverse") || entry.getKey().equals("KP2FrameLockYk")) continue;
            if (Integer.parseInt(entry.getValue().toString()) == 0 && getSingleFieldDuration(key) > LockDurParam){
//                UnLockState = 0;
                UnLockState = 1;
                break;
            } else {
                UnLockState = 0;
            }
        }


        /*
         * 需要添加:
         * 1、需要对各个Lock信号进行多次比较，若连续失锁xx次，判定为链路断开
         * 2、抗干扰帧的干扰信号有无,jamen
         */

        // 获取当前终端体制
        long CurNode = getTerCurrentNode(InTer.id);


        double[] TargetLoc = func_lla2ecef(InTer.pos);
        double[] InCurNLoclla = new double[3];
        double[] InCurNLocecef = new double[3];
        double   MaxDisInCurN = 999999999;
        double   InNTDis;
        log.info("接入切换模块：当前预计接入节点 {},当前接入节点:{}",InCurN,CurNode);
        ///////判断是否需要以及需要进行哪个决策
        boolean EnAccDis;
        boolean EnHandDis;
        if (InCurN == 0) {
            EnHandDis = false;
            EnAccDis = true;
            if (EnAccDis) {
                log.info("接入切换模块：需接入 EnAccDis == True");
            }
        }
        else {
            EnAccDis = false;
            for (Node node : InNode) {
                if (node.getPara()[0] == CurNode) {
                    InCurNLoclla = node.getPos();
                    MaxDisInCurN = node.getPara()[1];
                }
            }
            InCurNLocecef = func_lla2ecef(InCurNLoclla);
            InNTDis = Math.sqrt(
                    Math.pow(InCurNLocecef[0] - TargetLoc[0], 2) +
                            Math.pow(InCurNLocecef[1] - TargetLoc[1], 2) +
                            Math.pow(InCurNLocecef[2] - TargetLoc[2], 2)
            );
            log.info("接入切换模块：当前接入节点距离 {}",InNTDis);
            if (InNTDis >= MaxDisInCurN) {
                EnHandDis = true;
            }
            else {
                EnHandDis = false;
            }
        }
        // access
        boolean EnAccTer = Arrays.stream(InSchT).noneMatch(id -> id == InTer.id);//终端未在已使用列表中
        boolean EnAccReq = (InTer.req == 1 && InTer.antjen == 0);//终端发起接入请求，抗干扰未执行状态下终端req接入
        boolean EnAccSta = (InTer.status == 2);//终端处于状态2，即拓扑图中0号接收到，但该终端未上报；目前该种状态无法进入本决策
        //boolean EnAcc = EnAccDis || EnAccTer || EnAccReq || EnAccSta;
        boolean EnAcc = EnAccReq;
        log.info("接入切换模块：接入 EnAccDis = {}，EnAccTer = {}，EnAccReq = {}，EnAccSta = {}，InTer.req = {}， InTer.antjen = {}",EnAccDis,EnAccTer,EnAccReq,EnAccSta,InTer.req, InTer.antjen);
        // handover
//        boolean EnHandEbn0 = (ebn0 <= ThrRSS && InTer.status == 1 && InTer.antjen == 0);//正常工作中的终端，不执行抗干扰下，EbN0低于门限值需要进行切换
        boolean EnHandEbn0 = (ebn0 <= -10 && InTer.status == 1 && InTer.antjen == 0);//正常工作中的终端，不执行抗干扰下，EbN0低于门限值需要进行切换
        boolean EnHandReq = (InTer.req == 2 && InTer.antjen == 0) || (InTer.antj == 1);//终端请求做切换调度，1、不执行抗干扰下，req切换；2、抗干扰帧中请求切换
        boolean EnHandLock = (UnLockState == 1 && InTer.grxhyw == 0);//v，失锁，需要切换
        boolean EnHand = EnHandDis || EnHandEbn0 || EnHandReq;// || EnHandLock;
        log.info("接入切换模块：切换 EnHandDis = {}，EnHandEbn0 = {}，EnHandReq = {}，EnHandLock = {}",EnHandDis,EnHandEbn0,EnHandReq,EnHandLock);
        // antijamming
        boolean EnAntj = (InTer.antj == 5) && (InTer.antjen == 1) && InTer.status == 1;
        log.info("接入切换模块：接入切换状态 EnAcc = {}，EnHand = {}，EnAntj = {}",EnAcc,EnHand,EnAntj);

        long AccessNodeID = CurNode;
        int SlctEn = 0;
        long node_avail = 0;
        Integer TerID_Sp[] = {1, 2, 3, 4};//与HTCK站通道绑定的终端
        Long NodeId_Sp[] = {1834055782248001538L, 1834120494834802689L};//对应两个航天站的id ----- 需根据最终确定的id修改 --- 若id存在随机变化的话，可以通过输入得到

        int AccHandEn = 0;
        int SysAntj = 0;
        int TerMode = 0;//终端切换体制

        long LastNode = 0;

        //随遇接入未使用节点判断 0--使用，1--未使用
//        long node_avail_syjr[] = new long[InNode.length];
//        Arrays.fill(node_avail_syjr,1);

//        String TotalTerMode[] = new String[InSchT.length];
        long TotalTerNode[] = new long[InSchT.length];
        for (int i = 0; i < InSchT.length; i++) {
            TotalTerNode[i] = getTerCurrentNode(InSchT[i]);
        }



//        log.info("接入切换模块：terminalLoc -- {}; nodeLoc1 -- {}; nodeLoc2 -- {}",InTer.pos,InNode[1].pos,InNode[2].pos);

        if (EnAcc || EnHand || EnAntj) {
//            double[] TargetLoc = func_lla2ecef(InTer.pos);
            double[][] NodeLoc = new double[InNode.length][3];
            double[] TargetNodeDis = new double[InNode.length];

            for (int i = 0; i < InNode.length; i++) {
                NodeLoc[i] = func_lla2ecef(InNode[i].pos);
                TargetNodeDis[i] = Math.sqrt(
                        Math.pow(NodeLoc[i][0] - TargetLoc[0], 2) +
                                Math.pow(NodeLoc[i][1] - TargetLoc[1], 2) +
                                Math.pow(NodeLoc[i][2] - TargetLoc[2], 2)
                );
            }
            log.info("接入切换模块：distance -- {}",TargetNodeDis);


            List<Integer> AvailNodeIdx = new ArrayList<>();
            for (int i = 0; i < InNode.length; i++) {
                boolean ter_sp = Arrays.asList(TerID_Sp).contains(InTer.id);
                boolean node_sp = Arrays.asList(NodeId_Sp).contains(InNode[i].para[0]);
                if (ter_sp && node_sp) { // S、Ka航天站由于固定通道，对于1~4号终端随时可用
                    node_avail = 1;
                }
                else {   // 其余站根据使用情况判断
//                    boolean node_avail_syjr = Arrays.asList(TotalTerNode).contains(InNode[i].para[0]);
//                    if (!node_avail_syjr) { //false 随遇接入没有使用，根据预规划判断是否可使用
//                        node_avail = InNode[i].para[2];
//                    }
//                    else {   //随遇接入已经使用，该节点无法使用
//                        node_avail = 0;
//                    }
                    node_avail = 0;//测试，其余站不做接入切换
                }

//                if (node_avail == 1 && TargetNodeDis[i] <= InNode[i].para[1]) {
//                    AvailNodeIdx.add(i);
//                }
                if (node_avail == 1) {
                    if(EnAntj) {
                        AvailNodeIdx.add(i);//测试，抗干扰不做距离判断
                    }
                    else {
                        if (TargetNodeDis[i] <= InNode[i].para[1]) {
                            AvailNodeIdx.add(i);
                        }
                    }
                }
            }

            if (!EnAcc || EnHand || EnAntj) {
                AvailNodeIdx.removeIf(idx -> InNode[idx].para[0] == CurNode);
            }

            if (!AvailNodeIdx.isEmpty()) {
                AvailNodeIdx.sort(Comparator.comparingDouble(idx -> TargetNodeDis[idx])); // 改为升序
                AccessNodeID = InNode[AvailNodeIdx.get(0)].para[0];
//                SlctEn = 1; //todo 以下为测试使用，实际需要删除

                if (EnAntj) {
                    // 2025.06.05 ADD 开关
                    SlctEn = 1;
                }
                else if (EnAcc || EnHand) {
                    // 2025.06.05 ADD 开关
                    String open = RedisUtil.StringOps.get(NODE_SELECT);
                    if (open.equals("0")) {
                         SlctEn = 0;
                    }
                     else {
                        SlctEn = 1;
                     }
                }
                else {
                    SlctEn = 0;
                }

                if (EnAntj) {
                    // 系统级抗干扰
                    AccHandEn = 1;
                    SysAntj = 1;
                }
                else if (EnAcc) {
                    // 接入
                    AccHandEn = 3;
                }
                else if (EnHand) {
                    // 切换
                    AccHandEn = 2;
                }


                LastNode = CurNode;

                if (AccessNodeID == 1834055782248001538L) {   //Ka
                    switch (InTer.id) {
                        case 1:
                            TerMode = 5;
                            break;
                        case 2:
                            TerMode = 6;
                            break;
                        case 3:
                            TerMode = 7;
                            break;
                        case 4:
                            TerMode = 8;
                            break;
                        default:
                            TerMode = 0;
                    }
                }
                else if (AccessNodeID == 1834120494834802689L) {  //S
                    switch (InTer.id) {
                        case 1:
                            TerMode = 1;
                            break;
                        case 2:
                            TerMode = 2;
                            break;
                        case 3:
                            TerMode = 3;
                            break;
                        case 4:
                            TerMode = 4;
                            break;
                        default:
                            TerMode = 0;
                    }
                }
                else if (AccessNodeID == 1834121686784696321L) {  //UAV
                    TerMode = 9;
                }
                else if (AccessNodeID == 1838766928349425666L) {  //DD
                    TerMode = 10;
                }
                else {
                    TerMode = 0;
                }
            }

        }

        // todo last node id
        return new Output(SlctEn, InTer.id, AccessNodeID + "", LastNode + "", AccHandEn,SysAntj,TerMode);
//        return new Output(SlctEn, InTer.id, AccessNodeID, 0, AccHandEn,SysAntj);
    }

    public static long getTerCurrentNode(int terId) {
        long res = 0;
        String mode = RedisUtil.StringOps.get(CacheConstant.TERMINAL_CURRENT_MODE + terId);

        if (StringUtils.isNotBlank(mode)) {
            switch (mode) {
                case "1":  //S
                    res = 1834120494834802689L;
                    break;
                case "2":  //Ka
                    res = 1834055782248001538L;
                    break;
                case "3":  //UAV
                    res = 1834121686784696321L;
                    break;
                case "4":  //DD
                    res = 1838766928349425666L;
                    break;
                default:
                    res = -1;
                    break;
            }
        }
        return res;
    }

    public static String getTerCurrentCode(int terId) {
        String res = null;
        String mode = RedisUtil.StringOps.get(CacheConstant.TERMINAL_CURRENT_MODE + terId);

        if (StringUtils.isNotBlank(mode)) {
            switch (mode) {
                case "1":  //S
                    res = "_s";
                    break;
                case "2":  //Ka
                    res = "_ka";
                    break;
                case "3":  //UAV
                    res = "_uav";
                    break;
                case "4":  //DD
                    res = "_missile";
                    break;
                default:
                    break;
            }
        }
        return res;
    }

    /**
     * 获取当前字段对应值存在了多长时间
     * @param key
     * @return
     */
    private static long getSingleFieldDuration(String key) {
        Map<Object, Object> cacheMap = RedisUtil.HashOps.hGetAll(TERMINAL_MODE_STATUS + key);

        if (CollUtil.isNotEmpty(cacheMap)) {
//            LocalDateTime startTime = (LocalDateTime) cacheMap.get("startTime");
            String startTime =  cacheMap.get("startTime").toString();
            DateTime startTime1 = DateUtil.parse(startTime);
//            Duration duration = Duration.between(startTime, LocalDateTime.now());
            long between = DateUtil.between(startTime1,DateTime.now(), DateUnit.SECOND);
//            return  duration.getSeconds();
            return  between;
        } else {
            return 0;
        }
    }

//    public static void main(String[] args) {
//        Terminal InTer = new Terminal(3, new double[]{30.567998, 105.12856, 0}, -16, 0,5);
//
//        Node[] InNode = {
//                new Node(new double[]{30.89345, 105.56452, 100}, new int[]{123, 100, 1}),
//                new Node(new double[]{30.83345, 105.53452, 0}, new int[]{134, 500000, 1}),
//                new Node(new double[]{30.90345, 105.36452, 100}, new int[]{156, 1000000, 1}),
//                new Node(new double[]{30.85345, 105.58452, 0}, new int[]{434, 1000000, 0})
//        };
//
//        int[] InSchT = {3, 4, 5};
//        int InCurN = 0;
//        double ThrRSS = -20;
//
//        Output result = func_syjr(InTer, InNode, InSchT, InCurN, ThrRSS);
//
//        System.out.println("Out.SlctEn = " + result.SlctEn);
//        System.out.println("Out.TarID = " + result.TarID);
//        System.out.println("Out.NodeID = " + result.NodeID);
//        System.out.println("Out.AccHandEn = " + result.AccHandEn);
//        System.out.println("Out.SysAntj = " + result.SysAntj);
//    }


}
