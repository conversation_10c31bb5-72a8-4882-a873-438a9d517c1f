package com.gy.show.util;

import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.enums.RequirementSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量目标创建工具类
 */
@Slf4j
@Component
public class BatchRequirementCreator {

    /**
     * 根据批量创建配置生成包含多个目标的需求
     *
     * @param batchConfig 批量创建配置
     * @param targetTypeDataMap 按目标类型分组的数据映射
     * @return 包含多个目标的需求DTO
     */
    public RequirementInfoDTO createRequirementWithBatchTargets(
            BatchRequirementCreateDTO batchConfig,
            Map<String, TargetTypeData> targetTypeDataMap) {

        log.info("开始创建包含{}个目标的需求", batchConfig.getTargetCount());

        // 创建需求基本信息
        RequirementInfoDTO requirement = new RequirementInfoDTO();
        requirement.setRequirementName(batchConfig.getRequirementName());
        requirement.setRequirementType(batchConfig.getRequirementType());
        requirement.setImportance(batchConfig.getImportance());
        requirement.setStartTime(batchConfig.getStartTime());
        requirement.setEndTime(batchConfig.getEndTime());
        requirement.setRequirementComment(batchConfig.getRequirementComment());
        requirement.setSource(RequirementSourceEnum.MANUAL.getCode());
        requirement.setIsDeCompose(1); // 自动分解

        // 创建目标信息列表
        List<RequirementInfoDTO.TargetInfo> targetInfos = new ArrayList<>();
        int globalIndex = 1;

        // 按目标类型配置创建目标
        for (BatchRequirementCreateDTO.TargetTypeConfig typeConfig : batchConfig.getTargetTypeConfigs()) {
            TargetTypeData typeData = targetTypeDataMap.get(typeConfig.getDataType() + typeConfig.getTrackNamePrefix());

            for (int i = 0; i < typeConfig.getCount(); i++) {
                RequirementInfoDTO.TargetInfo targetInfo = createTargetInfo(
                    batchConfig,
                    typeConfig,
                    typeData.getTargetIds().get(i),
                    typeData.getTrackIds().get(i),
                    typeData.getGeneralIds().get(i),
                    globalIndex++
                );
                targetInfos.add(targetInfo);
            }
        }

        requirement.setTargetInfos(targetInfos);

        log.info("批量目标创建完成，共创建{}个目标", targetInfos.size());
        return requirement;
    }

    /**
     * 兼容旧版本的方法（保留向后兼容性）
     */
//    public RequirementInfoDTO createRequirementWithBatchTargets(
//            BatchRequirementCreateDTO batchConfig,
//            List<String> targetIds,
//            List<String> trackIds,
//            List<String> generalIds) {
//
//        // 验证数据
//        if (targetIds.size() != batchConfig.getTargetCount() ||
//            trackIds.size() != batchConfig.getTargetCount() ||
//            generalIds.size() != batchConfig.getTargetCount()) {
//            throw new IllegalArgumentException("目标数量、航迹数量和主表ID数量必须一致");
//        }
//
//        // 转换为新的数据结构
//        Map<Integer, TargetTypeData> targetTypeDataMap = new HashMap<>();
//
//        // 如果没有目标类型配置，使用默认配置
//        if (batchConfig.getTargetTypeConfigs() == null || batchConfig.getTargetTypeConfigs().isEmpty()) {
//            // 创建默认的目标类型配置（假设都是无人机）
//            BatchRequirementCreateDTO.TargetTypeConfig defaultConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
//            defaultConfig.setDataType(4); // 默认无人机
//            defaultConfig.setCount(batchConfig.getTargetCount());
//            defaultConfig.setTrackNamePrefix("默认航迹");
//
//            batchConfig.setTargetTypeConfigs(Arrays.asList(defaultConfig));
//        }
//
//        // 假设所有数据都属于第一个配置类型
//        BatchRequirementCreateDTO.TargetTypeConfig firstConfig = batchConfig.getTargetTypeConfigs().get(0);
//        TargetTypeData typeData = new TargetTypeData();
//        typeData.setTargetIds(targetIds);
//        typeData.setTrackIds(trackIds);
//        typeData.setGeneralIds(generalIds);
//
//        targetTypeDataMap.put(firstConfig.getDataType(), typeData);
//
//        return createRequirementWithBatchTargets(batchConfig, targetTypeDataMap);
//    }
    
    /**
     * 创建单个目标信息（新版本，支持按目标类型配置）
     */
    private RequirementInfoDTO.TargetInfo createTargetInfo(
            BatchRequirementCreateDTO batchConfig,
            BatchRequirementCreateDTO.TargetTypeConfig typeConfig,
            String targetId,
            String trackId,
            String generalId,
            int index) {

        // 创建目标信息
        RequirementInfoDTO.TargetInfo targetInfo = new RequirementInfoDTO.TargetInfo();
        targetInfo.setTargetId(targetId);
        targetInfo.setGeneralId(generalId);
        targetInfo.setTrackPresetId(trackId);

        // 设置航迹时间（优先使用目标类型配置，其次使用需求配置）
        LocalDateTime trackStartTime = batchConfig.getStartTime();
        if (typeConfig.getTrackConfig() != null && typeConfig.getTrackConfig().getTrackStartOffset() != null) {
            trackStartTime = trackStartTime.plusMinutes(typeConfig.getTrackConfig().getTrackStartOffset());
        }
        targetInfo.setTrackStartTime(trackStartTime);
        targetInfo.setTrackEndTime(trackStartTime.plusHours(1));

        // 创建任务列表（使用目标类型的任务配置）
        List<RequirementTaskDTO> tasks = createTasks(batchConfig, typeConfig, index);
        targetInfo.setTasks(tasks);

        return targetInfo;
    }

    /**
     * 创建单个目标信息（兼容旧版本）
     */
    private RequirementInfoDTO.TargetInfo createTargetInfo(
            BatchRequirementCreateDTO batchConfig,
            String targetId,
            String trackId,
            String generalId,
            int index) {

        // 使用默认的目标类型配置
        BatchRequirementCreateDTO.TargetTypeConfig defaultTypeConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        defaultTypeConfig.setDataType(4); // 默认无人机

        return createTargetInfo(batchConfig, defaultTypeConfig, targetId, trackId, generalId, index);
    }
    
    /**
     * 创建任务列表（新版本，支持按目标类型配置）
     */
    private List<RequirementTaskDTO> createTasks(
            BatchRequirementCreateDTO batchConfig,
            BatchRequirementCreateDTO.TargetTypeConfig typeConfig,
            int index) {
        List<RequirementTaskDTO> tasks = new ArrayList<>();

        if (typeConfig.getTaskConfig() == null || typeConfig.getTaskConfig().getTaskTypes() == null) {
            // 默认创建测控任务
            RequirementTaskDTO task = createDefaultTask(batchConfig, typeConfig, index);
            tasks.add(task);
        } else {
            // 根据目标类型配置创建任务
            RequirementTaskDTO task = new RequirementTaskDTO();
            task.setTaskName(getDataTypeName(typeConfig.getDataType()) + "任务-" + String.format("%04d", index));
            task.setTaskType(typeConfig.getTaskConfig().getTaskTypes());
            task.setRepeatType(typeConfig.getTaskConfig().getRepeatType());

            // 设置任务时间
            LocalDateTime taskStartTime = batchConfig.getStartTime();
            if (typeConfig.getTaskConfig().getTaskStartOffset() != null) {
                taskStartTime = taskStartTime.plusMinutes(typeConfig.getTaskConfig().getTaskStartOffset());
            }
            task.setStartTime(taskStartTime);

            LocalDateTime taskEndTime = taskStartTime;
            if (typeConfig.getTaskConfig().getTaskDuration() != null) {
                taskEndTime = taskStartTime.plusMinutes(typeConfig.getTaskConfig().getTaskDuration());
            } else {
                taskEndTime = taskStartTime.plusHours(1); // 默认1小时
            }
            task.setEndTime(taskEndTime);

            tasks.add(task);
        }

        return tasks;
    }

    /**
     * 创建任务列表（兼容旧版本）
     */
    private List<RequirementTaskDTO> createTasks(BatchRequirementCreateDTO batchConfig, int index) {
        // 使用默认的目标类型配置
        BatchRequirementCreateDTO.TargetTypeConfig defaultTypeConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        defaultTypeConfig.setDataType(4); // 默认无人机

        return createTasks(batchConfig, defaultTypeConfig, index);
    }

    /**
     * 创建默认任务（测控任务）- 新版本
     */
    private RequirementTaskDTO createDefaultTask(
            BatchRequirementCreateDTO batchConfig,
            BatchRequirementCreateDTO.TargetTypeConfig typeConfig,
            int index) {
        RequirementTaskDTO task = new RequirementTaskDTO();
        task.setTaskName(getDataTypeName(typeConfig.getDataType()) + "测控任务-" + String.format("%04d", index));
        task.setTaskType(Arrays.asList(1, 2, 3)); // 遥控、遥测、测量
        task.setRepeatType(0); // 仅一次
        task.setStartTime(batchConfig.getStartTime());
        task.setEndTime(batchConfig.getStartTime().plusHours(1)); // 默认1小时

        return task;
    }

    /**
     * 创建默认任务（测控任务）- 兼容旧版本
     */
    private RequirementTaskDTO createDefaultTask(BatchRequirementCreateDTO batchConfig, int index) {
        BatchRequirementCreateDTO.TargetTypeConfig defaultTypeConfig = new BatchRequirementCreateDTO.TargetTypeConfig();
        defaultTypeConfig.setDataType(4); // 默认无人机

        return createDefaultTask(batchConfig, defaultTypeConfig, index);
    }

    /**
     * 根据数据类型获取类型名称
     */
    private String getDataTypeName(Integer dataType) {
        switch (dataType) {
            case 4: return "无人机";
            case 5: return "无人艇";
            case 6: return "无人车";
            case 7: return "弹";
            default: return "目标";
        }
    }

    /**
     * 目标类型数据封装类
     */
    public static class TargetTypeData {
        private List<String> targetIds;
        private List<String> trackIds;
        private List<String> generalIds;

        public List<String> getTargetIds() { return targetIds; }
        public void setTargetIds(List<String> targetIds) { this.targetIds = targetIds; }

        public List<String> getTrackIds() { return trackIds; }
        public void setTrackIds(List<String> trackIds) { this.trackIds = trackIds; }

        public List<String> getGeneralIds() { return generalIds; }
        public void setGeneralIds(List<String> generalIds) { this.generalIds = generalIds; }
    }
}
