package com.gy.show.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gy.show.mapper.CommonMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CommonMapper 测试工具类
 * 用于测试模糊查询功能
 */
@Slf4j
@Component
public class CommonMapperTestUtil {

    @Autowired
    private CommonMapper commonMapper;

    /**
     * 测试现有的 getData 方法模糊查询功能
     */
    public void testGetDataWithKeyword(String tableName, String keyword) {
        log.info("=== 测试 getData 方法模糊查询 ===");
        log.info("表名: {}, 关键字: {}", tableName, keyword);

        try {
            IPage<Map<String, Object>> page = new Page<>(1, 10);
            List<String> searchCols = Arrays.asList("name"); // 在name字段中搜索
            
            IPage<Map<String, Object>> result = commonMapper.getData(page, tableName, keyword, searchCols, null);
            
            log.info("查询结果数量: {}", result.getRecords().size());
            log.info("总记录数: {}", result.getTotal());
            
            for (Map<String, Object> record : result.getRecords()) {
                log.info("记录: ID={}, Name={}", record.get("id"), record.get("name"));
            }
            
        } catch (Exception e) {
            log.error("测试 getData 方法失败", e);
        }
    }

    /**
     * 测试新增的 getDataByFieldLike 方法模糊查询功能
     */
    public void testGetDataByFieldLike(String tableName, String fieldName, String fieldValue) {
        log.info("=== 测试 getDataByFieldLike 方法模糊查询 ===");
        log.info("表名: {}, 字段名: {}, 字段值: {}", tableName, fieldName, fieldValue);

        try {
            IPage<Map<String, Object>> page = new Page<>(1, 10);
            
            IPage<Map<String, Object>> result = commonMapper.getDataByFieldLike(page, tableName, fieldName, fieldValue, null);
            
            log.info("查询结果数量: {}", result.getRecords().size());
            log.info("总记录数: {}", result.getTotal());
            
            for (Map<String, Object> record : result.getRecords()) {
                log.info("记录: ID={}, Name={}", record.get("id"), record.get("name"));
            }
            
        } catch (Exception e) {
            log.error("测试 getDataByFieldLike 方法失败", e);
        }
    }

    /**
     * 测试带额外条件的模糊查询
     */
    public void testGetDataByFieldLikeWithArgs(String tableName, String fieldName, String fieldValue, Map<String, Object> args) {
        log.info("=== 测试 getDataByFieldLike 方法带条件模糊查询 ===");
        log.info("表名: {}, 字段名: {}, 字段值: {}, 额外条件: {}", tableName, fieldName, fieldValue, args);

        try {
            IPage<Map<String, Object>> page = new Page<>(1, 10);
            
            IPage<Map<String, Object>> result = commonMapper.getDataByFieldLike(page, tableName, fieldName, fieldValue, args);
            
            log.info("查询结果数量: {}", result.getRecords().size());
            log.info("总记录数: {}", result.getTotal());
            
            for (Map<String, Object> record : result.getRecords()) {
                log.info("记录: ID={}, Name={}, 其他字段: {}", record.get("id"), record.get("name"), record);
            }
            
        } catch (Exception e) {
            log.error("测试 getDataByFieldLike 方法带条件失败", e);
        }
    }

    /**
     * 对比两种查询方法的结果
     */
    public void compareQueryMethods(String tableName, String keyword) {
        log.info("=== 对比两种查询方法 ===");
        log.info("表名: {}, 关键字: {}", tableName, keyword);

        try {
            IPage<Map<String, Object>> page1 = new Page<>(1, 10);
            IPage<Map<String, Object>> page2 = new Page<>(1, 10);
            
            // 方法1：使用 getData
            List<String> searchCols = Arrays.asList("name");
            IPage<Map<String, Object>> result1 = commonMapper.getData(page1, tableName, keyword, searchCols, null);
            
            // 方法2：使用 getDataByFieldLike
            IPage<Map<String, Object>> result2 = commonMapper.getDataByFieldLike(page2, tableName, "name", keyword, null);
            
            log.info("getData 方法结果数量: {}", result1.getRecords().size());
            log.info("getDataByFieldLike 方法结果数量: {}", result2.getRecords().size());
            
            // 比较结果是否一致
            boolean isSame = result1.getRecords().size() == result2.getRecords().size();
            log.info("两种方法结果是否一致: {}", isSame);
            
        } catch (Exception e) {
            log.error("对比查询方法失败", e);
        }
    }

    /**
     * 测试无人机目标查询示例
     */
    public void testUavTargetQuery() {
        log.info("=== 测试无人机目标查询示例 ===");
        
        // 假设无人机数据表名为 data_uav
        String tableName = "data_uav";
        String targetNamePrefix = "无人机";
        
        testGetDataWithKeyword(tableName, targetNamePrefix);
        testGetDataByFieldLike(tableName, "name", targetNamePrefix);
        compareQueryMethods(tableName, targetNamePrefix);
    }

    /**
     * 测试所有目标类型的查询
     */
    public void testAllTargetTypes() {
        log.info("=== 测试所有目标类型查询 ===");
        
        Map<String, String> targetTypes = new HashMap<>();
        targetTypes.put("data_uav", "无人机");      // 数据类型4
        targetTypes.put("data_boat", "无人艇");     // 数据类型5
        targetTypes.put("data_car", "无人车");      // 数据类型6
        targetTypes.put("data_missile", "弹");      // 数据类型7
        
        for (Map.Entry<String, String> entry : targetTypes.entrySet()) {
            String tableName = entry.getKey();
            String namePrefix = entry.getValue();
            
            log.info("--- 测试 {} 表，名称前缀: {} ---", tableName, namePrefix);
            testGetDataByFieldLike(tableName, "name", namePrefix);
        }
    }
}
