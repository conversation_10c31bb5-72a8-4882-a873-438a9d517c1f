package com.gy.show.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * 网络工具类
 * 提供动态获取本机IP地址的功能
 */
public class NetworkUtils {

    private static final Logger log = LoggerFactory.getLogger(NetworkUtils.class);

    /**
     * 获取本机首选IP地址
     * 优先级：以太网 > WiFi > 其他非虚拟接口
     * @return 本机IP地址
     */
    public static String getPreferredLocalIpAddress() {
        try {
            List<String> ethernetIps = new ArrayList<>();
            List<String> wifiIps = new ArrayList<>();
            List<String> otherIps = new ArrayList<>();

            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();

                // 跳过回环接口、虚拟接口和未启用的接口
                if (networkInterface.isLoopback() || networkInterface.isVirtual() || !networkInterface.isUp()) {
                    continue;
                }

                String interfaceName = networkInterface.getName().toLowerCase();
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();

                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();

                    // 只处理IPv4地址，跳过回环地址和链路本地地址
                    if (address instanceof Inet4Address &&
                            !address.isLoopbackAddress() &&
                            !address.isLinkLocalAddress()) {

                        String hostAddress = address.getHostAddress();

                        // 根据网络接口名称分类
                        if (interfaceName.contains("eth") || interfaceName.contains("en")) {
                            ethernetIps.add(hostAddress);
                            log.debug("发现以太网IP：{}, 接口：{}", hostAddress, interfaceName);
                        } else if (interfaceName.contains("wlan") || interfaceName.contains("wifi") || interfaceName.contains("wl")) {
                            wifiIps.add(hostAddress);
                            log.debug("发现WiFi IP：{}, 接口：{}", hostAddress, interfaceName);
                        } else {
                            otherIps.add(hostAddress);
                            log.debug("发现其他IP：{}, 接口：{}", hostAddress, interfaceName);
                        }
                    }
                }
            }

            // 按优先级返回IP地址
            if (!ethernetIps.isEmpty()) {
                String ip = ethernetIps.get(0);
                log.info("使用以太网IP地址：{}", ip);
                return ip;
            } else if (!wifiIps.isEmpty()) {
                String ip = wifiIps.get(0);
                log.info("使用WiFi IP地址：{}", ip);
                return ip;
            } else if (!otherIps.isEmpty()) {
                String ip = otherIps.get(0);
                log.info("使用其他网络接口IP地址：{}", ip);
                return ip;
            }

        } catch (SocketException e) {
            log.error("获取本机IP地址失败", e);
        }

        // 如果无法获取，返回默认值
        log.warn("无法自动获取本机IP地址，使用默认值：127.0.0.1");
        return "127.0.0.1";
    }

    /**
     * 获取所有可用的本机IP地址
     * @return IP地址列表
     */
    public static List<String> getAllLocalIpAddresses() {
        List<String> ipList = new ArrayList<>();

        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();

                // 跳过回环接口、虚拟接口和未启用的接口
                if (networkInterface.isLoopback() || networkInterface.isVirtual() || !networkInterface.isUp()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();

                    // 只处理IPv4地址，跳过回环地址和链路本地地址
                    if (address instanceof Inet4Address &&
                            !address.isLoopbackAddress() &&
                            !address.isLinkLocalAddress()) {

                        ipList.add(address.getHostAddress());
                    }
                }
            }
        } catch (SocketException e) {
            log.error("获取本机IP地址列表失败", e);
        }

        return ipList;
    }

    /**
     * 根据网络接口名称获取IP地址
     * @param interfaceName 网络接口名称（如：eth0, wlan0等）
     * @return IP地址，如果未找到返回null
     */
    public static String getIpByInterfaceName(String interfaceName) {
        try {
            NetworkInterface networkInterface = NetworkInterface.getByName(interfaceName);
            if (networkInterface != null && networkInterface.isUp() && !networkInterface.isLoopback()) {
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address instanceof Inet4Address && !address.isLoopbackAddress()) {
                        String ip = address.getHostAddress();
                        log.info("通过接口名称 {} 获取到IP地址：{}", interfaceName, ip);
                        return ip;
                    }
                }
            }
        } catch (SocketException e) {
            log.error("通过接口名称获取IP地址失败：{}", interfaceName, e);
        }

        return null;
    }

    /**
     * 检查IP地址是否可达
     * @param ipAddress IP地址
     * @param timeout 超时时间（毫秒）
     * @return 是否可达
     */
    public static boolean isIpReachable(String ipAddress, int timeout) {
        try {
            InetAddress address = InetAddress.getByName(ipAddress);
            return address.isReachable(timeout);
        } catch (Exception e) {
            log.error("检查IP地址可达性失败：{}", ipAddress, e);
            return false;
        }
    }

    /**
     * 获取指定网段的本机IP地址
     * @param networkPrefix 网段前缀（如：192.168.1）
     * @return 匹配的IP地址，如果未找到返回null
     */
    public static String getIpByNetworkPrefix(String networkPrefix) {
        List<String> allIps = getAllLocalIpAddresses();
        for (String ip : allIps) {
            if (ip.startsWith(networkPrefix)) {
                log.info("找到匹配网段 {} 的IP地址：{}", networkPrefix, ip);
                return ip;
            }
        }
        log.warn("未找到匹配网段 {} 的IP地址", networkPrefix);
        return null;
    }
}
