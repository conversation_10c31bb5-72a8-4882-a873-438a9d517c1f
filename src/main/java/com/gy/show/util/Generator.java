//package com.gy.show.util;
//
//import com.baomidou.mybatisplus.annotation.DbType;
//import com.baomidou.mybatisplus.generator.AutoGenerator;
//import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
//import com.baomidou.mybatisplus.generator.config.GlobalConfig;
//import com.baomidou.mybatisplus.generator.config.PackageConfig;
//import com.baomidou.mybatisplus.generator.config.StrategyConfig;
//import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
//
///**
// * 类自动生成
// */
//public class Generator {
//    public static void main(String[] args) {
//
//        // 创建autoGenerator
//        AutoGenerator autoGenerator = new AutoGenerator();
//
//        // 数据源配置
//        autoGenerator.setDataSource(getDataSourceConfig());
//
//        // 全局配置
//        autoGenerator.setGlobalConfig(getGlobalConfig());
//
//        // 包信息配置
//        autoGenerator.setPackageInfo(getPackageConfig());
//
//        // 策略配置
//        autoGenerator.setStrategy(getStrategyConfig());
//
//        // 执行
//        autoGenerator.execute();
//    }
//
//    private static StrategyConfig getStrategyConfig() {
//        //配置策略
//        StrategyConfig strategyConfig = new StrategyConfig();
//        // 注释则生成所有
//        strategyConfig.setInclude("template_business_relation");// String... 类型 todo
//        //自动添加lombokmodel注解
//        strategyConfig.setEntityLombokModel(true);
//        //设置驼峰名命
//        strategyConfig.setNaming(NamingStrategy.underline_to_camel);
//        strategyConfig.setColumnNaming(NamingStrategy.underline_to_camel);
//        strategyConfig.setRestControllerStyle(true);
//        return strategyConfig;
//    }
//
//    private static PackageConfig getPackageConfig() {
//        //包信息
//        PackageConfig packageConfig = new PackageConfig();
//        //存放路径
//        packageConfig.setParent("com.gy.show");
//        //设置模块名
////        packageConfig.setModuleName("target");
//        //设置controller
//        packageConfig.setController("controller");
//        packageConfig.setService("service");
//        packageConfig.setServiceImpl("service.impl");
////        packageConfig.setXml("mapper");
//        packageConfig.setMapper("dao");
//        packageConfig.setEntity("entity");
//        return packageConfig;
//    }
//
//    private static GlobalConfig getGlobalConfig() {
//        GlobalConfig globalConfig = new GlobalConfig();
//        //获取工程绝对路径
////        globalConfig.setOutputDir(System.getProperty("user.dir")+"/src/main/java");
//        globalConfig.setOutputDir(System.getProperty("user.dir")+"/src/main/java");
//        //关闭文件夹
//        globalConfig.setOpen(false);
//        //设置作者
//        globalConfig.setAuthor("sj");
//
//        globalConfig.setServiceName("%sService");
//        return globalConfig;
//    }
//
//    private static DataSourceConfig getDataSourceConfig() {
//        //数据源
//        DataSourceConfig dataSourceConfig = new DataSourceConfig();
//        //选用数据库
//        dataSourceConfig.setDbType(DbType.MYSQL);
//        //设置数据库连接
//        dataSourceConfig.setUrl("*******************************************************************************************************************************************************************");
//        //设置用户名
//        dataSourceConfig.setUsername("root");
//        //设置密码
//        dataSourceConfig.setPassword("123456");
//        //设置数据库驱动
//        dataSourceConfig.setDriverName("com.mysql.cj.jdbc.Driver");
//        return dataSourceConfig;
//    }
//}
