package com.gy.show.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gy.show.common.ObjectConvert;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

public class ConvertUtil {

    private static final String splitor = "[";

    private static final String englishComma = ",";

    private ConvertUtil() {
    }

    /**
     * LocalDateTime 转时间戳
     *
     * @param time 时间
     * @return 时间戳
     */
    public static Long toTimestamp(LocalDateTime time) {
        return time.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 时间戳转 LocalDateTime
     *
     * @param timestamp 时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.of("+8")).toLocalDateTime();
    }

    /**
     * 时间戳转时间字符串
     *
     * @param timestamp 时间戳
     * @return
     */
    public static String timestamp2DateString(long timestamp, String format) {
        return DateUtil.format(toLocalDateTime(timestamp), format);
    }

    public static Long getSecondTimestampTwo(long source) {
        String timestamp = String.valueOf(source / 1000);
        return Long.valueOf(timestamp);
    }

    /**
     * 获取 vo 分页数据
     *
     * @param page do 分页数据
     * @return vo 分页数据
     */
    public static <T> IPage<T> buildPage(IPage<? extends ObjectConvert<T>> page) {
        IPage<T> pageViews = new Page<>();
        BeanUtil.copyProperties(page, pageViews);
        pageViews.setRecords(page.getRecords()
                .stream()
                .map(ObjectConvert::convert)
                .collect(Collectors.toList()));
        return pageViews;
    }

    public static Map<String, Object> fieldConvertIntoComment(Map<String, Object> data, Map<String, String> fieldMap) {
        Map<String, Object> result = new HashMap<>(64);
        fieldMap.forEach((field, comment) -> result.put(comment, data.getOrDefault(field, "")));
        return result;
    }

    public static void convertTimeStamp2Time(Map<String, Object> map) {
        map.entrySet().forEach(entry -> {
            Object value = entry.getValue();
            //时间转换
            if (value instanceof Timestamp) {
                entry.setValue(DateUtil.format(((Timestamp) value), NORM_DATETIME_PATTERN));
            }
        });
    }


    //字符串转换集合
    public static <T> List<T> toJavaList(String source, Class<T> clazz) {
        if (!source.contains(splitor)) {
            if (!source.contains(englishComma)) {
                return Collections.singletonList(Convert.convert(clazz, source));
            } else {
                return Arrays.stream(source.split(englishComma)).map(p -> Convert.convert(clazz, p)).collect(Collectors.toList());
            }
        } else {
            return JSON.parseArray(source, clazz);
        }
    }

    public static List<String> string2List(String source) {
        // 对老数据做兼容处理
        List<String> array;
        if (source.contains("[")) {
            array = JSONArray.parseArray(source, String.class);
        } else {
            array = Arrays.asList(source);
        }
        return array;
    }

    /**
     * map转对象方法
     * @param source
     * @param target
     * @param <T>
     * @return
     */
    public static <T> T map2Object(Map source, Class<T> target) throws Exception{
        Field[] fields = target.getDeclaredFields();
        T t = target.newInstance();

        for (Field field : fields) {
            Object val;
            if ((val = source.get(field.getName())) != null) {
                field.setAccessible(true);
                field.set(t, val);
            }
        }
        return t;
    }

}
