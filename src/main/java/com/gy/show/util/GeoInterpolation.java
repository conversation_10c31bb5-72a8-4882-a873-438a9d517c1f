package com.gy.show.util;

import java.util.ArrayList;
import java.util.List;

public class GeoInterpolation {

    /**
     * 创建插值点列表
     * @param start 起点的经纬高 [经度, 纬度, 高度]
     * @param end 终点的经纬高 [经度, 纬度, 高度]
     * @param numPoints 生成的插值点数量（不含起点和终点）
     * @return 包含起点、插值点和终点的点列表
     */
    public static List<double[]> createInterpolationPoints(double[] start, double[] end, int numPoints) {
        List<double[]> points = new ArrayList<>(numPoints + 2);  // 包含起点和终点

        double startX = start[0], startY = start[1], startZ = start[2];
        double endX = end[0], endY = end[1], endZ = end[2];

        // 计算每一步的经纬度和高度的步长
        double stepX = (endX - startX) / (numPoints + 1);
        double stepY = (endY - startY) / (numPoints + 1);
        double stepZ = (endZ - startZ) / (numPoints + 1);

        // 添加起点
        points.add(start);

        // 生成插值点
        for (int i = 1; i <= numPoints; i++) {
            double interpolatedX = startX + stepX * i;
            double interpolatedY = startY + stepY * i;
            double interpolatedZ = startZ + stepZ * i;
            points.add(new double[]{interpolatedX, interpolatedY, interpolatedZ});
        }

        // 添加终点
        points.add(end);

        return points;
    }

    public static void main(String[] args) {
        // 示例：起点和终点的经纬度和高度
        double[] start = {116.4074, 39.9042, 50.0};  // 北京 [经度, 纬度, 高度]
        double[] end = {-118.2437, 34.0522, 30.0};   // 洛杉矶 [经度, 纬度, 高度]

        // 生成 50 个插值点
        GeoInterpolation geo = new GeoInterpolation();
        List<double[]> points = geo.createInterpolationPoints(start, end, 50);

        // 打印插值点
        for (double[] point : points) {
            System.out.printf("插值点: 经度 = %.6f, 纬度 = %.6f, 高度 = %.2f\n", point[0], point[1], point[2]);
        }
    }
}
