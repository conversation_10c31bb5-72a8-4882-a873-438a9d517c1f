package com.gy.show.util;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

public class MapUtils {

    /*  map 转对象 */
    public static Object mapToObject(Map<String, Object> map, Class<?> beanClass){
        String jsonString = JSONObject.toJSONString(map);
        return JSONObject.parseObject(jsonString,beanClass);
    }

    /* 对象转map */
    public static Map<String,Object> objectToMap(Object object){
        String jsonStr = JSONObject.toJSONString(object);
        return JSONObject.parseObject(jsonStr);
    }
}
