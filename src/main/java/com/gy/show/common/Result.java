package com.gy.show.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 公共返回结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class Result implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    @ApiModelProperty(value = "返回码")
    private Integer code;

    @ApiModelProperty(value = "返回消息")
    private String message;

    @ApiModelProperty(value = "返回数据")
    private Map<String, Object> dataMap = new HashMap<>();

    @ApiModelProperty(value = "返回数据")
    private Object data;

    public static Result ok() {
        return new Result().setSuccess(true)
                .setCode(ResultCode.SUCCESS.getCode())
                .setMessage(ResultCode.SUCCESS.getMessage());
    }

    public static Result ok(Object data) {
        return new Result().setSuccess(true)
                .setData(data)
                .setCode(ResultCode.SUCCESS.getCode())
                .setMessage(ResultCode.SUCCESS.getMessage());
    }

    public static Result ok(Object data, String message) {
        Result result = new Result();
        result.setSuccess(true);
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(message);
        result.setData(data);
        return result;
    }


     public static Result resultData(Boolean success,  Map<String, Object> data) {
        Result result = new Result();
        result.setSuccess(true);
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setDataMap(data);
        return result;
    }

    public static Result error() {
        Result result = new Result();
        result.setSuccess(false);
        result.setCode(ResultCode.COMMON_FAIL.getCode());
        result.setMessage(ResultCode.COMMON_FAIL.getMessage());
        return result;
    }

    public static Result error(String message) {
        Result result = new Result();
        result.setSuccess(false);
        result.setCode(ResultCode.COMMON_FAIL.getCode());
        result.setMessage(message);
        return result;
    }

    public static Result error(Exception e) {
        Result result = new Result();
        result.setSuccess(false);
        result.setCode(ResultCode.COMMON_FAIL.getCode());
        if (e instanceof ServiceException && StringUtils.isNotBlank(e.getMessage())) {
            result.setMessage(e.getMessage());
        } else {
            result.setMessage("系统出现异常,请重新尝试,若依旧失败,请联系管理员");
        }
        return result;
    }

    public static Result nullError(Exception e) {
        Result result = new Result();
        result.setSuccess(false);
        result.setCode(ResultCode.COMMON_FAIL.getCode());
        if (e instanceof CustomException && StringUtils.isNotBlank(e.getMessage())) {
            result.setMessage(e.getMessage());
        } else {
            result.setMessage("数据为空");
        }
        return result;
    }

    /**
     * 自定义返回成功与否
     *
     * @param success
     * @return
     */
    public Result success(Boolean success) {
        this.setSuccess(success);
        return this;
    }

    public Result message(String message) {
        this.setMessage(message);
        return this;
    }

    public Result code(Integer code) {
        this.setCode(code);
        return this;
    }

    public Result data(String key, Object value) {
        this.dataMap.put(key, value);
        return this;
    }

    public Result data(Map<String, Object> map) {
        this.setDataMap(map);
        return this;
    }


    public static  Result fail() {
        return restResult( 500,  null);
    }

    public static  Result fail(String msg) {
        return restResult(500, msg);
    }

    private static  Result restResult(int code, String msg) {
        Result result = new Result();
        result.setCode(code);
        result.setMessage(msg);
        return result;
    }

}
