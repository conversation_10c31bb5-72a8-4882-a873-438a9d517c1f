package com.gy.show.common;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;


/**
 * 常量类
 *
 * <AUTHOR>
 * @since 2022/05/12
 */
@Component
@Slf4j
@Data
public class Const {
    public static final String QUERTYSCUESS = "查询成功";

    public static final String DELETE = "删除成功";

    public static final String SUCESS = "成功";

    public static final String FAIL = "失败";

    public static final String LOGIN_FAIL = "用户名或密码错误";

    public static final String LOGIN_SUCCESS = "登录成功";

    public static final String LOGIN_ERROR = "登录异常";


    public static final Integer IS_NOT_DELETE = 0; // 未删除状态

    public static final Integer IS_DELETE = 1; // 删除状态

    public static final String EQUIPMENT_NOT_USE = "7"; //设备不能使用状态标识

    public static final String EQUIPMENT_USE = "6"; // 设备使用状态

    public static final String TASK_NAME = "task";  // 计划任务标识

    public static final String PLAN_NAME = "plan";  // 规划任务标识

    public static final String TARGET_NAME = "target"; // 目标标识


    public static final String TARGET = "RWGH:TARGET:"; // redis 记录目标轨迹的标识

    public static final String PLAN = "RWGH:PLAN:";     // redis 记录规划任务轨迹的标识

    public static final String TASK = "RWGH:TASK:";     // redis 记录计划任务轨迹的标识

    public static final String CONFIG = "RWGH:CONFIG:"; // redis 记录参数配置的标识

    public static final String KEYS = "config";         // redis 记录参数配置的Map-key值


    public static final Integer JWD_JD = 360;           // 度数

    public static final Integer JWD_CF = 31;            // 次方

    public static final Double JWD_GD = 0.01;           // 高低单位

}