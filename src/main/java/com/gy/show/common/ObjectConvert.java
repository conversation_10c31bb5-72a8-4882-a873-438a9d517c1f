package com.gy.show.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import java.lang.reflect.ParameterizedType;

public class ObjectConvert<T> {

    protected void beforeConvert() {
    }

    protected void afterConvert(T t) {
    }

    public T convert() {
        T t;
        beforeConvert();
        try {
            t = newInstance();
            BeanUtil.copyProperties(this, t, CopyOptions.create().ignoreError());
        } catch (Exception e) {
            throw new RuntimeException("Bean convert failed, Caused by " + e, e);
        }
        afterConvert(t);
        return t;
    }

    @SuppressWarnings("unchecked")
    private T newInstance() throws Exception {
        Class<T> clazz = (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
        return clazz.newInstance();
    }

}
