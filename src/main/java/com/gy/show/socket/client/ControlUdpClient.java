package com.gy.show.socket.client;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.nio.NioDatagramChannel;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ControlUdpClient extends AbstractNettyClient {

    public ControlUdpClient(String id) {
        super(id);
    }

    protected ChannelInitializer<NioDatagramChannel> createHandler() {
        return new ControlUdpChannelInitializer();
    }
}
