//package com.gy.show.socket;
//
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.stereotype.Component;
//
//import java.io.BufferedReader;
//import java.io.IOException;
//import java.io.InputStreamReader;
//import java.io.PrintWriter;
//import java.net.ServerSocket;
//import java.net.Socket;
//
//@Component
//public class SocketServer implements ApplicationRunner {
//
//    @Override
//    public void run(ApplicationArguments args) {
//        BufferedReader br = null;
//        PrintWriter pw = null;
//        Socket s = null;
//        ServerSocket serverSocket = null;
//        try {
//            serverSocket = new ServerSocket(9000);
//            while (true) {
//                s = serverSocket.accept();
//                br = new BufferedReader(new InputStreamReader(s.getInputStream(), "UTF-8"));
//                pw = new PrintWriter(s.getOutputStream(),true);
//                String message;
//                while ((message=br.readLine())!=null){
//                    System.out.println("收到客户端消息："+message);
//                    pw.println("服务端收到客户端消息："+message);
//                }
//                pw.flush();
//                try {
//                    br.close();
//                    pw.close();
//                    s.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//}
