package com.gy.show.socket.handler;

import com.gy.show.enums.ExternalDataTypeEnum;
import com.gy.show.service.ExternalDataService;
import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.socket.message.HeadMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.gy.show.enums.ExternalDataTypeEnum.*;

@ChannelHandler.Sharable
@Slf4j
@Component
public class ControlMessageDecodeHandler extends ChannelInboundHandlerAdapter {

    private static ExternalDataService externalDataService;

    @Resource
    public void setExternalDataService(ExternalDataService externalDataService) {
        ControlMessageDecodeHandler.externalDataService = externalDataService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        NioDatagramChannel channel = (NioDatagramChannel) ctx.channel();
        log.info("链接报告信息：本客户端链接到服务端。channelId：{}, IP: {}, PORT:{}", channel.id(), channel.localAddress().getHostString(), channel.localAddress().getPort());
        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.info("断开链接,{}", ctx.channel().localAddress().toString());
        super.channelInactive(ctx);
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        String id = ctx.channel().attr(AttributeKey.valueOf("id")).get().toString();
        String ter = ctx.channel().attr(AttributeKey.valueOf("ter")).get().toString();
        log.info("接收到数据包：{}，开始读取数据", id);

        // 解析报文头
        HeadMessage head = externalDataService.parseControlMessageHead(msg);
        log.info("解析头部信息结果：{}", head);

        // 解析报文体
        parseMessageBody(head, msg, id, ter);

//        super.channelRead(ctx, msg);
    }

    private void parseMessageBody(HeadMessage head, Object msg, String id, String ter) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();
        try {
            // 根据包类型去判断业务类型是什么
            String type = FieldDefinition.byteToHex(head.getType());

            ExternalDataTypeEnum typeEnum = ExternalDataTypeEnum.getEnumByCode(Integer.parseInt(type));
            log.info("开始解析报文体,当前报文类型是：{}", typeEnum.getMessage());

            switch (typeEnum) {
                case BUSINESS_DATA: // 业务数据，业务数据只需要转发无需解析
                    externalDataService.handlerBusinessData(msg, id, ter);
                    break;
                case CAR_DATA: // 管控数据 无人车数据
                    externalDataService.handlerControlData(msg, CAR_DATA, id, ter);
                    break;
                case SHIP_DATA: // 管控数据 无人艇数据
                    externalDataService.handlerControlData(msg, SHIP_DATA, id, ter);
                    break;
                case PLANE_DATA: // 管控数据 无人机数据
                    externalDataService.handlerControlData(msg, PLANE_DATA, id, ter);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("发生异常", e);
        } finally {
            byteBuf.release();
        }

    }

}
