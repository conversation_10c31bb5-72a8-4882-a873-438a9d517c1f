package com.gy.show.socket;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.Socket;

public class SocketClient {
    public static void main(String[] args) throws IOException {
        Socket socket=null;
        PrintWriter writer=null;
        try {
            socket=new Socket("192.168.19.252",9000);
            if (socket.isConnected()){
                writer=new PrintWriter(new OutputStreamWriter(socket.getOutputStream()));
                writer.write("我是客户端");
                writer.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        finally {
            writer.close();
            socket.close();
        }
    }
}
