package com.gy.show.enums;

import java.util.Arrays;

public enum EquipmentEnum {


    LAND_BASED(1, "地基平台"),

    SPACE_BASED(2, "空基平台"),

    VACANT_PART(3, "天基平台"),

    SEA_BASED(8, "海基平台");


    private Integer code;

    private String message;

    EquipmentEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static EquipmentEnum getEnumByCode(Integer code) {
        return Arrays.stream(EquipmentEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
