package com.gy.show.enums;

import java.util.Arrays;

public enum ConfigTypeEnum {


    TIME_RANGE(1, "时间段"),

    DROPDOWN(2, "下拉框"),

    BOOLEAN(3, "布尔类型"),

    INTEGER(4, "整型");


    private Integer code;

    private String message;

    ConfigTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ConfigTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(ConfigTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
