package com.gy.show.enums;

import java.util.Arrays;

public enum AreaTypeEnum {


    EQUIPMENT(1, "资源"),

    TARGET(2, "目标");


    private Integer code;

    private String message;

    AreaTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static AreaTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(AreaTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
