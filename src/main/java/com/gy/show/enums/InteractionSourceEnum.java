package com.gy.show.enums;

public enum InteractionSourceEnum {

    /**
     * 站点状态
     */
    STATION_STATE("stationState"),

    /**
     * 终端状态
     */
    TERMINAL_STATE("terminalState"),

    /**
     * 操控端交互日志
     */
    CONTROL_LOG("controlLog"),

    /**
     * 接入决策模块交互日志
     */
    SELECTION_LOG("selectionLog");

    private String message;

    InteractionSourceEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
