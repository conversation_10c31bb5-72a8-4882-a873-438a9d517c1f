package com.gy.show.enums;

/**
 * 控制命令响应Result枚举
 */
public enum CommandResponseEnum {

    MORMAl("0", "正常接收并执行"),

    REJECT("1", "分控拒收"),

    FOEMAT_REEOR("2","帧格式错误（未知命令）"),

    OBJECT_NOT_EXIST("3","被控对象不存在"),

    PARAM_ERROR("4","参数错误"),

    CONDITION_NOT_MET("5","条件不具备"),

    UNKNOWN("6","未知原因失败");

    private String code;

    private String message;

    CommandResponseEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
