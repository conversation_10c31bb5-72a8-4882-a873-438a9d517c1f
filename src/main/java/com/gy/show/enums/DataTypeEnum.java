package com.gy.show.enums;

import java.util.Arrays;

public enum DataTypeEnum {


    GROUND_PLATFORM(1, "地基平台"),

    SPACE_PLATFORM(2, "空基平台"),

    SKY_PLATFORM(3, "天基平台"),

    UAV(4, "无人机"),

    BOAT(5, "无人艇"),

    CAR(6, "无人车"),

    MISSILE(7, "弹"),

    SEA_BASED(8, "海基平台");


    private Integer code;

    private String message;

    DataTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static DataTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(DataTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
