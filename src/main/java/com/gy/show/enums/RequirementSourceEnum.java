package com.gy.show.enums;

public enum RequirementSourceEnum {

    MANUAL(1, "手动输入"),

    EXTERNAL(2, "外部输入"),

    TEMPLATE(3, "模板创建");


    private Integer code;

    private String message;

    RequirementSourceEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
