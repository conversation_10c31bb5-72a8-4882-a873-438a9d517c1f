package com.gy.show.enums;

/**
 * 数据源模式枚举
 * 用于控制真实数据和模拟数据的转发
 */
public enum DataSourceModeEnum {
    
    /**
     * 真实数据模式 - 转发通过网络接收的真实数据
     */
    REAL_DATA("real", "真实数据"),
    
    /**
     * 模拟数据模式 - 转发模拟生成的数据
     */
    SIMULATION_DATA("simulation", "模拟数据");

    private final String code;
    private final String description;

    DataSourceModeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static DataSourceModeEnum fromCode(String code) {
        for (DataSourceModeEnum mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的数据源模式代码: " + code);
    }

    /**
     * 判断是否为真实数据模式
     */
    public boolean isRealData() {
        return this == REAL_DATA;
    }

    /**
     * 判断是否为模拟数据模式
     */
    public boolean isSimulationData() {
        return this == SIMULATION_DATA;
    }
}
