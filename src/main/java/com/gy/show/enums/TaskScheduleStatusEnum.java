package com.gy.show.enums;

import java.util.Arrays;

public enum TaskScheduleStatusEnum {


    NOT_SCHEDULE(0, "待调度"),

    SCHEDULING(1, "调度中"),

    SCHEDULED_SUCCESS(2, "调度成功"),

    SCHEDULED_FAIL(3, "调度失败");


    private Integer code;

    private String message;

    TaskScheduleStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static TaskScheduleStatusEnum getEnumByCode(Integer code) {
        return Arrays.stream(TaskScheduleStatusEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
