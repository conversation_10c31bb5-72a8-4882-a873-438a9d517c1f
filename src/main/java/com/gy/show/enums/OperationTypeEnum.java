package com.gy.show.enums;

public enum OperationTypeEnum {
    LOGIN,
    INSERT,
    UPDATE,
    DELETE,
    PLAN,
    ClEAR,
    OTHER;

    public static String getMsg(OperationTypeEnum type){
        String msg = null;
        switch (type){
            case LOGIN:
                msg = "登录";
                break;
            case INSERT:
                msg = "新增";
                break;
            case UPDATE:
                msg = "修改";
                break;
            case DELETE:
                msg = "删除";
                break;
            case PLAN:
                msg = "规划";
                break;
            case ClEAR:
                msg = "清除";
                break;
            case OTHER:
                msg = "其它";
                break;
            default:
                break;
        }
        return msg;
    }
}
