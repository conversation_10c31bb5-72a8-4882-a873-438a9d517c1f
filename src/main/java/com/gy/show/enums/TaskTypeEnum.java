package com.gy.show.enums;

import java.util.Arrays;

public enum TaskTypeEnum {


    YK(1, "遥控"),

    Y<PERSON>(2, "遥测"),

    <PERSON><PERSON>(3, "测量"),

    SC(4, "数传"),

    <PERSON><PERSON>(5, "测控");


    private Integer code;

    private String message;

    TaskTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static TaskTypeEnum getEnumByName(String name) {
        return Arrays.stream(TaskTypeEnum.values()).filter(f -> f.getMessage().equals(name)).findFirst().orElse(null);
    }

    public static TaskTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(TaskTypeEnum.values()).filter(f -> f.getCode().equals(code)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
