package com.gy.show.enums;

/**
 * 外部数据包类型
 */
public enum ClientTypeEnum {

    CAR("car"),
//    CAR2("car2"), // 底盘
//    CAR3("car3"), // 自主

    SHIP("ship"),

    PLANE("plane"),

    TERMINAL("terminal"),

    MISSILE("missile"),

    UAV("uav"),

    SPACE("space"),

    SPACE_STATUS("spaceStatus");


    private String message;

    ClientTypeEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
