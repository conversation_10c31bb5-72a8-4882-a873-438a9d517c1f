package com.gy.show.enums;

import java.util.Arrays;

public enum RequirementStatusEnum {

    PENDING_DECOMPOSITION(0, "待分解"),

    DECOMPOSED(1, "已分解"),

    SCHEDULED(2, "已调度");

    private Integer code;

    private String message;

    RequirementStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static RequirementStatusEnum getEnumByCode(Integer code) {
        return Arrays.stream(RequirementStatusEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }
}
