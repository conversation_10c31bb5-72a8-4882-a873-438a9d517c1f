package com.gy.show.enums;

/**
 * 返回工具类的枚举
 */
public enum ResultEnum {

    SUCCESS(200,"成功"),
    FAIL(500,"失败"),
    DOWNLOADERROR(500,"下载失败"),
    NO_PERMISSON(403,"没有权限"),
    NO_AUTH(401,"请先登录"),
    NOT_FOUND(404,"未找到资源"),
    SERVER_ERROR(500,"服务器异常"),
    INVALID_PARAMS(501,"参数错误");

    private Integer code;
    private String msg;

    ResultEnum(Integer code, String msg){
        this.code=code;
        this.msg=msg;
    }

    public Integer getCode(){
        return code;
    }

    public String getMsg(){
        return msg;
    }
}
