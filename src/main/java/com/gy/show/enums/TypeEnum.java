package com.gy.show.enums;

public enum TypeEnum {


    INFO(1, "提示"),


    WARN(2, "警告"),


    ERROR(3, "错误");


    private Integer code;

    private String message;

    TypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
