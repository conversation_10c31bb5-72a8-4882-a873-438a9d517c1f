package com.gy.show.enums;

import java.util.Arrays;

public enum TrackTypeEnum {


    INTERNAL(1, "手动创建"),

    EXTERNAL(2, "外部导入");


    private Integer code;

    private String message;

    TrackTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static TrackTypeEnum getEnumByCode(Integer code) {
        return Arrays.stream(TrackTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
