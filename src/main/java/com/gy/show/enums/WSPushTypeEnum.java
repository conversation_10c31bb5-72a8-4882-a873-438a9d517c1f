package com.gy.show.enums;

/**
 * WS type 说明
 */
public enum WSPushTypeEnum {

    SATELLITE_POINT_UPDATE("1","轨迹更新"),// 轨迹更新
    TARGET_POINT_PUSH("2","目标点位"),     // 目标点位
    EQUIPMENT_STATUS("3","设备状态"),      // 设备状态（位置信息、状态信息）
    PLAN_ISSUE("4","任务下发"),         // 规划任务下发
    PLAN_DELETE("5","任务删除"),        // 规划任务删除
    PLAN_STATUS("6","任务状态"),           // 任务状态
    EQUIPMENT_PLAN("7","设备任务"),       // 设备任务
    TASK_LIST("8","计划列表刷新"),     // 计划列表刷新
    PLAN_LIST("9","规划列表刷新"),       // 规划列表刷新
    REMOVE_SIGNAL_LINE("10","删除信号轨迹");       // 删除信号轨迹

    private final String type;
    private final String clazz;

    WSPushTypeEnum(String type, String clazz) {
        this.type = type;
        this.clazz = clazz;
    }

    public String getType() {
        return type;
    }

    public String getClazz() {
        return clazz;
    }

}
