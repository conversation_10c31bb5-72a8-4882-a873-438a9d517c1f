package com.gy.show.enums;

import java.util.Arrays;

public enum AlgorithmContextEnum {


    SORTED_COVER(1, "开始时间优先"),

    SORTED_PRIORITY(2, "优先级排序算法"),

    THIRD_PART(3, "三方调度算法"),

    CAVER_CALCULATE(4, "设备覆盖率计算"),

    CONTINUOUS_PRIORITY(5, "连续测控优先");


    private Integer code;

    private String message;

    AlgorithmContextEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static AlgorithmContextEnum getEnumByCode(Integer code) {
        return Arrays.stream(AlgorithmContextEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
