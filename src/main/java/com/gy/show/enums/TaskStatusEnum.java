package com.gy.show.enums;

import java.util.Arrays;

public enum TaskStatusEnum {


    NOT_EXE(0, "待执行"),

    EXEING(1, "执行中"),

    EXE_SUCCESS(2, "执行成功"),

    EXE_FAIL(3, "执行失败"),

    CALL_OFF(4, "已取消");


    private Integer code;

    private String message;

    TaskStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static TaskStatusEnum getEnumByCode(Integer code) {
        return Arrays.stream(TaskStatusEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
