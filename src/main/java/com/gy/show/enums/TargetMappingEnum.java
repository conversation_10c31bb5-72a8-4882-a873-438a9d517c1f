package com.gy.show.enums;

import java.util.Arrays;

public enum TargetMappingEnum {


    CAR1(119, "_c1", "car_c1", 2),

    CAR2(120, "_c2", "car_c2", 2),

    SHIP(117, "_ship", "ship_ship", 3),

    UAV(118, "_uav", "plane_uav", 4);


    private Integer code;

    private String id;

    private String flag;

    private Integer packageType;

    TargetMappingEnum(Integer code, String id, String flag, Integer packageType) {
        this.code = code;
        this.id = id;
        this.flag = flag;
        this.packageType = packageType;
    }

    public static TargetMappingEnum getEnumByCode(Integer code) {
        return Arrays.stream(TargetMappingEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
    }

    public static TargetMappingEnum getEnumById(String id) {
        return Arrays.stream(TargetMappingEnum.values()).filter(f -> f.getId().equalsIgnoreCase(id)).findFirst().orElse(null);
    }

    public static TargetMappingEnum getEnumByFlag(String flag) {
        return Arrays.stream(TargetMappingEnum.values()).filter(f -> f.getFlag().equalsIgnoreCase(flag)).findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Integer getPackageType() {
        return packageType;
    }

    public void setPackageType(Integer packageType) {
        this.packageType = packageType;
    }
}
