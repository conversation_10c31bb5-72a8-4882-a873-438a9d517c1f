package com.gy.show.service;

import com.gy.show.enums.DataSourceModeEnum;

/**
 * 外部数据配置管理服务
 * 用于管理外部数据处理相关的配置参数
 */
public interface ExternalDataConfigService {

    /**
     * 获取数据跟随状态
     * @return true表示开启跟随，false表示关闭跟随
     */
    boolean getFollowStatus();

    /**
     * 设置数据跟随状态
     * @param followStatus true表示开启跟随，false表示关闭跟随
     */
    void setFollowStatus(boolean followStatus);

    /**
     * 切换数据跟随状态
     * @return 切换后的状态
     */
    boolean toggleFollowStatus();

    /**
     * 获取当前数据源模式
     * @return 当前数据源模式
     */
    DataSourceModeEnum getDataSourceMode();

    /**
     * 设置数据源模式
     * @param mode 数据源模式
     */
    void setDataSourceMode(DataSourceModeEnum mode);

    /**
     * 切换数据源模式
     * @return 切换后的数据源模式
     */
    DataSourceModeEnum toggleDataSourceMode();

    /**
     * 判断当前是否为真实数据模式
     * @return true表示真实数据模式，false表示模拟数据模式
     */
    boolean isRealDataMode();

    /**
     * 判断当前是否为模拟数据模式
     * @return true表示模拟数据模式，false表示真实数据模式
     */
    boolean isSimulationDataMode();
}
