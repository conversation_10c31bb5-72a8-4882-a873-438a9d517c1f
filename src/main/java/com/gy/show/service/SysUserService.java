package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.SysUser;
import com.gy.show.entity.dto.SysUserDTO;
import com.gy.show.entity.dto.UserPasswordDTO;

public interface SysUserService extends IService<SysUser> {

    void save(SysUserDTO userDTO);

    IPage<SysUserDTO> getUsers(IPage<SysUser> page, String keyword);

    SysUserDTO getUserById(String id);

    void updateUser(SysUserDTO user);

    void changeStatus(String userId);

    void changeUserPassword(UserPasswordDTO userPasswordDTO);
}
