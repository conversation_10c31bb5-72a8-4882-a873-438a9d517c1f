package com.gy.show.service;

import com.gy.show.entity.dto.DataAreaDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;

import java.util.List;
import java.util.Map;

public interface StatsService {

    Map<String, Object> typeStats(String areaId);

    Map<String, Integer> typeStatsByDataType(String areaId);

    Map<String, Integer> areaStats(Integer areaType);

    Map<String, Integer> areaDataTypeStats(Integer type);

    Map<String, Integer> allTypeStats(Integer type);

    Map<String, Double> areaIdle();

    Map<String, Object> requirementStat();

    List<RequirementTaskDTO> targetBz(String requirementId);

    Object realTimeAreaType(String requirementId);
}
