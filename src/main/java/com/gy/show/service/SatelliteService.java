package com.gy.show.service;

import com.cetc10.spaceflight.orbitpre.orbit.LLAPredict;
import com.gy.show.entity.dto.SatelliteDTO;

import java.time.LocalDateTime;
import java.util.List;

public interface SatelliteService {

    Object calculateOrbit(String generaId, String equipmentId, String startTime);

    void startNumericalForecast(String generaId, String equipmentId);

    void stopNumericalForecast(String generaId, String equipmentId);

    Object getSatelliteCoordinate(List<SatelliteDTO> satelliteDTO);

    List<LLAPredict> getSatelliteCoordinate(SatelliteDTO satelliteDTO);
}
