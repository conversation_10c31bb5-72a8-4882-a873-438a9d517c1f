package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.DataPresetTrackInfo;
import com.gy.show.entity.dto.DataPresetTrackInfoDTO;

public interface DataPresetTrackInfoService extends IService<DataPresetTrackInfo> {

    IPage<DataPresetTrackInfoDTO> pageTrackInfo(IPage page, String keyword);

    void deleteTrackInfo(String id);

    DataPresetTrackInfo createPresetTrackInfo(DataPresetTrackInfoDTO dataPresetTrackInfoDTO);

    DataPresetTrackInfo updatePresetTrackInfo(DataPresetTrackInfoDTO dataPresetTrackInfoDTO);

    void updateTaskTrack(String presetId);
}
