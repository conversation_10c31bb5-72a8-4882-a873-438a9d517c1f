package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.DataModel;

public interface DataModelService extends IService<DataModel> {

    /**
     * 根据ID获取平台资源模型信息
     *
     * @param id 平台资源模型ID
     * @return 平台资源模型信息
     */
    DataModel getDataModelById(String id);

    /**
     * 创建平台资源模型信息
     *
     * @param dataModel 平台资源模型信息
     * @return 创建后的平台资源模型信息
     */
    DataModel createDataModel(DataModel dataModel);

    /**
     * 更新平台资源模型信息
     *
     * @param id          平台资源模型ID
     * @param newDataModel 更新后的平台资源模型信息
     * @return 更新后的平台资源模型信息
     */
    DataModel updateDataModel(String id, DataModel newDataModel);

    /**
     * 删除平台资源模型信息
     *
     * @param id 平台资源模型ID
     */
    void deleteDataModel(String id);

    /**
     * 分页获取所有平台资源模型信息
     *
     * @param page    当前页码，默认为1
     * @param size    每页记录数，默认为10
     * @param keyword 关键字，模糊查询模型名称
     * @return 平台资源模型信息分页列表
     */
    IPage<DataModel> getAllDataModels(int page, int size, String keyword);
}
