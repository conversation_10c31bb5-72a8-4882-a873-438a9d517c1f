package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.DataArea;
import com.gy.show.entity.dto.DataAreaDTO;

import java.util.List;

public interface DataAreaService extends IService<DataArea> {

    /**
     * 根据ID获取资源域信息
     *
     * @param id 资源域ID
     * @return 资源域信息
     */
    DataArea getDataAreaById(String id);

    /**
     * 创建资源域信息
     *
     * @param dataArea 资源域信息
     * @return 创建后的资源域信息
     */
    DataArea createDataArea(DataAreaDTO dataAreaDTO);


    /**
     * 删除资源域信息
     *
     * @param id 资源域ID
     */
    void deleteDataArea(String id);

    /**
     * 分页获取所有资源域信息
     *
     * @param page     当前页码，默认为1
     * @param size     每页记录数，默认为10
     * @param areaType
     * @param keyword  关键字，模糊查询资源域名称
     * @param source
     * @return 资源域信息分页列表
     */
    IPage<DataAreaDTO> getAllDataAreas(int page, int size, Integer areaType, String keyword, Integer source);

    /**
     * 更新资源域信息
     *
     * @return 更新后的资源域信息
     */
    void updateDataArea(DataAreaDTO dataArea);

    List<DataAreaDTO> getAreaAndTargetInfo(String generalId, String targetId);
}
