package com.gy.show.service;

import java.util.List;
import java.util.Map;

/**
 * 缓存预热服务接口
 */
public interface CacheWarmupService {

    /**
     * 启动缓存预热
     *
     * @param taskIds 任务ID列表
     * @return 预热任务ID
     */
    String startWarmup(List<String> taskIds);

    /**
     * 获取预热状态
     *
     * @param warmupId 预热任务ID
     * @return 预热状态信息
     */
    Map<String, Object> getWarmupStatus(String warmupId);

    /**
     * 停止预热
     *
     * @param warmupId 预热任务ID
     */
    void stopWarmup(String warmupId);

    /**
     * 获取所有预热任务列表
     *
     * @return 预热任务列表
     */
    List<Map<String, Object>> getWarmupList();

    /**
     * 清除指定业务的缓存
     *
     * @param bzId 业务ID
     */
    void clearCache(String bzId);
}
