package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.external.ReceiveExternalDTO;
import org.springframework.core.io.InputStreamResource;

import javax.servlet.http.HttpServletResponse;

public interface RequirementInfoService extends IService<RequirementInfo> {

    /**
     * 根据ID获取需求信息
     *
     * @param id 需求ID
     * @return 需求信息
     */
    RequirementInfoDTO getRequirementInfoById(String id);

    /**
     * 创建需求信息
     *
     * @param requirementInfo 需求信息
     * @return 创建后的需求信息
     */
    RequirementInfo createRequirementInfo(RequirementInfo requirementInfo);

    /**
     * 更新需求信息
     *
     * @param id           需求ID
     * @param newRequirementInfo 更新后的需求信息
     * @return 更新后的需求信息
     */
    RequirementInfo updateRequirementInfo(String id, RequirementInfo newRequirementInfo);

    /**
     * 删除需求信息
     *
     * @param id 需求ID
     */
    void deleteRequirementInfo(String id);

    /**
     * 分页获取所有需求信息
     *
     * @param page    当前页码，默认为1
     * @param size    每页记录数，默认为10
     * @param keyword 关键字，模糊查询需求名称
     * @return 需求信息分页列表
     */
    IPage<RequirementInfo> getAllRequirementInfos(int page, int size, String keyword);

    RequirementInfo addRequirementInfo(RequirementInfoDTO requirementInfoDTO);

    Object getTaskById(String requirementId);

    InputStreamResource getFileTemplate(HttpServletResponse response);

    void parseExcel(String id);

    void deleteRequirementFile(String id);

    void receiveExternalRequirement(ReceiveExternalDTO keys);

    Integer getTrackEndTime(String persetId);
}
