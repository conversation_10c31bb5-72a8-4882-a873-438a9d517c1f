package com.gy.show.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dos.TaskTargetRelation;
import com.gy.show.entity.dto.RequirementTargetTrackDTO;
import com.gy.show.entity.dto.TaskTargetRelationDTO;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public interface TaskTargetService extends IService<TaskTargetRelation> {

    TaskTargetRelationDTO listByTask(String taskId, Boolean isInterpolated);

    void storeTarget(TaskTargetRelationDTO taskTargetRelationDTO);

    void removeTrack(String relationId);

    void updateTarget(TaskTargetRelationDTO taskTargetRelationDTO);

    List<TaskTargetRelation> listByTaskIds(List<String> taskIds);

    void removeTargetAndTrack(String taskId, String targetId);

    LinkedList<double[]> calculateTaskPoint(RequirementTask task, List<RequirementTargetTrackDTO> track, TaskTargetRelation targetRelation);

    /**
     * 批量根据任务ID查询目标及航迹信息
     * @param taskIds 任务ID集合
     * @param isInterpolated 是否插值
     * @return 任务ID与目标信息的映射
     */
    Map<String, TaskTargetRelationDTO> listTargetsByTaskIds(List<String> taskIds, Boolean isInterpolated);

}
