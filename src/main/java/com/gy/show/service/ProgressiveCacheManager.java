package com.gy.show.service;

import com.gy.show.config.PerformanceConfig;
import com.gy.show.runnner.PerformanceMonitor;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 渐进式缓存管理器
 * 专门为高频调用场景优化的缓存策略
 */
@Slf4j
@Service
public class ProgressiveCacheManager {

    @Autowired
    private PerformanceConfig performanceConfig;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    // 专用线程池，避免耗尽默认线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(
            Math.max(2, Runtime.getRuntime().availableProcessors() / 2),
            r -> {
                Thread t = new Thread(r, "progressive-cache-" + System.currentTimeMillis());
                t.setDaemon(true); // 设置为守护线程，避免阻止JVM退出
                return t;
            }
    );

    // 记录正在进行渐进式计算的任务
    private final ConcurrentHashMap<String, AtomicBoolean> progressiveComputingTasks = new ConcurrentHashMap<>();

    // 记录渐进式计算的进度
    private final ConcurrentHashMap<String, AtomicInteger> progressiveProgress = new ConcurrentHashMap<>();

    /**
     * 获取指定时间点的数据，如果不存在则触发渐进式预计算
     */
    public String getTimePointData(String cacheKey, Integer timePoint, Integer totalTime,
                                   List<String> taskIds, TimePointDataComputer dataComputer) {

        // 首先尝试从缓存获取
        Object cachedData = RedisUtil.HashOps.hGet(cacheKey, String.valueOf(timePoint));
        if (cachedData != null) {
            performanceMonitor.incrementCounter("cache_hit");
            return cachedData.toString();
        }

        performanceMonitor.incrementCounter("cache_miss");

        // 缓存未命中，立即计算当前时间点数据
        String currentData = dataComputer.computeTimePointData(taskIds, timePoint);

        // 缓存当前数据
        RedisUtil.HashOps.hPut(cacheKey, String.valueOf(timePoint), currentData);
        setCacheExpiration(cacheKey, totalTime);

        // 只有在合理的totalTime范围内才启动后台计算
        if (totalTime > 0 && totalTime <= 7200) { // 最多2小时
            // 启动渐进式预计算（如果还没有启动）
            startProgressiveComputation(cacheKey, timePoint, totalTime, taskIds, dataComputer);

            // 启动预测性预加载（只预加载少量数据）
            if (timePoint < totalTime) {
                startPredictivePreloading(cacheKey, timePoint, totalTime, taskIds, dataComputer);
            }
        }

        return currentData;
    }



    /**
     * 启动渐进式预计算
     */
    private void startProgressiveComputation(String cacheKey, Integer currentTimePoint, Integer totalTime,
                                             List<String> taskIds, TimePointDataComputer dataComputer) {

        // 检查是否已经在进行渐进式计算
        AtomicBoolean isComputing = progressiveComputingTasks.computeIfAbsent(cacheKey, k -> new AtomicBoolean(false));
        if (!isComputing.compareAndSet(false, true)) {
            return; // 已经在计算中
        }

        log.info("启动渐进式预计算，cacheKey: {}, totalTime: {}", cacheKey, totalTime);

        // 使用专用线程池，避免线程泄漏
        executorService.submit(() -> {
            try {
                AtomicInteger progress = progressiveProgress.computeIfAbsent(cacheKey, k -> new AtomicInteger(0));
                int batchSize = performanceConfig.getProgressiveBatchSize();
                int delay = performanceConfig.getProgressiveDelayMs();

                // 分批计算所有时间点
                for (int startPoint = 0; startPoint <= totalTime; startPoint += batchSize) {
                    int endPoint = Math.min(startPoint + batchSize - 1, totalTime);

                    // 计算这一批的数据
                    computeBatchTimePoints(cacheKey, startPoint, endPoint, taskIds, dataComputer);

                    progress.set(endPoint);

                    // 短暂延迟，避免占用过多资源
                    if (delay > 0) {
                        Thread.sleep(delay);
                    }

                    log.debug("渐进式计算进度: {}/{}", endPoint, totalTime);
                }

                log.info("渐进式预计算完成，cacheKey: {}", cacheKey);

            } catch (Exception e) {
                log.error("渐进式预计算失败", e);
            } finally {
                isComputing.set(false);
                progressiveComputingTasks.remove(cacheKey);
                progressiveProgress.remove(cacheKey);
            }
        });
    }

    /**
     * 启动预测性预加载（优化版本，减少计算量）
     */
    private void startPredictivePreloading(String cacheKey, Integer currentTimePoint, Integer totalTime,
                                           List<String> taskIds, TimePointDataComputer dataComputer) {

        // 减少预加载窗口大小，避免过度计算
        int windowSize = Math.min(performanceConfig.getPredictiveWindowSize(), 10);
        int endPoint = Math.min(currentTimePoint + windowSize, totalTime);

        // 使用专用线程池
        executorService.submit(() -> {
            try {
                log.debug("启动预测性预加载，范围: {} - {}", currentTimePoint + 1, endPoint);

                // 批量检查缓存状态，减少Redis调用
                List<String> timePointsToCompute = new ArrayList<>();
                for (int timePoint = currentTimePoint + 1; timePoint <= endPoint; timePoint++) {
                    Object cached = RedisUtil.HashOps.hGet(cacheKey, String.valueOf(timePoint));
                    if (cached == null) {
                        timePointsToCompute.add(String.valueOf(timePoint));
                    }
                }

                // 只计算需要的时间点
                if (!timePointsToCompute.isEmpty()) {
                    Map<String, String> batchData = new HashMap<>();
                    for (String timePointStr : timePointsToCompute) {
                        int timePoint = Integer.parseInt(timePointStr);
                        String data = dataComputer.computeTimePointData(taskIds, timePoint);
                        batchData.put(timePointStr, data);

                        // 限制批次大小，避免内存问题
                        if (batchData.size() >= 5) {
                            RedisUtil.HashOps.hPutAll(cacheKey, batchData);
                            batchData.clear();
                        }
                    }

                    // 写入剩余数据
                    if (!batchData.isEmpty()) {
                        RedisUtil.HashOps.hPutAll(cacheKey, batchData);
                    }
                }

                setCacheExpiration(cacheKey, totalTime);

            } catch (Exception e) {
                log.error("预测性预加载失败", e);
            }
        });
    }

    /**
     * 批量计算时间点数据
     */
    private void computeBatchTimePoints(String cacheKey, int startPoint, int endPoint,
                                        List<String> taskIds, TimePointDataComputer dataComputer) {
        try {
            Map<String, String> batchData = new HashMap<>();

            for (int timePoint = startPoint; timePoint <= endPoint; timePoint++) {
                // 检查是否已经缓存
                Object cached = RedisUtil.HashOps.hGet(cacheKey, String.valueOf(timePoint));
                if (cached == null) {
                    String data = dataComputer.computeTimePointData(taskIds, timePoint);
                    batchData.put(String.valueOf(timePoint), data);
                }
            }

            // 批量写入缓存
            if (!batchData.isEmpty()) {
                RedisUtil.HashOps.hPutAll(cacheKey, batchData);
            }

        } catch (Exception e) {
            log.error("批量计算时间点数据失败，范围: {} - {}", startPoint, endPoint, e);
        }
    }

    /**
     * 设置缓存过期时间
     */
    private void setCacheExpiration(String cacheKey, Integer totalTime) {
        int millis = totalTime * 1000 * performanceConfig.getPointDataCacheMultiplier();
        if (millis > 0) {
            RedisUtil.KeyOps.expire(cacheKey, millis, TimeUnit.MILLISECONDS);
        }
    }

    /**
     * 获取渐进式计算进度
     */
    public int getProgressiveProgress(String cacheKey) {
        AtomicInteger progress = progressiveProgress.get(cacheKey);
        return progress != null ? progress.get() : 0;
    }

    /**
     * 检查是否正在进行渐进式计算
     */
    public boolean isProgressiveComputing(String cacheKey) {
        AtomicBoolean isComputing = progressiveComputingTasks.get(cacheKey);
        return isComputing != null && isComputing.get();
    }

    /**
     * 关闭线程池，释放资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭渐进式缓存管理器线程池");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
