// RequirementEquipmentTrackService.java

package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.RequirementEquipmentTrack;

public interface RequirementEquipmentTrackService extends IService<RequirementEquipmentTrack> {

    RequirementEquipmentTrack getById(String id);

    IPage<RequirementEquipmentTrack> list(Integer pageNum, Integer pageSize, String keyword);

    void add(RequirementEquipmentTrack requirementEquipmentTrack);

    void update(RequirementEquipmentTrack requirementEquipmentTrack);

    void delete(String id);
}
