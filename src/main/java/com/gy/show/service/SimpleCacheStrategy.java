package com.gy.show.service;

import com.gy.show.runnner.PerformanceMonitor;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 简化的缓存策略
 * 专注于快速响应，减少复杂的后台计算
 */
@Slf4j
@Service
public class SimpleCacheStrategy {

    @Autowired
    private PerformanceMonitor performanceMonitor;

    /**
     * 简单的缓存获取策略
     * 只计算当前需要的数据，不进行复杂的预计算
     */
    public String getTimePointDataSimple(String cacheKey, Integer timePoint, Integer totalTime,
                                         TimePointDataComputer dataComputer, java.util.List<String> taskIds) {

        long startTime = performanceMonitor.startTimer("getTimePointDataSimple");

        try {
            // 首先尝试从缓存获取
            Object cachedData = RedisUtil.HashOps.hGet(cacheKey, String.valueOf(timePoint));
            if (cachedData != null) {
                performanceMonitor.incrementCounter("simple_cache_hit");
                return cachedData.toString();
            }

            performanceMonitor.incrementCounter("simple_cache_miss");

            // 缓存未命中，计算当前时间点数据
            String currentData = dataComputer.computeTimePointData(taskIds, timePoint);

            // 缓存当前数据
            RedisUtil.HashOps.hPut(cacheKey, String.valueOf(timePoint), currentData);

            // 设置缓存过期时间
            int millis = totalTime * 1000 * 2; // 2倍时长
            if (millis > 0) {
                RedisUtil.KeyOps.expire(cacheKey, millis, TimeUnit.MILLISECONDS);
            }

            // 只预计算下一个时间点，减少计算量
            if (timePoint < totalTime) {
                precomputeNextTimePoint(cacheKey, timePoint + 1, dataComputer, taskIds);
            }

            return currentData;

        } finally {
            performanceMonitor.endTimer("getTimePointDataSimple");
        }
    }

    /**
     * 预计算下一个时间点
     */
    private void precomputeNextTimePoint(String cacheKey, Integer nextTimePoint,
                                         TimePointDataComputer dataComputer, java.util.List<String> taskIds) {

        // 异步计算下一个时间点，但不阻塞当前请求
        new Thread(() -> {
            try {
                // 检查是否已经缓存
                Object cached = RedisUtil.HashOps.hGet(cacheKey, String.valueOf(nextTimePoint));
                if (cached == null) {
                    String data = dataComputer.computeTimePointData(taskIds, nextTimePoint);
                    RedisUtil.HashOps.hPut(cacheKey, String.valueOf(nextTimePoint), data);
                    log.debug("预计算完成，时间点: {}", nextTimePoint);
                }
            } catch (Exception e) {
                log.warn("预计算下一个时间点失败: {}", nextTimePoint, e);
            }
        }, "precompute-" + nextTimePoint).start();
    }
}
