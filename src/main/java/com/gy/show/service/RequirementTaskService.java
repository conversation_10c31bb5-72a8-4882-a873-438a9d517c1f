package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dto.BatchTaskDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.vo.TaskQueryVO;
import com.gy.show.enums.TaskScheduleStatusEnum;

import java.time.LocalDateTime;
import java.util.List;

public interface RequirementTaskService extends IService<RequirementTask> {

    RequirementTask addRequirementTask(RequirementTaskDTO requirementTaskDTO);

    void updateRequirementTask(RequirementTaskDTO requirementTaskDTO);

    void submitTask(String requirementId);

    List<RequirementTask> getTaskByRequirement(String id);

    Object queryTaskByEquipment(String generalId, String equipmentId);

    List<RequirementTaskDTO> queryScheduleTask();

    void changeTaskStatus(List<RequirementTaskDTO> tasks, TaskScheduleStatusEnum taskStatusEnum);

    IPage getAllRequirementTasks(IPage page, TaskQueryVO taskQueryVO);

    RequirementTaskDTO getRequirementTaskById(String id);

    Object getDataByTarget(String targetRelationId);

    /**
     * 查询当前正在执行的任务（当前时间在任务开始时间和结束时间之间）
     * @return 正在执行的任务列表
     */
    List<RequirementTaskDTO> queryCurrentExecutingTasks();

    /**
     * 批量更新任务状态
     * @param taskIds 任务ID列表
     * @param status 新状态
     */
    void batchUpdateTaskStatus(List<String> taskIds, Integer status);

    /**
     * 查询需要状态更新的任务（待执行和执行中的任务）
     * @return 需要状态更新的任务列表
     */
    List<RequirementTaskDTO> queryTasksForStatusUpdate();

    List<RequirementTaskDTO> listByIds(BatchTaskDTO batchTaskDTO);
}
