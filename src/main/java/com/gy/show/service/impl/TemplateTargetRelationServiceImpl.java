package com.gy.show.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.TemplateBusinessRelation;
import com.gy.show.entity.dos.TemplateTargetRelation;
import com.gy.show.mapper.TemplateBusinessRelationMapper;
import com.gy.show.mapper.TemplateTargetRelationMapper;
import com.gy.show.service.TemplateBusinessRelationService;
import com.gy.show.service.TemplateTargetRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TemplateTargetRelationServiceImpl extends ServiceImpl<TemplateTargetRelationMapper, TemplateTargetRelation> implements TemplateTargetRelationService {

    @Resource
    private TemplateTargetRelationMapper templateTargetRelationMapper;

}
