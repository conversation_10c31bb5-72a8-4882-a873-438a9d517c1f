package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataFile;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dos.TemplateBusinessRelation;
import com.gy.show.entity.dto.RequirementTemplateDTO;
import com.gy.show.mapper.DataFileMapper;
import com.gy.show.mapper.RequirementTemplateMapper;
import com.gy.show.mapper.TemplateBusinessRelationMapper;
import com.gy.show.service.RequirementTemplateService;
import com.gy.show.service.TemplateBusinessRelationService;
import com.gy.show.util.ConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TemplateBusinessRelationServiceImpl extends ServiceImpl<TemplateBusinessRelationMapper, TemplateBusinessRelation> implements TemplateBusinessRelationService {

    @Resource
    private TemplateBusinessRelationMapper templateBusinessRelationMapper;

}
