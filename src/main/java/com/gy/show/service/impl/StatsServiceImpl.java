package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.DataAreaDTO;
import com.gy.show.entity.dto.DataGeneralDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.enums.AreaTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.mapper.DataGeneralAreaRelationMapper;
import com.gy.show.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StatsServiceImpl implements StatsService {

    @Autowired
    private DataGeneralAreaRelationMapper dataGeneralAreaRelationMapper;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private SysDictionaryService sysDictionaryService;

    @Autowired
    private DataAreaService dataAreaService;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private RequirementInfoService requirementInfoService;
    
    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    public Map<String, Object> typeStats(String areaId) {
        return aggregateStats(areaId, null);
    }

    public Map<String, Integer> typeStatsByDataType(String areaId) {
        Map<String, Object> aggregatedStats = aggregateStats(areaId, DataGeneral::getDataType);

        Map<Integer, String> dic = loadDataTypeDictionary();
        // Convert aggregated stats to the required format
        Map<String, Integer> result = new HashMap<>();
        aggregateStats(result, dic, aggregatedStats);

        return result;
    }

    @Override
    public Map<String, Integer> areaStats(Integer areaType) {
        // 1 先根据资源域查询关联关系表
        List<DataGeneralAreaRelation> relations = dataGeneralAreaRelationMapper.queryAreaRelationByType(areaType);

        // 2 根据资源域分组查询
        Map<String, List<String>> groupByArea = relations.stream()
                .collect(Collectors.groupingBy(DataGeneralAreaRelation::getAreaId, Collectors.mapping(
                        DataGeneralAreaRelation::getGeneralId, Collectors.toList())));

        // 查询资源域
        List<String> areaIds = relations.stream()
                .map(DataGeneralAreaRelation::getAreaId)
                .distinct()
                .collect(Collectors.toList());
        List<DataArea> dataAreas = dataAreaService.list(Wrappers.<DataArea>lambdaQuery().eq(DataArea::getAreaType, areaType).in(DataArea::getId, areaIds));
        Map<String, List<DataArea>> groupByAreaId = dataAreas.stream()
                .collect(Collectors.groupingBy(DataArea::getId));

        Map<String, Integer> result = new HashMap<>();
        // 先查询资源域下有哪些分类
        for (Map.Entry<String, List<String>> entry : groupByArea.entrySet()) {
            String areaId = entry.getKey();
            List<DataArea> areas = groupByAreaId.get(areaId);

            List<String> generalIds = entry.getValue();
            List<DataGeneral> generals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getId, generalIds));

            // 计算每个generals下分类
            Map<String, Object> areaStats = aggregateStatsByGenerals(areaId, generals, null);
            for (Map.Entry<String, Object> statEntry : areaStats.entrySet()) {
                result.merge(areas.get(0).getAreaName(), (Integer) statEntry.getValue(), Integer::sum);
            }
        }
        return result;
    }

    private Map<Integer, String> loadDataTypeDictionary() {
        List<SysDictionary> sourceType = sysDictionaryService.getDicByType("sourceType");

        return sourceType.stream()
                .collect(Collectors.toMap(
                        SysDictionary::getDictValue,
                        SysDictionary::getDictName
                ));
    }

    public Map<String, Integer> areaDataTypeStats(Integer type) {
        List<DataArea> areas = dataAreaService.list(Wrappers.<DataArea>lambdaQuery()
                .eq(DataArea::getAreaType, type));

        List<String> areaIds = areas.stream()
                .map(DataArea::getId)
                .collect(Collectors.toList());

        // 1 先根据资源域查询关联关系表
        List<DataGeneralAreaRelation> relations = dataGeneralAreaRelationMapper.selectList(Wrappers.<DataGeneralAreaRelation>lambdaQuery()
                .in(DataGeneralAreaRelation::getAreaId, areaIds));

        // 2 根据资源域分组查询
        Map<String, List<String>> groupByArea = relations.stream()
                .collect(Collectors.groupingBy(DataGeneralAreaRelation::getAreaId, Collectors.mapping(
                        DataGeneralAreaRelation::getGeneralId, Collectors.toList())));

        Map<String, Integer> result = new HashMap<>();
        Map<Integer, String> dataTypeDictionary = loadDataTypeDictionary();

        // 先查询资源域下有哪些分类
        for (Map.Entry<String, List<String>> entry : groupByArea.entrySet()) {
            List<DataGeneral> generals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery()
                    .in(DataGeneral::getId, entry.getValue()));

            // 计算每个generals下分类
            Map<String, Object> areaStats = aggregateStatsByGenerals(entry.getKey(), generals, DataGeneral::getDataType);

            aggregateStats(result, dataTypeDictionary, areaStats);
        }
        return result;
    }

    @Override
    public Map<String, Integer> allTypeStats(Integer type) {
        List<DataArea> areas = dataAreaService.list(Wrappers.<DataArea>lambdaQuery()
                .eq(DataArea::getAreaType, type));

        List<Map<String, Object>> maps = new ArrayList<>();
        for (DataArea area : areas) {
            Map<String, Object> typeStats = typeStats(area.getId());
            maps.add(typeStats);
        }

        return maps.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> (Integer) entry.getValue(),
                        Integer::sum
                ));
    }

    /**
     * 统计每个资源域下资源的占用率
     * @return
     */
    @Override
    public Map<String, Double> areaIdle() {
        // 查询所有资源域
        List<DataArea> areas = dataAreaService.list(Wrappers.<DataArea>lambdaQuery()
                .eq(DataArea::getAreaType, AreaTypeEnum.EQUIPMENT.getCode()));

        List<Map<String, Object>> maps = new ArrayList<>();
        for (DataArea area : areas) {
            Map<String, Object> occupancyByArea = dataEquipmentOccupancyService.getOccupancyByArea(area.getId());
            maps.add(occupancyByArea);
        }

       return maps.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.collectingAndThen(
                                Collectors.averagingDouble(entry -> Double.parseDouble(entry.getValue().toString())),
                                average -> Math.round(average * 100.0) / 100.0
                        )
                ));
    }

    @Override
    public Map<String, Object> requirementStat() {
        // 查询需求总数
        int count = requirementInfoService.count();

        // 查询任务总数
        int taskCount = requirementTaskService.count();

        // 查询搭载目标总数
        int targetCount = taskTargetRelationService.count();

        // 查询搭载设备总数
        int equipmentCount = dataEquipmentOccupancyService.count();

        Map<String, Object> result = new HashMap<>();
        result.put("requirementCount", count);
        result.put("taskCount", taskCount);
        result.put("targetCount", targetCount);
        result.put("equipmentCount", equipmentCount);

        return result;
    }

    @Override
    public List<RequirementTaskDTO> targetBz(String requirementId) {
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(requirementId);

        List<RequirementTaskDTO> result = new ArrayList<>();
        // 查询任务对应的目标`
        for (RequirementTask task : tasks) {
            TaskTargetRelation targetRelation = taskTargetRelationService.getById(task.getTargetRelationId());
            DataGeneral dataGeneral = dataGeneralService.getById(targetRelation.getGeneralId());

            RequirementTaskDTO taskDTO = task.convert();

            TaskTargetRelationDTO convert = targetRelation.convert();
            convert.setDataTypeValue(dataGeneral.getTableComment());
            taskDTO.setTarget(convert);

            result.add(taskDTO);
        }

        return result;
    }

    @Override
    public Object realTimeAreaType(String requirementId) {
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(requirementId);

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());

        List<DataEquipmentOccupancy> dataEquipmentOccupancies = dataEquipmentOccupancyService.queryEquipmentByTaskIds(taskIds);

        Map<String, List<DataEquipmentOccupancy>> equipments = dataEquipmentOccupancies.stream()
                .collect(Collectors.groupingBy(DataEquipmentOccupancy::getAreaId));

        // 查询出参与调度的资源域
        List<String> areaIds = dataEquipmentOccupancies.stream()
                .map(DataEquipmentOccupancy::getAreaId)
                .collect(Collectors.toList());

        Collection<DataArea> dataAreas = dataAreaService.listByIds(areaIds);

        List<DataAreaDTO> dataAreaDTOS = dataAreas.stream()
                .map(area -> {
//                    List<DataGeneralAreaRelation> areaRelations = dataGeneralAreaRelationMapper.selectList(Wrappers.<DataGeneralAreaRelation>lambdaQuery()
//                            .eq(DataGeneralAreaRelation::getAreaId, area.getId()));
//
//                    List<String> generaIds = areaRelations.stream()
//                            .map(DataGeneralAreaRelation::getGeneralId)
//                            .collect(Collectors.toList());


                    List<DataEquipmentOccupancy> equipmentOccupancies = equipments.get(area.getId());

                    Map<String, List<DataEquipmentOccupancy>> equipmentsGroupByGeneralId = equipmentOccupancies.stream()
                            .collect(Collectors.groupingBy(DataEquipmentOccupancy::getGeneralId));

                    List<String> generalIds = equipmentOccupancies.stream()
                            .map(DataEquipmentOccupancy::getGeneralId)
                            .distinct()
                            .collect(Collectors.toList());

                    Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);
                    List<DataGeneralDTO> dataGeneralDTOS = dataGenerals.stream()
                            .map(dataGeneral -> {
//                                Map<String, Object> params = new HashMap<>();
//                                List<DataEquipmentOccupancy> occupancies = equipments.get(dataGeneral.getId());
//                                params.put("area_id", area.getId());
//                                Integer count = commonMapper.getCount(dataGeneral.getTableName(), params);
                                DataGeneralDTO generalDTO = dataGeneral.convert();
                                List<DataEquipmentOccupancy> dataEquipmentOccupancies1 = equipmentsGroupByGeneralId.get(dataGeneral.getId());

                                generalDTO.setCount(dataEquipmentOccupancies1.size());
                                return generalDTO;
                            }).filter(d -> d.getCount() > 0)
                            .collect(Collectors.toList());


                    DataAreaDTO areaDTO = area.convert();
                    areaDTO.setDataGenerals(dataGeneralDTOS);

                    return areaDTO;
                }).collect(Collectors.toList());

        Map<Integer, List<DataAreaDTO>> result = dataAreaDTOS.stream()
                .collect(Collectors.groupingBy(DataAreaDTO::getSource));

        return result;
    }

    private void aggregateStats(Map<String, Integer> result, Map<Integer, String> dataTypeDictionary, Map<String, Object> areaStats) {
        for (Map.Entry<String, Object> statEntry : areaStats.entrySet()) {
            String key = statEntry.getKey();
            Integer count = (Integer) statEntry.getValue();

            // Assuming the key format is "<dataType>:<tableComment>"
            String[] parts = key.split(":", 2);
            if (parts.length == 2) {
                Integer dataType = Integer.parseInt(parts[0]);
                String convertDataType = dataTypeDictionary.getOrDefault(dataType, "Unknown");

                result.merge(convertDataType, count, Integer::sum);
            }
        }
    }

    private Map<String, Object> aggregateStats(String areaId, Function<DataGeneral, Integer> groupByFunction) {
        List<String> generalIds = dataGeneralAreaRelationMapper.selectList(Wrappers.<DataGeneralAreaRelation>lambdaQuery()
                .eq(DataGeneralAreaRelation::getAreaId, areaId)).stream().map(DataGeneralAreaRelation::getGeneralId).collect(Collectors.toList());

        if (CollUtil.isEmpty(generalIds)) {
            return new HashMap<>();
        }
        //根据基础数据主表ID查询主表基础信息
        List<DataGeneral> generals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getId, generalIds));

        return aggregateStatsByGenerals(areaId, generals, groupByFunction);
    }

    private Map<String, Object> aggregateStatsByGenerals(String areaId, List<DataGeneral> generals, Function<DataGeneral, Integer> groupByFunction) {
        Map<Integer, List<DataGeneral>> generalsByType = generals.stream()
                .collect(Collectors.groupingBy(groupByFunction != null ? groupByFunction : general -> 0));

        // 异步查询所有表的count数据
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
        for (Map.Entry<Integer, List<DataGeneral>> entry : generalsByType.entrySet()) {
            List<DataGeneral> typeGenerals = entry.getValue();

            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> typeGenerals.stream()
                    .map(data -> countByTable(data, areaId))
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(
                            e -> (groupByFunction != null ? entry.getKey() + ":" : "") + e.getKey(),
                            Map.Entry::getValue,
                            (existing, replacement) -> existing // 如果有重复键，保留现有值
                    )), executor);

            futures.add(future);
        }

        // 汇总结果
        CompletableFuture<Map<String, Object>> resultMapFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .flatMap(map -> map.entrySet().stream())
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (existing, replacement) -> existing // 如果有重复键，保留现有值
                        )));

        try {
            return resultMapFuture.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException("汇总结果异常", e);
        }
    }

    private Map<String, Object> countByTable(DataGeneral general, String areaId) {
        Map<String, Object> args = new HashMap<>();
        if (areaId != null) {
            args.put("area_id", areaId);
        }
        Integer count = commonMapper.getCountByField(general.getTableName(), args, null);
        return new HashMap<String, Object>() {{
            put(general.getTableComment(), count);
        }};
    }
}
