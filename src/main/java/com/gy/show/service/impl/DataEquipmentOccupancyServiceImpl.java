package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.*;
import com.gy.show.entity.dto.DataEquipmentOccupancyDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.mapper.DataEquipmentOccupancyMapper;
import com.gy.show.service.*;
import com.gy.show.util.ConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataEquipmentOccupancyServiceImpl extends ServiceImpl<DataEquipmentOccupancyMapper, DataEquipmentOccupancy> implements DataEquipmentOccupancyService {

    @Autowired
    private DataEquipmentOccupancyMapper dataEquipmentOccupancyMapper;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private DataGeneralAreaRelationService dataGeneralAreaRelationService;

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Autowired
    private TaskTargetService taskTargetService;

    @Autowired
    private DataAreaService dataAreaService;

    public DataEquipmentOccupancy getDataEquipmentOccupancyById(String id) {
        return dataEquipmentOccupancyMapper.selectById(id);
    }

    public DataEquipmentOccupancy createDataEquipmentOccupancy(DataEquipmentOccupancy dataEquipmentOccupancy) {
        dataEquipmentOccupancyMapper.insert(dataEquipmentOccupancy);
        return dataEquipmentOccupancy;
    }

    public DataEquipmentOccupancy updateDataEquipmentOccupancy(String id, DataEquipmentOccupancy newDataEquipmentOccupancy) {
        newDataEquipmentOccupancy.setId(id);
        dataEquipmentOccupancyMapper.updateById(newDataEquipmentOccupancy);
        return newDataEquipmentOccupancy;
    }

    public void deleteDataEquipmentOccupancy(String id) {
        dataEquipmentOccupancyMapper.deleteById(id);
    }

    public IPage<DataEquipmentOccupancy> getAllDataEquipmentOccupancies(int page, int size, String keyword) {
        LambdaQueryWrapper<DataEquipmentOccupancy> queryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper.like(DataEquipmentOccupancy::getTaskId, keyword)
                    .or().like(DataEquipmentOccupancy::getEquipmentId, keyword));
        }
        queryWrapper.orderByDesc(DataEquipmentOccupancy::getCreateTime);
        return dataEquipmentOccupancyMapper.selectPage(new Page<>(page, size), queryWrapper);
    }

    @Override
    public Map<String, Object> getOccupancyByArea(String areaId) {
        // 查询该资源域下正在占用的设备
        List<DataEquipmentOccupancy> dataEquipmentOccupancies = list(Wrappers.<DataEquipmentOccupancy>lambdaQuery().eq(DataEquipmentOccupancy::getAreaId, areaId)
                .le(DataEquipmentOccupancy::getStartTime, LocalDateTime.now())
                .ge(DataEquipmentOccupancy::getEndTime, LocalDateTime.now())
                .orderByDesc(DataEquipmentOccupancy::getCreateTime)
                .last("limit 10"));


        // 将该资源域下所有类型先赋值为0
        List<String> generalIds = dataGeneralAreaRelationService.list(Wrappers.<DataGeneralAreaRelation>lambdaQuery().eq(DataGeneralAreaRelation::getAreaId, areaId))
                .stream().map(DataGeneralAreaRelation::getGeneralId).collect(Collectors.toList());

        if (CollUtil.isEmpty(generalIds)) {
            return new HashMap<>();
        }

        List<DataGeneral> generals = dataGeneralService.list(Wrappers.<DataGeneral>lambdaQuery().in(DataGeneral::getId, generalIds));
        Map<String, Object> result = generals.stream()
                .collect(Collectors.toMap(
                        DataGeneral::getTableComment,
                        v -> 0
                ));

        DecimalFormat df = new DecimalFormat("#.##");
        if (CollUtil.isNotEmpty(dataEquipmentOccupancies)) {
            Map<String, List<DataEquipmentOccupancy>> map = dataEquipmentOccupancies.stream()
                    .collect(Collectors.groupingBy(DataEquipmentOccupancy::getGeneralId));

            for (Map.Entry<String, List<DataEquipmentOccupancy>> entry : map.entrySet()) {
                String generalId = entry.getKey();
                DataGeneral dataGeneral = dataGeneralService.getById(generalId);

                // 获取该类型下总设备数量
                Integer total = commonMapper.getCount(dataGeneral.getTableName(), null);

                // 计算占用比例
                Double percent = (double) entry.getValue().size() / total;


                result.put(dataGeneral.getTableComment(), df.format(percent));
            }
        }
        return result;
    }

    @Override
    public IPage<DataEquipmentOccupancyDTO> getEquipmentByTask(int page, int size, String taskId) {
        IPage<DataEquipmentOccupancy> occupancyIPage = page(new Page<>(page, size), Wrappers.<DataEquipmentOccupancy>lambdaQuery().eq(DataEquipmentOccupancy::getTaskId, taskId)
                .orderByDesc(DataEquipmentOccupancy::getCreateTime));
        IPage<DataEquipmentOccupancyDTO> equipments = ConvertUtil.buildPage(occupancyIPage);

        // 主表ID集合
        List<String> generalIds = equipments.getRecords()
                .stream().map(DataEquipmentOccupancyDTO::getGeneralId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(generalIds)) {
            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(generalIds);
            Map<String, List<DataGeneral>> groupById = dataGenerals.stream()
                    .collect(Collectors.groupingBy(DataGeneral::getId));

            for (DataEquipmentOccupancyDTO record : equipments.getRecords()) {
                List<DataGeneral> generalList = groupById.get(record.getGeneralId());
                if (CollUtil.isNotEmpty(generalList)) {
                    DataGeneral general = generalList.get(0);

                    Map<String, Object> equipmentDetail = commonMapper.getOne(general.getTableName(), record.getEquipmentId());

                    String areaId = equipmentDetail.get("areaId").toString();
                    DataArea dataArea = dataAreaService.getById(areaId);

                    record.setAreaName(dataArea.getAreaName());
                    record.setEquipmentDetail(equipmentDetail);
                    record.setDataType(general.getDataType());
                    record.setType(general.getTableComment());
                }
            }
        }

        return equipments;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByTaskId(String taskId) {
        remove(Wrappers.<DataEquipmentOccupancy>lambdaQuery().eq(DataEquipmentOccupancy::getTaskId, taskId));
    }

    @Override
    public List<DataEquipmentOccupancy> queryEquipmentByTaskIds(List<String> taskIds) {
        return list(Wrappers.<DataEquipmentOccupancy>lambdaQuery().in(DataEquipmentOccupancy::getTaskId, taskIds));
    }

    @Override
    public Object getDataByEquipment(String equipmentId, String requirementId) {
        List<RequirementTask> tasks = requirementTaskService.getTaskByRequirement(requirementId);

        List<String> taskIds = tasks.stream()
                .map(RequirementTask::getId)
                .collect(Collectors.toList());

        List<DataEquipmentOccupancy> dataEquipmentOccupancies = list(Wrappers.<DataEquipmentOccupancy>lambdaQuery().in(DataEquipmentOccupancy::getTaskId, taskIds)
                .eq(DataEquipmentOccupancy::getEquipmentId, equipmentId));

        Map<String, List<DataEquipmentOccupancy>> groupByTaskId = dataEquipmentOccupancies.stream()
                .collect(Collectors.groupingBy(DataEquipmentOccupancy::getTaskId));

        // 查询设备关联的目标列表
        List<String> relationIds = tasks.stream()
                .map(RequirementTask::getTargetRelationId)
                .collect(Collectors.toList());

        Collection<TaskTargetRelation> taskTargetRelations = taskTargetService.listByIds(relationIds);


        List<RequirementTaskDTO> taskDTOs = tasks.stream()
                .map(task -> {
                    List<DataEquipmentOccupancy> occupancies = groupByTaskId.get(task.getId());
                    RequirementTaskDTO taskDTO = task.convert();
                    taskDTO.setOccupancies(occupancies);
                    return taskDTO;
                })
                .filter(t -> CollUtil.isNotEmpty(t.getOccupancies()))
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();

        result.put("tasks", taskDTOs);
        result.put("targets", taskTargetRelations);

        return result;
    }

    @Override
    public Map<String, List<DataEquipmentOccupancyDTO>> getEquipmentByTaskIds(List<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return new HashMap<>();
        }

        // 批量查询设备占用信息
        List<DataEquipmentOccupancy> occupancies = list(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                .in(DataEquipmentOccupancy::getTaskId, taskIds)
                .orderByDesc(DataEquipmentOccupancy::getCreateTime));

        if (CollUtil.isEmpty(occupancies)) {
            return new HashMap<>();
        }

        // 转换为DTO
        List<DataEquipmentOccupancyDTO> equipmentDTOs = occupancies.stream()
                .map(DataEquipmentOccupancy::convert)
                .collect(Collectors.toList());

        // 获取主表ID集合
        List<String> generalIds = equipmentDTOs.stream()
                .map(DataEquipmentOccupancyDTO::getGeneralId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询主表信息并按ID分组
        Map<String, List<DataGeneral>> groupById = new HashMap<>();
        if (CollUtil.isNotEmpty(generalIds)) {
            Collection<DataGeneral> generals = dataGeneralService.listByIds(generalIds);
            groupById = generals.stream()
                    .collect(Collectors.groupingBy(DataGeneral::getId));
        }

        // 填充设备详情信息
        for (DataEquipmentOccupancyDTO record : equipmentDTOs) {
            List<DataGeneral> generalList = groupById.get(record.getGeneralId());
            if (CollUtil.isNotEmpty(generalList)) {
                DataGeneral general = generalList.get(0);

                try {
                    Map<String, Object> equipmentDetail = commonMapper.getOne(general.getTableName(), record.getEquipmentId());

                    if (equipmentDetail != null && equipmentDetail.get("areaId") != null) {
                        String areaId = equipmentDetail.get("areaId").toString();
                        DataArea dataArea = dataAreaService.getById(areaId);

                        if (dataArea != null) {
                            record.setAreaName(dataArea.getAreaName());
                        }
                    }

                    record.setEquipmentDetail(equipmentDetail);
                    record.setDataType(general.getDataType());
                    record.setType(general.getTableComment());
                } catch (Exception e) {
                    log.warn("Failed to get equipment detail for equipmentId: {}, tableName: {}",
                            record.getEquipmentId(), general.getTableName(), e);
                }
            }
        }

        // 按任务ID分组返回
        return equipmentDTOs.stream()
                .collect(Collectors.groupingBy(DataEquipmentOccupancyDTO::getTaskId));
    }


}
