package com.gy.show.service.impl;

import com.gy.show.config.PerformanceConfig;
import com.gy.show.service.CacheWarmupService;
import com.gy.show.service.SituationService;
import com.gy.show.service.TimePointDataComputer;
import com.gy.show.runnner.PerformanceMonitor;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.gy.show.constants.CacheConstant.POINT_CACHE_PREFIX;

/**
 * 缓存预热服务实现
 */
@Slf4j
@Service
public class CacheWarmupServiceImpl implements CacheWarmupService {

    @Autowired
    private SituationService situationService;

    @Autowired
    private TimePointDataComputer timePointDataComputer;

    @Autowired
    private PerformanceConfig performanceConfig;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    // 预热任务状态管理
    private final ConcurrentHashMap<String, WarmupTask> warmupTasks = new ConcurrentHashMap<>();

    @Override
    public String startWarmup(List<String> taskIds) {
        String warmupId = generateWarmupId();

        log.info("启动缓存预热，预热ID: {}, 任务数量: {}", warmupId, taskIds.size());

        // 创建预热任务
        WarmupTask warmupTask = new WarmupTask(warmupId, taskIds);
        warmupTasks.put(warmupId, warmupTask);

        // 异步执行预热
        CompletableFuture.runAsync(() -> executeWarmup(warmupTask));

        return warmupId;
    }

    @Override
    public Map<String, Object> getWarmupStatus(String warmupId) {
        WarmupTask task = warmupTasks.get(warmupId);
        if (task == null) {
            throw new IllegalArgumentException("预热任务不存在: " + warmupId);
        }

        Map<String, Object> status = new HashMap<>();
        status.put("warmupId", warmupId);
        status.put("status", task.getStatus());
        status.put("progress", task.getProgress());
        status.put("totalTime", task.getTotalTime());
        status.put("currentTime", task.getCurrentTime());
        status.put("startTime", task.getStartTime());
        status.put("taskIds", task.getTaskIds());
        status.put("errorMessage", task.getErrorMessage());

        return status;
    }

    @Override
    public void stopWarmup(String warmupId) {
        WarmupTask task = warmupTasks.get(warmupId);
        if (task != null) {
            task.stop();
            log.info("预热任务已停止: {}", warmupId);
        }
    }

    @Override
    public List<Map<String, Object>> getWarmupList() {
        List<Map<String, Object>> result = new ArrayList<>();

        for (WarmupTask task : warmupTasks.values()) {
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("warmupId", task.getWarmupId());
            taskInfo.put("status", task.getStatus());
            taskInfo.put("progress", task.getProgress());
            taskInfo.put("startTime", task.getStartTime());
            taskInfo.put("taskCount", task.getTaskIds().size());

            result.add(taskInfo);
        }

        return result;
    }

    @Override
    public void clearCache(String bzId) {
        String cacheKey = POINT_CACHE_PREFIX + bzId;
        RedisUtil.KeyOps.delete(cacheKey);
        log.info("已清除缓存: {}", cacheKey);
    }

    /**
     * 执行预热任务
     */
    private void executeWarmup(WarmupTask warmupTask) {
        try {
            warmupTask.setStatus("RUNNING");

            // 获取总时长
            Integer totalTime = getTotalTime(warmupTask.getTaskIds());
            warmupTask.setTotalTime(totalTime);

            if (totalTime == 0) {
                warmupTask.setStatus("COMPLETED");
                return;
            }

            String cacheKey = POINT_CACHE_PREFIX + warmupTask.getWarmupId();
            int batchSize = performanceConfig.getProgressiveBatchSize();
            int delay = performanceConfig.getProgressiveDelayMs();

            log.info("开始预热计算，总时长: {}秒, 批次大小: {}", totalTime, batchSize);

            // 分批计算所有时间点
            for (int startPoint = 0; startPoint <= totalTime && !warmupTask.isStopped(); startPoint += batchSize) {
                int endPoint = Math.min(startPoint + batchSize - 1, totalTime);

                // 计算这一批的数据
                Map<String, String> batchData = new HashMap<>();
                for (int timePoint = startPoint; timePoint <= endPoint && !warmupTask.isStopped(); timePoint++) {
                    String data = timePointDataComputer.computeTimePointData(warmupTask.getTaskIds(), timePoint);
                    batchData.put(String.valueOf(timePoint), data);
                    warmupTask.setCurrentTime(timePoint);
                }

                // 批量写入缓存
                if (!batchData.isEmpty()) {
                    RedisUtil.HashOps.hPutAll(cacheKey, batchData);
                }

                // 更新进度
                double progress = (double) endPoint / totalTime * 100;
                warmupTask.setProgress(progress);

                // 短暂延迟
                if (delay > 0) {
                    Thread.sleep(delay);
                }

                log.debug("预热进度: {}/{} ({}%)", endPoint, totalTime, String.format("%.2f", progress));
            }

            // 设置缓存过期时间
            int millis = totalTime * 1000 * performanceConfig.getPointDataCacheMultiplier();
            if (millis > 0) {
                RedisUtil.KeyOps.expire(cacheKey, millis, TimeUnit.MILLISECONDS);
            }

            if (warmupTask.isStopped()) {
                warmupTask.setStatus("STOPPED");
            } else {
                warmupTask.setStatus("COMPLETED");
                warmupTask.setProgress(100.0);
            }

            log.info("预热任务完成: {}", warmupTask.getWarmupId());

        } catch (Exception e) {
            log.error("预热任务执行失败: " + warmupTask.getWarmupId(), e);
            warmupTask.setStatus("FAILED");
            warmupTask.setErrorMessage(e.getMessage());
        }
    }

    /**
     * 获取总时长
     */
    private Integer getTotalTime(List<String> taskIds) {
        try {
            // 这里需要调用situationService的getTotalTime方法
            // 为了简化，暂时返回一个默认值
            return 3600; // 1小时
        } catch (Exception e) {
            log.error("获取总时长失败", e);
            return 0;
        }
    }

    /**
     * 生成预热ID
     */
    private String generateWarmupId() {
        return "warmup_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
                + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 预热任务类
     */
    private static class WarmupTask {
        private final String warmupId;
        private final List<String> taskIds;
        private final LocalDateTime startTime;
        private volatile String status = "PENDING";
        private volatile double progress = 0.0;
        private volatile Integer totalTime = 0;
        private volatile Integer currentTime = 0;
        private volatile String errorMessage;
        private final AtomicBoolean stopped = new AtomicBoolean(false);

        public WarmupTask(String warmupId, List<String> taskIds) {
            this.warmupId = warmupId;
            this.taskIds = new ArrayList<>(taskIds);
            this.startTime = LocalDateTime.now();
        }

        // Getters and setters
        public String getWarmupId() { return warmupId; }
        public List<String> getTaskIds() { return taskIds; }
        public LocalDateTime getStartTime() { return startTime; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public double getProgress() { return progress; }
        public void setProgress(double progress) { this.progress = progress; }
        public Integer getTotalTime() { return totalTime; }
        public void setTotalTime(Integer totalTime) { this.totalTime = totalTime; }
        public Integer getCurrentTime() { return currentTime; }
        public void setCurrentTime(Integer currentTime) { this.currentTime = currentTime; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public boolean isStopped() { return stopped.get(); }
        public void stop() { stopped.set(true); }
    }
}
