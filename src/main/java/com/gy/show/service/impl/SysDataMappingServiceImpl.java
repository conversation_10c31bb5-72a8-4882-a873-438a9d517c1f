package com.gy.show.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.constants.CacheConstant;
import com.gy.show.entity.dos.SysDataMapping;
import com.gy.show.mapper.SysDataMappingMapper;
import com.gy.show.service.SysDataMappingService;
import com.gy.show.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SysDataMappingServiceImpl extends ServiceImpl<SysDataMappingMapper, SysDataMapping> implements SysDataMappingService {
    @Override
    public List<SysDataMapping> queryDataMapping(List<Integer> dataType) {
        List<SysDataMapping> result;

        // 查询缓存
        String dataStr = RedisUtil.StringOps.get(CacheConstant.DATA_MAPPING);
        if (StringUtils.isNotBlank(dataStr)) {
            result = JSON.parseArray(dataStr, SysDataMapping.class);

            Map<Integer, List<SysDataMapping>> collect = result.stream().filter(r -> dataType.contains(r.getDataType())).collect(Collectors.groupingBy(SysDataMapping::getDataType));
            // 查询不存在
            if (collect.size() <= dataType.size()) {
                result = queryCache(dataType);
            }
        } else {
            result = queryCache(dataType);
        }
        return result;
    }

    @Override
    public SysDataMapping getByDataValue(String dataValue) {
        return getOne(Wrappers.<SysDataMapping>lambdaQuery().like(SysDataMapping::getDataValue, dataValue));
    }

    private List<SysDataMapping> queryCache(List<Integer> dataType) {
        LambdaQueryWrapper<SysDataMapping> queryWrapper = Wrappers.lambdaQuery();

        if (dataType != null) {
            queryWrapper.in(SysDataMapping::getDataType, dataType);
        }
        List<SysDataMapping> result = list(queryWrapper);

        // 存缓存
        RedisUtil.StringOps.setEx(CacheConstant.DATA_MAPPING, JSON.toJSONString(result), 24, TimeUnit.HOURS);
        return result;
    }
}
