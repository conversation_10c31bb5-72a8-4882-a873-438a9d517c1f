package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dos.ScheduleLog;
import com.gy.show.entity.dto.ScheduleLogDTO;
import com.gy.show.mapper.ScheduleLogMapper;
import com.gy.show.service.RequirementTaskService;
import com.gy.show.service.ScheduleLogService;
import com.gy.show.util.ConvertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ScheduleLogServiceImpl extends ServiceImpl<ScheduleLogMapper, ScheduleLog> implements ScheduleLogService {

    @Autowired
    private RequirementTaskService requirementTaskService;

    @Override
    public Object pageByEquipmentId(IPage page, String equipmentId) {
        IPage pageData = page(page, Wrappers.<ScheduleLog>lambdaQuery().eq(ScheduleLog::getEquipmentId, equipmentId));
        List<ScheduleLog> records = pageData.getRecords();

        List<ScheduleLogDTO> scheduleLogDTOS = records.stream()
                .map(log -> {
                    ScheduleLogDTO logDTO = log.convert();

                    RequirementTask task = requirementTaskService.getById(logDTO.getTaskId());
                    if (task != null) {
                        logDTO.setTaskName(task.getTaskName());
                    }

                    return logDTO;
                })
                .collect(Collectors.toList());

        IPage buildPage = ConvertUtil.buildPage(pageData);
        buildPage.setRecords(scheduleLogDTOS);
        return buildPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveScheduleLog(ScheduleLogDTO scheduleLogDTO) {
        ScheduleLog scheduleLog = scheduleLogDTO.convert();

        save(scheduleLog);
    }
}
