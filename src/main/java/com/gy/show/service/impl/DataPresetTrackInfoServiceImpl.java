package com.gy.show.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataPresetTrack;
import com.gy.show.entity.dos.DataPresetTrackInfo;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.TaskTargetRelation;
import com.gy.show.entity.dto.DataPresetTrackDTO;
import com.gy.show.entity.dto.DataPresetTrackInfoDTO;
import com.gy.show.mapper.DataPresetTrackInfoMapper;
import com.gy.show.service.DataPresetTrackInfoService;
import com.gy.show.service.DataPresetTrackService;
import com.gy.show.service.RequirementTargetTrackService;
import com.gy.show.service.TaskTargetRelationService;
import com.gy.show.util.ConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataPresetTrackInfoServiceImpl extends ServiceImpl<DataPresetTrackInfoMapper, DataPresetTrackInfo> implements DataPresetTrackInfoService {

    @Autowired
    private DataPresetTrackService dataPresetTrackService;

    @Autowired
    private RequirementTargetTrackService requirementTargetTrackService;

    @Autowired
    private TaskTargetRelationService taskTargetRelationService;

    @Override
    public IPage<DataPresetTrackInfoDTO> pageTrackInfo(IPage page, String keyword) {
        IPage<DataPresetTrackInfo> resultPage = page(page, Wrappers.<DataPresetTrackInfo>lambdaQuery()
                .like(StringUtils.isNotBlank(keyword), DataPresetTrackInfo::getName, keyword));

        IPage<DataPresetTrackInfoDTO> result = ConvertUtil.buildPage(resultPage);
        if (CollUtil.isNotEmpty(resultPage.getRecords())) {
            List<String> infoIds = resultPage.getRecords()
                    .stream()
                    .map(DataPresetTrackInfo::getId)
                    .collect(Collectors.toList());

            List<DataPresetTrack> presetTracks = dataPresetTrackService.list(Wrappers.<DataPresetTrack>lambdaQuery().in(DataPresetTrack::getPresetId, infoIds));

            Map<String, List<DataPresetTrack>> group = presetTracks.stream()
                    .collect(Collectors.groupingBy(DataPresetTrack::getPresetId));

            result.getRecords().forEach(t -> {
                List<DataPresetTrack> tracks = group.get(t.getId());
                t.setTracks(tracks);
            });
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTrackInfo(String id) {
        // 删除信息表
        removeById(id);

        // 删除航迹点表
        dataPresetTrackService.remove(Wrappers.<DataPresetTrack>lambdaQuery().eq(DataPresetTrack::getPresetId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataPresetTrackInfo createPresetTrackInfo(DataPresetTrackInfoDTO dataPresetTrackInfoDTO) {
        DataPresetTrackInfo trackInfo = dataPresetTrackInfoDTO.convert();

        save(trackInfo);

        return trackInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DataPresetTrackInfo updatePresetTrackInfo(DataPresetTrackInfoDTO dataPresetTrackInfoDTO) {
        DataPresetTrackInfo trackInfo = dataPresetTrackInfoDTO.convert();

        updateById(trackInfo);

        return trackInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTaskTrack(String presetId) {
        // 查询有哪些任务被关联了
        List<RequirementTargetTrack> targetTracks = requirementTargetTrackService.list(Wrappers.<RequirementTargetTrack>lambdaQuery()
                .eq(RequirementTargetTrack::getPresetId, presetId));

        if (CollUtil.isEmpty(targetTracks)) {
            log.info("暂无关联任务需要更新");
            return;
        }

        // 查询新航迹
        DataPresetTrackDTO trackDTO = dataPresetTrackService.pagePresetTracks(presetId);
        List<DataPresetTrack> newTrack = trackDTO.getTracks();

        // 按目标id进行分组
        Map<String, List<RequirementTargetTrack>> groupByRelation = targetTracks.stream()
                .collect(Collectors.groupingBy(RequirementTargetTrack::getRelationId));

        for (Map.Entry<String, List<RequirementTargetTrack>> entry : groupByRelation.entrySet()) {
            String relationId = entry.getKey();

            // 获取目标快照表
            TaskTargetRelation targetRelation = taskTargetRelationService.getById(relationId);

            targetRelation.setLongitude(newTrack.get(0).getLongitude());
            targetRelation.setLatitude(newTrack.get(0).getLatitude());
            targetRelation.setAltitude(newTrack.get(0).getAltitude());

            taskTargetRelationService.updateById(targetRelation);

            // 删除旧航迹
            requirementTargetTrackService.remove(Wrappers.<RequirementTargetTrack>lambdaQuery()
                    .eq(RequirementTargetTrack::getRelationId, relationId));

            List<RequirementTargetTrack> newTargetTrack = newTrack.stream()
                    .map(track -> {
                        RequirementTargetTrack targetTrack = new RequirementTargetTrack();
                        BeanUtil.copyProperties(track, targetTrack);

                        targetTrack.setRelationId(relationId);
                        targetTrack.setPresetId(presetId);
                        return targetTrack;
                    }).collect(Collectors.toList());

            // 保存新航迹
            requirementTargetTrackService.saveBatch(newTargetTrack);

            log.info("relationId:{}的航迹已经更新，presetId:{}", relationId, presetId);
        }
    }
}
