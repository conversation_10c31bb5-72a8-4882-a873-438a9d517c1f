// RequirementEquipmentTrackService.java

package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.RequirementEquipmentTrack;
import com.gy.show.mapper.RequirementEquipmentTrackMapper;
import com.gy.show.service.RequirementEquipmentTrackService;
import org.springframework.stereotype.Service;

@Service
public class RequirementEquipmentTrackServiceImpl extends ServiceImpl<RequirementEquipmentTrackMapper, RequirementEquipmentTrack> implements RequirementEquipmentTrackService {

    @Override
    public RequirementEquipmentTrack getById(String id) {
        return null;
    }

    public IPage<RequirementEquipmentTrack> list(Integer pageNum, Integer pageSize, String keyword) {
        Page<RequirementEquipmentTrack> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectPage(page, lambdaQuery()
                .like(RequirementEquipmentTrack::getRelationId, keyword)
                .orderByDesc(RequirementEquipmentTrack::getCreateTime));
    }

    public void add(RequirementEquipmentTrack requirementEquipmentTrack) {
        save(requirementEquipmentTrack);
    }

    public void update(RequirementEquipmentTrack requirementEquipmentTrack) {
        updateById(requirementEquipmentTrack);
    }

    public void delete(String id) {
        removeById(id);
    }
}
