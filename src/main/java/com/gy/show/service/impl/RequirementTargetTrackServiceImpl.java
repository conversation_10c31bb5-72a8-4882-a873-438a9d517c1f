package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.mapper.RequirementTargetTrackMapper;
import com.gy.show.service.RequirementTargetTrackService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class RequirementTargetTrackServiceImpl extends ServiceImpl<RequirementTargetTrackMapper, RequirementTargetTrack> implements RequirementTargetTrackService {

    @Override
    public List<RequirementTargetTrack> queryTrackByIds(List<String> relationIds) {
        if (CollUtil.isEmpty(relationIds)) return Collections.EMPTY_LIST;
        return list(Wrappers.<RequirementTargetTrack>lambdaQuery().in(RequirementTargetTrack::getRelationId, relationIds));
    }
}
