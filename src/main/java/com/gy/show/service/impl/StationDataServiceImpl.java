package com.gy.show.service.impl;

import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.LogTypeEnum;
import com.gy.show.enums.TargetMappingEnum;
import com.gy.show.runnner.InterBootstrapRunner;
import com.gy.show.entity.dto.external.StationDataDTO;
import com.gy.show.service.*;
import com.gy.show.socket.client.StationTcpClient;
import com.gy.show.socket.message.StationSpaceDataHead;
import com.gy.show.socket.message.StationSpaceTelemetry;
import com.gy.show.util.DateUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.socket.DatagramPacket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Random;

@Slf4j
@Service
public class StationDataServiceImpl extends StationServiceImpl implements StationDataService {

    @Autowired
    private InterBootstrapRunner runner;

    @Autowired
    private ThreadPoolTaskExecutor pool;

    @Override
    public ByteBuf writeHeadMessage(int length, Integer target) {
        ByteBuf byteBuf = Unpooled.buffer();

        // Time 4 单位0.1ms 当日的积秒
        byteBuf.writeIntLE((int) DateUtil.getDaySeconds());

        // Task ID 4 来源于操控端信宿  0x75 0x76 0x77 0x78
        byteBuf.writeIntLE(target);

        // Data Type ID  4
        byteBuf.writeIntLE(0x00210102);

        // Device ID 4
        byteBuf.writeIntLE(0);

        // Res1 4
        byteBuf.writeIntLE(0);

        // TODO 测试使用，实际环境需打开以下注释 Length 4 12是固定长度 4 是内容的固定头部
        byteBuf.writeIntLE(length + 12 + 4);
//        byteBuf.writeIntLE(28 + 12 + 4);

        return byteBuf;
    }

    @Override
    public void writeRemoteControl(ByteBuf byteBuf, byte[] content) {
        // 标志码 1
        byteBuf.writeByte(0xF);

        // 小环标识 1
        byteBuf.writeByte(0xF);

        // 发令位数 4 nB * 8
//        byteBuf.writeIntLE((content.length + 4) * 8);
        byteBuf.writeIntLE(32 * 8);

        // 发遥控日期 2 相对于2000年1月1日的积日
        byteBuf.writeShortLE((short) DateUtil.daysSince2000());

        // 发遥控时间 4
        byteBuf.writeIntLE(0xFFFFFFFF);

        // 遥控帧内容 0x0203
        byteBuf.writeIntLE(0x030290EB);

        // TODO 最终联调时后面打开以下注释, 现在先写死
        byteBuf.writeBytes(content);

//        Random random = new Random();
//        for (int i = 0; i < 28; i++) {
//            byteBuf.writeByte(1);
//        }
    }

    @Override
    public void remoteControlCommand(StationDataDTO stationDataDTO) {
        List<Integer> targetIds = stationDataDTO.getTargetIds();
        List<String> stationIds = stationDataDTO.getStationIds();

        for (String stationId : stationIds) {
            for (Integer targetId : targetIds) {
                byte[] content;
                if (stationDataDTO.getContent() == null || stationDataDTO.getContent().length <= 0) {
                    content = new byte[32];
                } else {
                    content = stationDataDTO.getContent();
                }

                // 头
                ByteBuf byteBuf = writeHeadMessage(content.length, targetId);

                // 报文体
                writeRemoteControl(byteBuf, content);

                // 发送信息
                Map<String, StationTcpClient> tcpSenders = runner.getTcpSenders();
                StationTcpClient sender = tcpSenders.get(ClientTypeEnum.SPACE_STATUS.getMessage() + stationId);
                sender.sendMessage(byteBuf);
                log.info("向航天测控站：{}，发送遥控指令成功", stationId);
            }
        }
    }

    @Override
    public StationSpaceDataHead parseMessageHead(Object msg) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        StationSpaceDataHead head = new StationSpaceDataHead();
        // Time 4
        int time = byteBuf.readIntLE();
        head.setTime(time);

        // Task ID 4
        int taskId = byteBuf.readIntLE();
        head.setTaskId(taskId);

        // Data Type ID  4
        int typeId = byteBuf.readIntLE();
        head.setTypeId(typeId);

        // Device ID 4
        int deviceId = byteBuf.readIntLE();
        head.setDeviceId(deviceId);

        // Res1 4
        int res1 = byteBuf.readIntLE();
        head.setRes1(res1);

        // length 4
        int length = byteBuf.readIntLE();
        head.setLength(length);

        return head;
    }

    /**
     * @param msg
     * @param head
     * @param id
     * @param taskId
     */
    @Override
    public void parseMessageBody(Object msg, StationSpaceDataHead head, String id, Integer taskId) {
        DatagramPacket packet = (DatagramPacket) msg;
        ByteBuf byteBuf = packet.content();

        // 发送日志
        sendStationMessage2Front(byteBuf, LogTypeEnum.INFO.getMessage(), id.equals("_ka") ? "接收到天基综合测控节点遥测数据" : "接收到空基综合测控节点遥测数据", id);

        StationSpaceTelemetry telemetry = new StationSpaceTelemetry();

        // 标志码 1
        byte flag = byteBuf.readByte();
        telemetry.setFlag(flag);

        // 遥测数据采样时标 4 TODO 需要一个映射表来存储目标与设备之间的关系
        int ty = byteBuf.readIntLE();
        telemetry.setTy(ty);

        // 跳过 1A CF FC 1D
        byteBuf.skipBytes(4);

        // data 数据
//        byte[] data = new byte[byteBuf.readableBytes()];
//        byteBuf.readBytes(data);
//        telemetry.setData(data);
//
//        log.info("解析航天测控站遥测数据报文体：{}", telemetry);

        // 这里暂时先给无人艇发送遥测数据
//        externalDataService.sendBusinessData(data, ClientTypeEnum.SHIP);

        // 无人车遥测数据，需要判断帧头
//        ClientTypeEnum clientTypeEnum = parseCarDataType(data);

        log.info("剩余遥测信息长度:{}", byteBuf.readableBytes());
        byte[] data;
        if (taskId == 117 || taskId == 118) {
            data = new byte[128];
        } else {
            data = new byte[70];
        }

        byteBuf.readBytes(data);

        // 发送遥测数据
        sendBusinessData(data, id, taskId);

    }

    @Override
    public void testCarBz() {
//        byte[] data = {0x50, 0x45, 0x54, 0x43, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x02, 0x00, 0x00};
        byte[] data = {(byte) 0xEE, (byte) 0xFF};

        pool.execute(() -> {
            while (true) {
                byte[] zj = packageZj();

//        ClientTypeEnum clientTypeEnum = parseCarDataType(data);
                externalDataService.sendBusinessData(zj, ClientTypeEnum.CAR.getMessage());

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });

    }

    @Override
    public void testCarDp() {
        pool.execute(() -> {
            while (true) {
                byte[] zj = packageDp();

//        ClientTypeEnum clientTypeEnum = parseCarDataType(data);
                externalDataService.sendBusinessData(zj, ClientTypeEnum.CAR.getMessage());

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });

    }

    private byte[] packageDp() {
        ByteBuf byteBuf = Unpooled.buffer();
        Random random = new Random();

        int[] choices = {1, 2, 4};

        // 4
        byteBuf.writeIntLE(0x43544550);

        // 4
        byteBuf.writeIntLE(0);

        // 4
        byteBuf.writeIntLE(0);

        // 4
        byteBuf.writeIntLE(0x0221);

        // 1
        byteBuf.writeByte(0);

        // 1
        byteBuf.writeByte(0);

        // 1
        byteBuf.writeByte(0);

        // 1
        byteBuf.writeByte(0);

        // 1
        byteBuf.writeByte(0);

        // 1
        byteBuf.writeByte(100);

        // 1
        byteBuf.writeByte(0);

        // 1 随机 1 2 4
        int idx = random.nextInt(choices.length);
        byteBuf.writeByte(choices[idx]);

        // 4
        byteBuf.writeBytes(new byte[4]);

        byte[] data = new byte[28];

        byteBuf.readBytes(data);

        return data;

    }

    private byte[] packageZj() {
        ByteBuf byteBuf = Unpooled.buffer();
        Random random = new Random();

        // 帧头 2
        byteBuf.writeShortLE(0xFFEE);

        // 帧类型 2
        byteBuf.writeShortLE(0x0033);

        // 帧长 1
        byteBuf.writeByte(0x3D);

        // 俯仰角 1
        byteBuf.writeByte(random.nextInt(91));

        // 倾斜角 1
        byteBuf.writeByte(random.nextInt(91));

        // 航向角 2
        byteBuf.writeShortLE(random.nextInt(91));

        // 经度 4
        byteBuf.writeIntLE(0);

        // 纬度 4
        byteBuf.writeIntLE(0);

        // 跟踪偏差 2
        byteBuf.writeShortLE(0);

        // 控制状态 2
        byteBuf.writeShortLE(0);

        // 实时速度 4
        byteBuf.writeIntLE(0);

        // 目标跟踪类型 1
        byteBuf.writeByte(1);

        // 目标跟踪状态 1
        byteBuf.writeByte(0);

        byteBuf.writeBytes(new byte[34]);

        byte[] data = new byte[61];

        byteBuf.readBytes(data);

        return data;
    }

    /**
     * 业务数据，业务数据只需要转发无需解析
     *  0x43544550 小端 底盘发送到20033
     *  其它的都发给自主 20034
     * @param data
     */
    private ClientTypeEnum parseCarDataType(byte[] data) {
        byte[] magic = {0x43, 0x54, 0x45, 0x50};

        boolean match = true;

        for (int i = 0; i < magic.length; i++) {
            if (data[i] != magic[i]) {
                match = false;
                break;
            }
        }

        ClientTypeEnum clientTypeEnum = null;
//        if (match) {
//            // 匹配 发送到20033端口 底盘
//            clientTypeEnum = ClientTypeEnum.CAR2;
//        } else {
//            // 不匹配 发送到20034端口 自主
//            clientTypeEnum = ClientTypeEnum.CAR3;
//        }

        return clientTypeEnum;
    }

}
