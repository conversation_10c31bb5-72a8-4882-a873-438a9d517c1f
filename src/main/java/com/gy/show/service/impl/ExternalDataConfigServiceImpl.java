package com.gy.show.service.impl;

import com.gy.show.enums.DataSourceModeEnum;
import com.gy.show.service.ExternalDataConfigService;
import com.gy.show.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 外部数据配置管理服务实现
 */
@Slf4j
@Service
public class ExternalDataConfigServiceImpl implements ExternalDataConfigService {

    /**
     * Redis缓存key，用于持久化跟随状态
     */
    private static final String FOLLOW_STATUS_CACHE_KEY = "external:data:follow:status";

    /**
     * Redis缓存key，用于持久化数据源模式
     */
    private static final String DATA_SOURCE_MODE_CACHE_KEY = "external:data:source:mode";

    /**
     * 内存中的跟随状态，用于快速访问
     */
    private final AtomicBoolean followStatus = new AtomicBoolean(false);

    /**
     * 内存中的数据源模式，用于快速访问
     */
    private final AtomicReference<DataSourceModeEnum> dataSourceMode = new AtomicReference<>(DataSourceModeEnum.REAL_DATA);

    /**
     * 初始化时从Redis加载状态
     */
    public ExternalDataConfigServiceImpl() {
        loadFollowStatusFromCache();
        loadDataSourceModeFromCache();
    }

    @Override
    public boolean getFollowStatus() {
        return followStatus.get();
    }

    @Override
    public void setFollowStatus(boolean followStatus) {
        this.followStatus.set(followStatus);
        // 同步到Redis缓存
        saveFollowStatusToCache(followStatus);
        log.info("数据跟随状态已设置为: {}", followStatus ? "开启" : "关闭");
    }

    @Override
    public boolean toggleFollowStatus() {
        boolean newStatus = !followStatus.get();
        setFollowStatus(newStatus);
        log.info("数据跟随状态已切换为: {}", newStatus ? "开启" : "关闭");
        return newStatus;
    }

    @Override
    public DataSourceModeEnum getDataSourceMode() {
        return dataSourceMode.get();
    }

    @Override
    public void setDataSourceMode(DataSourceModeEnum mode) {
        if (mode == null) {
            throw new IllegalArgumentException("数据源模式不能为空");
        }
        this.dataSourceMode.set(mode);
        // 同步到Redis缓存
        saveDataSourceModeToCache(mode);
        log.info("数据源模式已设置为: {}", mode.getDescription());
    }

    @Override
    public DataSourceModeEnum toggleDataSourceMode() {
        DataSourceModeEnum currentMode = dataSourceMode.get();
        DataSourceModeEnum newMode = currentMode == DataSourceModeEnum.REAL_DATA ?
                                    DataSourceModeEnum.SIMULATION_DATA :
                                    DataSourceModeEnum.REAL_DATA;
        setDataSourceMode(newMode);
        log.info("数据源模式已切换为: {}", newMode.getDescription());
        return newMode;
    }

    @Override
    public boolean isRealDataMode() {
        return dataSourceMode.get() == DataSourceModeEnum.REAL_DATA;
    }

    @Override
    public boolean isSimulationDataMode() {
        return dataSourceMode.get() == DataSourceModeEnum.SIMULATION_DATA;
    }

    /**
     * 从Redis缓存加载跟随状态
     */
    private void loadFollowStatusFromCache() {
        try {
            String cachedStatus = RedisUtil.StringOps.get(FOLLOW_STATUS_CACHE_KEY);
            if (StringUtils.isNotBlank(cachedStatus)) {
                boolean status = Boolean.parseBoolean(cachedStatus);
                followStatus.set(status);
                log.info("从缓存加载数据跟随状态: {}", status ? "开启" : "关闭");
            } else {
                // 默认状态为关闭
                followStatus.set(false);
                saveFollowStatusToCache(false);
                log.info("初始化数据跟随状态为: 关闭");
            }
        } catch (Exception e) {
            log.error("从缓存加载数据跟随状态失败，使用默认状态: 关闭", e);
            followStatus.set(false);
        }
    }

    /**
     * 保存跟随状态到Redis缓存
     */
    private void saveFollowStatusToCache(boolean status) {
        try {
            RedisUtil.StringOps.set(FOLLOW_STATUS_CACHE_KEY, String.valueOf(status));
        } catch (Exception e) {
            log.error("保存数据跟随状态到缓存失败", e);
        }
    }

    /**
     * 从Redis缓存加载数据源模式
     */
    private void loadDataSourceModeFromCache() {
        try {
            String cachedMode = RedisUtil.StringOps.get(DATA_SOURCE_MODE_CACHE_KEY);
            if (StringUtils.isNotBlank(cachedMode)) {
                DataSourceModeEnum mode = DataSourceModeEnum.fromCode(cachedMode);
                dataSourceMode.set(mode);
                log.info("从缓存加载数据源模式: {}", mode.getDescription());
            } else {
                // 默认模式为真实数据
                dataSourceMode.set(DataSourceModeEnum.REAL_DATA);
                saveDataSourceModeToCache(DataSourceModeEnum.REAL_DATA);
                log.info("初始化数据源模式为: {}", DataSourceModeEnum.REAL_DATA.getDescription());
            }
        } catch (Exception e) {
            log.error("从缓存加载数据源模式失败，使用默认模式: {}", DataSourceModeEnum.REAL_DATA.getDescription(), e);
            dataSourceMode.set(DataSourceModeEnum.REAL_DATA);
        }
    }

    /**
     * 保存数据源模式到Redis缓存
     */
    private void saveDataSourceModeToCache(DataSourceModeEnum mode) {
        try {
            RedisUtil.StringOps.set(DATA_SOURCE_MODE_CACHE_KEY, mode.getCode());
        } catch (Exception e) {
            log.error("保存数据源模式到缓存失败", e);
        }
    }
}
