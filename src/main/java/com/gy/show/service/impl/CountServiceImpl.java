package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gy.show.entity.dos.*;
import com.gy.show.enums.RequirementStatusEnum;
import com.gy.show.enums.TaskScheduleStatusEnum;
import com.gy.show.enums.TaskStatusEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CountServiceImpl implements CountService {

    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private RequirementInfoService requirementInfoService;
    @Autowired
    private RequirementTaskService requirementTaskService;
    @Autowired
    private DataGeneralService dataGeneralService;
    @Resource
    private CommonMapper commonMapper;
    @Resource
    private SysDictionaryService sysDictionaryService;

    @Override
    public Map<String, Integer> countRequirementExeCondition() {
        Map<String, Integer> countMap = new HashMap<>();
        String[] timeRangeSplit = getTimeRangeBySysConfig();
        List<RequirementInfo> requirementInfos = requirementInfoService.list(new QueryWrapper<RequirementInfo>().between("start_time", timeRangeSplit[0], timeRangeSplit[1]));
        Map<Integer, List<RequirementInfo>> collect = requirementInfos.stream().collect(Collectors.groupingBy(RequirementInfo::getStatus));
        for (Integer code : collect.keySet()) {
            String statusMessage = RequirementStatusEnum.getEnumByCode(code).getMessage();
            countMap.put(statusMessage, collect.get(code).size());
        }
        return countMap;
    }

    @Override
    public Map<String, Integer> countTaskExeCondition() {
        Map<String, Integer> countMap = new HashMap<>();
        String[] timeRangeSplit = getTimeRangeBySysConfig();
        List<RequirementTask> requirementTasks = requirementTaskService.list(new QueryWrapper<RequirementTask>().between("start_time", timeRangeSplit[0], timeRangeSplit[1]));
        Map<Integer, List<RequirementTask>> collect = requirementTasks.stream().collect(Collectors.groupingBy(task -> task.getStatus()));
        for (Integer code : collect.keySet()) {
            String statusMessage = TaskStatusEnum.getEnumByCode(code).getMessage();
            countMap.put(statusMessage, collect.get(code).size());
        }
        return countMap;
    }

    @Override
    public Map<String, Integer> countMbBusinessType() {
        Map<String, Integer> countMap = new HashMap<>();
        List<DataGeneral> list = dataGeneralService.list();
        List<Map<String, Object>> records = new ArrayList<>();
        for (DataGeneral dataGeneral : list) {
            IPage<Map<String, Object>> data = commonMapper.getData(new Page<>(-1,-1), dataGeneral.getTableName(), null, null, null);
            records.addAll(data.getRecords());
        }
        List<SysDictionary> sysDictionarys = sysDictionaryService.getDicByType("businessType");
        Map<Integer, String> dicMap = sysDictionarys.stream().collect(Collectors.toMap(dic -> dic.getDictValue(), dic -> dic.getDictName()));
        Map<Object, List<Map<String, Object>>> collect = records.stream().filter(map -> map.get("businessType") != null).collect(Collectors.groupingBy(map -> map.get("businessType")));
        for (Object businessType:collect.keySet()){
            countMap.put(dicMap.get(businessType),collect.get(businessType).size());
        }
        return countMap;
    }

    @Override
    public Map<Object, Integer> countDataAreaSchedule() {

        return null;
    }


    /**
     * 获取时间区间
     *
     * @return
     */
    private String[] getTimeRangeBySysConfig() {
        //时间区间
        SysConfig sysConfig = sysConfigService.getById(2);
        String timeRangeStr = sysConfig.getValue();
        String[] split = timeRangeStr.split("\\|");
        return split;
    }
}
