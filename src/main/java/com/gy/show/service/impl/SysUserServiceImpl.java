package com.gy.show.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.SysUser;
import com.gy.show.entity.dto.SysUserDTO;
import com.gy.show.entity.dto.UserPasswordDTO;
import com.gy.show.mapper.SysUserMapper;
import com.gy.show.service.SysUserService;
import com.gy.show.util.ConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(String userId) {
        SysUser user = getById(userId);
        if (user != null) {
            user.setStatus(!user.getStatus());
            updateById(user);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeUserPassword(UserPasswordDTO userPasswordDTO) {
        SysUser user = getById(userPasswordDTO.getUserId());

        if (user == null) {
            throw new ServiceException("参数错误");
        }

        // 比较旧密码是否正确
        if (!userPasswordDTO.getOldPassword().equals(user.getPassword())) {
            throw new ServiceException("旧密码输入错误");
        }

        // 比较新旧密码是否相同
        if (user.getPassword().equals(userPasswordDTO.getNewPassword())) {
            throw new ServiceException("新密码不能与旧密码相同");
        }

        user.setPassword(userPasswordDTO.getNewPassword());
        updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SysUserDTO userDTO) {
        SysUser user = userDTO.convert();
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String encode = encoder.encode(user.getPassword());
        user.setPassword(encode);

        save(user);
    }

    @Override
    public IPage<SysUserDTO> getUsers(IPage<SysUser> page, String keyword) {
        IPage<SysUser> userPage = page(page, Wrappers.<SysUser>lambdaQuery().like(StringUtils.isNotBlank(keyword), SysUser::getUsername, keyword));

        return ConvertUtil.buildPage(userPage);
    }

    @Override
    public SysUserDTO getUserById(String id) {
        SysUser sysUser = getById(id);
        return sysUser.convert();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(SysUserDTO user) {
        SysUser sysUser = user.convert();
        updateById(sysUser);
    }
}
