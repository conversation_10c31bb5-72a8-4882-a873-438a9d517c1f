package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataModel;
import com.gy.show.mapper.DataModelMapper;
import com.gy.show.service.DataModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class DataModelServiceImpl extends ServiceImpl<DataModelMapper, DataModel> implements DataModelService {

    @Autowired
    private DataModelMapper dataModelMapper;

    public DataModel getDataModelById(String id) {
        return dataModelMapper.selectById(id);
    }

    public DataModel createDataModel(DataModel dataModel) {
        dataModelMapper.insert(dataModel);
        return dataModel;
    }

    public DataModel updateDataModel(String id, DataModel newDataModel) {
        newDataModel.setId(id);
        dataModelMapper.updateById(newDataModel);
        return newDataModel;
    }

    public void deleteDataModel(String id) {
        dataModelMapper.deleteById(id);
    }

    public IPage<DataModel> getAllDataModels(int page, int size, String keyword) {
        LambdaQueryWrapper<DataModel> queryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.like(DataModel::getMxmc, keyword);
        }
        queryWrapper.orderByDesc(DataModel::getCjsj);
        return dataModelMapper.selectPage(new Page<>(page, size), queryWrapper);
    }
}
