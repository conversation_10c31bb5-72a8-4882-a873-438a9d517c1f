package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataArea;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.DataGeneralAreaRelation;
import com.gy.show.entity.dto.DataAreaDTO;
import com.gy.show.entity.dto.DataGeneralDTO;
import com.gy.show.enums.DataTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.mapper.DataAreaMapper;
import com.gy.show.service.DataAreaService;
import com.gy.show.service.DataGeneralAreaRelationService;
import com.gy.show.service.DataGeneralService;
import com.gy.show.util.ConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DataAreaServiceImpl extends ServiceImpl<DataAreaMapper, DataArea> implements DataAreaService {

    @Resource
    private DataAreaMapper dataAreaMapper;

    @Autowired
    private DataGeneralAreaRelationService dataGeneralAreaRelationService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Resource
    private CommonMapper commonMapper;

    public DataArea getDataAreaById(String id) {
        return dataAreaMapper.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public DataArea createDataArea(DataAreaDTO dataAreaDTO) {
        // 保存资源域表
        DataArea dataArea = dataAreaDTO.convert();
        saveOrUpdate(dataArea);

        dataGeneralAreaRelationService.remove(Wrappers.<DataGeneralAreaRelation>lambdaQuery().eq(DataGeneralAreaRelation::getAreaId, dataArea.getId()));
        // 保存资源域与资源类型关联表
        List<DataGeneralAreaRelation> relations = dataAreaDTO.getGeneralIds()
                .stream()
                .map(type -> {
                    DataGeneralAreaRelation relation = new DataGeneralAreaRelation();
                    relation.setAreaId(dataArea.getId());
                    relation.setGeneralId(type);
                    return relation;
                })
                .collect(Collectors.toList());

        dataGeneralAreaRelationService.saveOrUpdateBatch(relations);

        return dataArea;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDataArea(DataAreaDTO dataArea) {
        // 需要判断当前选择的类型和已有类型
        List<String> newDataGeneralIds = dataArea.getGeneralIds();

        // 数据库已存在的id
        List<DataGeneralAreaRelation> dataGeneralAreaRelations = dataGeneralAreaRelationService.list(Wrappers.<DataGeneralAreaRelation>lambdaQuery()
                .eq(DataGeneralAreaRelation::getAreaId, dataArea.getId()));
        List<String> existDataGeneralIds = dataGeneralAreaRelations.stream()
                .map(DataGeneralAreaRelation::getGeneralId)
                .collect(Collectors.toList());

        // 需要删除的id
        List<String> needRemoveType = existDataGeneralIds.stream()
                .filter(e -> !newDataGeneralIds.contains(e))
                .collect(Collectors.toList());

//        if (CollUtil.isNotEmpty(needRemoveType) && !dataArea.isConfirm()) {
//            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(needRemoveType);
//            StringBuilder sb = new StringBuilder();
//            for (DataGeneral dataGeneral : dataGenerals) {
//                sb.append(dataGeneral.getTableComment())
//                        .append(",");
//            }
//
//            throw new ServiceException("该操作会删除" + sb.toString() + "请确认是否继续？");
//        }

        // 删除与该域相关的所有数据
        if (CollUtil.isNotEmpty(needRemoveType)) {
            Collection<DataGeneral> dataGenerals = dataGeneralService.listByIds(needRemoveType);
            for (DataGeneral dataGeneral : dataGenerals) {
                String sql = "DELETE FROM " + dataGeneral.getTableName() + " WHERE area_id = " + dataArea.getId();
                log.info("执行sql语句----> {}", sql);
                commonMapper.exec(sql);
            }
        }

        createDataArea(dataArea);
    }

    @Override
    public List<DataAreaDTO> getAreaAndTargetInfo(String generalId, String targetId) {
        List<DataAreaDTO> collect = new ArrayList<>();
        DataGeneral dataGenera = dataGeneralService.getById(generalId);
        List<Map<String, Object>> targetLists = commonMapper.getListByCol(dataGenera.getTableName(), "id", targetId);
        if (targetLists != null && targetLists.size() != 0) {
            Map<String, Object> result = targetLists.get(0);
            String areaId = result.get("areaId").toString();
            DataArea dataArea = dataAreaMapper.selectById(areaId);
            List<DataArea> dataAreas = dataAreaMapper.selectList(new QueryWrapper<DataArea>().eq("area_type", dataArea.getAreaType()));
            collect = dataAreas.stream().map(area -> {
                DataAreaDTO convert = area.convert();
                List<DataGeneralAreaRelation> dataGeneralAreaRelations = dataGeneralAreaRelationService.list(new QueryWrapper<DataGeneralAreaRelation>().eq("area_id", areaId));
                List<String> generalIds = dataGeneralAreaRelations.stream().map(relation -> relation.getGeneralId()).collect(Collectors.toList());
                List<DataGeneral> dataGenerals = dataGeneralService.list(new QueryWrapper<DataGeneral>().in("id", generalIds));
                List<DataGeneralDTO> generalIdDtos = dataGenerals.stream().map(dataGeneral -> {
                    {
                        DataGeneralDTO convert1 = dataGeneral.convert();
                        if (convert1.getId().equals(generalId)) {
                            convert1.setShow(true);
                            convert1.setTargetInfo(result);
                        }
                        // 转换数据类型枚举
                        convert1.setDataTypeValue(DataTypeEnum.getEnumByCode(convert1.getDataType()).getMessage());
                        return convert1;
                    }
                }).collect(Collectors.toList());
                if (convert.getId().equals(areaId)) {
                    convert.setShow(true);
                    convert.setDataGenerals(generalIdDtos);
                }
                return convert;
            }).collect(Collectors.toList());
        }
        return collect;

    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteDataArea(String id) {
        dataAreaMapper.deleteById(id);
    }

    public IPage<DataAreaDTO> getAllDataAreas(int page, int size, Integer areaType, String keyword, Integer source) {
        LambdaQueryWrapper<DataArea> queryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(keyword)) {
            queryWrapper.like(DataArea::getAreaName, keyword);
        }
        queryWrapper.eq(areaType != null, DataArea::getAreaType, areaType);
        queryWrapper.eq(source != null, DataArea::getSource, source);
        queryWrapper.orderByDesc(DataArea::getCreateTime);

        IPage<DataArea> resultPage = dataAreaMapper.selectPage(new Page<>(page, size), queryWrapper);

        List<DataGeneralAreaRelation> areaRelations = dataGeneralAreaRelationService.list();
        Map<String, List<String>> areaRelation = areaRelations.stream()
                .collect(Collectors.groupingBy(DataGeneralAreaRelation::getAreaId,
                        Collectors.mapping(dataGeneralAreaRelation -> dataGeneralAreaRelation.getGeneralId(), Collectors.toList())));

        IPage<DataAreaDTO> result = ConvertUtil.buildPage(resultPage);
        List<DataAreaDTO> records = result.getRecords();
        records.forEach(r -> r.setGeneralIds(areaRelation.get(r.getId())));

        return result;
    }
}
