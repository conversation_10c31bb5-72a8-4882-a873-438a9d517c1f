package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.TaskEquipmentRelation;
import com.gy.show.mapper.TaskEquipmentRelationMapper;
import com.gy.show.service.TaskEquipmentRelationService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class TaskEquipmentRelationServiceImpl extends ServiceImpl<TaskEquipmentRelationMapper, TaskEquipmentRelation> implements TaskEquipmentRelationService {

    @Override
    public List<TaskEquipmentRelation> queryEquipmentByTaskIds(List<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) return Collections.EMPTY_LIST;
        return list(Wrappers.<TaskEquipmentRelation>lambdaQuery().in(TaskEquipmentRelation::getTaskId, taskIds));
    }
}
