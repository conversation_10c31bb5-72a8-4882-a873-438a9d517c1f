package com.gy.show.service.impl;

import com.alibaba.fastjson.JSON;
import com.gy.show.constants.Constants;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.entity.dto.external.UavStationDTO;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.InteractionSourceEnum;
import com.gy.show.enums.LogTypeEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.runnner.InterBootstrapRunner;
import com.gy.show.service.ExternalDataService;
import com.gy.show.service.UavStationService;
import com.gy.show.socket.BinaryParser;
import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.socket.message.fields.*;
import com.gy.show.socket.server.UdpMulticastSender;
import com.gy.show.ws.FullViewTsServer;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UavStationServiceImpl extends StationServiceImpl implements UavStationService {

    @Autowired
    private InterBootstrapRunner runner;

    @Autowired
    private ExternalDataService externalDataService;

    @Autowired
    private FullViewTsServer sever;

    @Override
    public void parseMessage(ByteBuf byteBuf, String id) {
        // 解析报文头
        Map<String, Object> headMap = parseHeadMessage(byteBuf);

        // 解析报文
        parseBodyMessage(byteBuf, headMap, id);
    }

    @Override
    public void sendMessage(UavStationDTO uavStationDTO) {
        log.info("向无人机测控站发送消息：{}", uavStationDTO);
        ByteBuf byteBuf = Unpooled.buffer();

        // 构建信息
        packageMessage(byteBuf, uavStationDTO);

        // 发送消息
        UdpMulticastSender sender = runner.getSenders().get(ClientTypeEnum.UAV.getMessage());
        sender.sendMessage(byteBuf);
    }

    @Override
    public void parseMessage1(ByteBuf byteBuf, String id) {
        // 解析报文头
        Map<String, Object> headMap = parseHeadMessage(byteBuf);

        // 解析报文
        parseBodyMessage1(byteBuf, headMap, id);
    }

    private void parseBodyMessage1(ByteBuf byteBuf, Map<String, Object> headMap, String id) {
        String code = Integer.toHexString((Short) headMap.get("code"));
        log.info("无人机测控站信息命令码：{}", code);

        switch (code) {
            case "41":
                // 解析报文
                log.info("接收到无人机遥测数据");

                break;
            default:
                break;
        }
    }

    private void packageMessage(ByteBuf byteBuf, UavStationDTO uavStationDTO) {
        // 帧头 1
        byteBuf.writeByte(0x7B);

        // 源地址 1
        byteBuf.writeByte(0xA2);

        // 目的地址 1
        byteBuf.writeByte(0xA1);

        // 信息类型 1
        byteBuf.writeByte(0xB1);

        // 帧循环计数 1
        byteBuf.writeByte(0);

        // 数据长度 2
        byteBuf.writeShortLE(0x02);

        // 命令码 1
        byteBuf.writeByte(0x31);

        // 席位号 1
        byteBuf.writeByte(0x30);

        // 发射功率 1
        byteBuf.writeByte(uavStationDTO.getTransmitPower());

        // 帧尾 1
        byteBuf.writeByte(0x7D);

        // 校验和 1
        byteBuf.writeByte(0);

        // 换行回车 2
        byteBuf.writeByte(0x0D);
        byteBuf.writeByte(0x0A);
    }

    private void parseBodyMessage(ByteBuf byteBuf, Map<String, Object> headMap, String id) {
        String code = Integer.toHexString((Short) headMap.get("code"));
        log.info("无人机测控站信息命令码：{}", code);

        // 获取映射站点信息
        Map<String, Object> stationInfo = mappingStation(id);

        switch (code) {
            case "33":
                // 解析报文
                Map<String, Object> bodyInfo = parseUavStatusMessage(byteBuf);


                bodyInfo.put("stationInfo", stationInfo);

                // 缓存站点数据
                cacheStationData(Collections.singletonList(bodyInfo), id);

                // 发送到前端
                sendMessage2Front(bodyInfo, WebSocketTypeEnum.FULL_VIEW_UAV_STATION.getCode());

                // 发送交互日志
                server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到无人机测控站状态信息", InteractionSourceEnum.STATION_STATE )));

                break;
                // todo  命令码需要更换，这里随便写一个先
            case "41":
                log.info("接收到无人机遥测数据,数据长度：{}", byteBuf.readableBytes());

                List<FieldDefinition> bodyList = new LinkedList<>();

                // ADT编号 1
                UnSignedByteField adt = new UnSignedByteField("adt");
                bodyList.add(adt);

                // 备用 2
                EmptyField empty = new EmptyField("backup", 2);
                bodyList.add(empty);

                // 时间码 4
                UnSignedIntegerField time = new UnSignedIntegerField("time");
                bodyList.add(time);

                // 目标代号 16
                FixedLengthField targetCode = new FixedLengthField("targetCode", 16);
                bodyList.add(targetCode);

                // 解析报文
                Map<String, Object> body = BinaryParser.parseFieldsLE(byteBuf, bodyList);

                byte[] target = (byte[]) body.get("targetCode");
                body.put("targetCode", new String(target));
                log.info("无人机测控站信息头部解析结果：{}", body);

                // 发送日志
                sendStationMessage2Front(byteBuf, LogTypeEnum.INFO.getMessage(), "接收到无人机测控站遥测数据", id);

                // 遥测信息
                log.info("剩余遥测信息长度:{}", byteBuf.readableBytes());
                byte[] data = new byte[70];
                byteBuf.readBytes(data);

                // 转发遥测信息到操控端
                sendBusinessData(data, id, null);

                // 发送交互日志
//                server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到无人机测控站遥测信息" )));

                break;
            default:
                break;
        }
    }

    private Map<String, Object> parseUavStatusMessage(ByteBuf byteBuf) {
        List<FieldDefinition> bodyList = new LinkedList<>();

        // 上行锁定状态 1
        UnSignedByteField sxsdzt = new UnSignedByteField("sxsdzt");
        bodyList.add(sxsdzt);

        // 下行锁定状态 1
        UnSignedByteField xxsdzt = new UnSignedByteField("xxsdzt");
        bodyList.add(xxsdzt);

        // 上行发射状态 1
        CharField sxfszt = new CharField("sxfszt", Constants.fireStatus);
        bodyList.add(sxfszt);

        // 下行发射状态 1
        CharField xxfszt = new CharField("xxfszt", Constants.fireStatus);
        bodyList.add(xxfszt);

        // 上行遥控频率 7
        FixedLengthField sxykpl = new FixedLengthField("sxykpl", 7);
        bodyList.add(sxykpl);

        // 下行遥控频率 5
        FixedLengthField xxykpl = new FixedLengthField("xxykpl", 5);
        bodyList.add(xxykpl);

        // 上行遥控速率 4
        UnSignedIntegerField sxyksl = new UnSignedIntegerField("sxyksl");
        bodyList.add(sxyksl);

        // 下行遥测速率 4
        UnSignedIntegerField xxycsl = new UnSignedIntegerField("xxycsl");
        bodyList.add(xxycsl);

        // 上行信号强度信息 2
        UnSignShortField sxxhqd = new UnSignShortField("sxxhqd");
        bodyList.add(sxxhqd);

        // 下行信号强度信息 2
        UnSignShortField xxxhqd = new UnSignShortField("xxxhqd");
        bodyList.add(xxxhqd);

        // 帧尾 1
        UnSignedByteField frameTail = new UnSignedByteField("frameTail");
        bodyList.add(frameTail);

        // 校验和 1
        UnSignedByteField checksum = new UnSignedByteField("checksum");
        bodyList.add(checksum);

        // 换行回车 2
        UnSignShortField enter = new UnSignShortField("enter");
        bodyList.add(enter);

        Map<String, Object> bodyMap = BinaryParser.parseFieldsLE(byteBuf, bodyList);

        // 单位解析
        byte[] sxykpl1 = (byte[]) bodyMap.get("sxykpl");
        bodyMap.put("sxykpl", convertHexToDecimal(sxykpl1));

        byte[] xxykpl1 = (byte[]) bodyMap.get("xxykpl");
        bodyMap.put("xxykpl", convertHexToDecimal(xxykpl1));

        Long sxykslInt = (Long) bodyMap.get("sxyksl");
        bodyMap.put("sxyksl", (double) sxykslInt / 10);

        Long xxycslInt = (Long) bodyMap.get("xxycsl");
        bodyMap.put("xxycsl", (double) xxycslInt / 10);

        Integer sxxhqdInt = (Integer) bodyMap.get("sxxhqd");
        bodyMap.put("sxxhqd", (double) sxxhqdInt / 100);

        Integer xxxhqdInt = (Integer) bodyMap.get("xxxhqd");
        bodyMap.put("xxxhqd", (double) xxxhqdInt / 10);

        log.info("解析无人机测控站状态信息结果：{}", bodyMap);

        return bodyMap;

    }

    private double convertHexToDecimal(byte[] hexArr) {
        long integerPart = 0; // 整数部分
        long fractionalPart = 0; // 小数部分
        int fractionalDigits = 0;

        for (int i = 0; i < hexArr.length; i++) {
            int lowNibble = hexArr[i] & 0x0F; // 提取低四位

            if (i < 4) { // 前4个字节为整数部分
                integerPart = integerPart * 10 + lowNibble;
            } else {
                fractionalPart = fractionalPart * 10 + lowNibble;
                fractionalDigits++;
            }
        }

        return integerPart + fractionalPart / Math.pow(10, fractionalDigits);
    }

    private Map<String, Object> parseHeadMessage(ByteBuf byteBuf) {
        List<FieldDefinition> headList = new LinkedList<>();

        // 帧头 1
        UnSignedByteField frameHead = new UnSignedByteField("frameHead");
        headList.add(frameHead);

        // 源地址 1
        UnSignedByteField sourceAddress = new UnSignedByteField("sourceAddress");
        headList.add(sourceAddress);

        // 目的地址 1
        UnSignedByteField targetAddress = new UnSignedByteField("targetAddress");
        headList.add(targetAddress);

        // 信息类型 1
        UnSignedByteField messageType = new UnSignedByteField("messageType");
        headList.add(messageType);

        // 帧循环计数 1
        UnSignedByteField frameLoopCount = new UnSignedByteField("frameLoopCount");
        headList.add(frameLoopCount);

        // 数据长度 2
        ShortField length = new ShortField("length");
        headList.add(length);

        // 命令码 1
        UnSignedByteField code = new UnSignedByteField("code");
        headList.add(code);

        // 席位号 1
        UnSignedByteField no = new UnSignedByteField("no");
        headList.add(no);

        Map<String, Object> headMap = BinaryParser.parseFieldsLE(byteBuf, headList);
        log.info("无人机测控站信息头部解析结果：{}", headMap);

        return headMap;
    }
}
