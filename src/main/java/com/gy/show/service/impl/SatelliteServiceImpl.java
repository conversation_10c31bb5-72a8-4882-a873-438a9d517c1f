package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.cetc10.spaceflight.orbitpre.orbit.LLAPredict;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dto.SatelliteDTO;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.service.DataGeneralService;
import com.gy.show.service.FileService;
import com.gy.show.service.SatelliteService;
import com.gy.show.util.SatelliteOrbitCalculator;
import com.gy.show.util.SatelliteUtil;
import com.gy.show.ws.GlobalServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class SatelliteServiceImpl implements SatelliteService {

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private FileService fileService;

    @Autowired
    private GlobalServer simulationServer;

    @Autowired
    private ThreadPoolTaskExecutor executor;

    private Map<String, SatelliteOrbitCalculator.SatellitePropagator> runningSatellite = new ConcurrentHashMap<>();

    @Override
    public Object calculateOrbit(String generaId, String equipmentId, String startTime) {
        Map<String, Object> equipment = queryEquipment(generaId, equipmentId);

        List<LLAPredict> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(equipment)) {
            // 获取二根数文件存储路径
            String filePath = queryFilePath(equipment);

//            result = SatelliteOrbitCalculator.calculateOrbit(filePath, 36000);
            result = SatelliteUtil.calculateOrbit0(filePath, DateUtil.parseLocalDateTime(startTime), null, 10.0);
        }
        return result;
    }

    @Override
    public void startNumericalForecast(String generaId, String equipmentId) {
        SatelliteOrbitCalculator.SatellitePropagator satellite = runningSatellite.get(getRunningMapKey(generaId, equipmentId));
        if (satellite != null) {
            return;
        }
        Map<String, Object> equipment = queryEquipment(generaId, equipmentId);

        if (CollUtil.isNotEmpty(equipment)) {
            String filePath = queryFilePath(equipment);

            SatelliteOrbitCalculator.SatellitePropagator satellitePropagator = new SatelliteOrbitCalculator.SatellitePropagator(filePath, 3600, simulationServer, equipmentId);

            executor.execute(satellitePropagator::numericalForecast);
            runningSatellite.put(getRunningMapKey(generaId, equipmentId), satellitePropagator);
            log.info("卫星推送线程已启动，当前运行中的卫星数量：{}", runningSatellite.size());
        }
    }

    @Override
    public void stopNumericalForecast(String generaId, String equipmentId) {
        String runningMapKey = getRunningMapKey(generaId, equipmentId);
        SatelliteOrbitCalculator.SatellitePropagator satellitePropagator = runningSatellite.get(runningMapKey);
        if (satellitePropagator == null) {
            throw new ServiceException("当前卫星没有推送数据");
        }

        // 停止推送
        satellitePropagator.stopForecast();

        runningSatellite.remove(runningMapKey);
        log.info("停止推送卫星实时位置成功，当前剩余进行中的卫星位置推送数量：{}", runningSatellite.size());
    }

    @Override
    public List<LLAPredict> getSatelliteCoordinate(SatelliteDTO satelliteDTO) {
        String generaId = satelliteDTO.getGeneraId();
        String equipmentId = satelliteDTO.getEquipmentId();

        Map<String, Object> equipment = queryEquipment(generaId, equipmentId);

        List<LLAPredict> llaPredicts = new ArrayList<>();
        if (CollUtil.isNotEmpty(equipment)) {
            String filePath = queryFilePath(equipment);

            if (filePath == null) {
                return llaPredicts;
            }


            llaPredicts = SatelliteUtil.calculateOrbit0(filePath, satelliteDTO.getDateTime(), satelliteDTO.getDateTime(), 1.0);
        }
        return llaPredicts;
    }

    @Override
    public Object getSatelliteCoordinate(List<SatelliteDTO> satelliteDTO) {
        Map<String, List<LLAPredict>> result = new HashMap<>(satelliteDTO.size());
        for (SatelliteDTO dto : satelliteDTO) {
            List<LLAPredict> llaPredicts = getSatelliteCoordinate(dto);

            result.put(dto.getEquipmentId(), llaPredicts);
        }
        return result;
    }

    private String getRunningMapKey(String generaId, String equipmentId) {
        return generaId + ":" + equipmentId;
    }

    private Map<String, Object> queryEquipment(String generaId, String equipmentId) {
        DataGeneral dataGeneral = dataGeneralService.getById(generaId);

        return commonMapper.getOne(dataGeneral.getTableName(), equipmentId);
    }

    private String queryFilePath(Map<String, Object> equipment) {
        // 获取二根数文件
        String fileId = (String) equipment.get("fileId");
        if (StringUtils.isBlank(fileId)) {
            return null;
        }

        // 获取二根数文件存储路径
        return fileService.getFileRealPath(fileId);
    }
}
