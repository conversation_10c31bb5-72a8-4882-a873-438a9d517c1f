package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.constants.CacheConstant;
import com.gy.show.constants.Constants;
import com.gy.show.controller.external.InteractionLogDTO;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.SysDataMapping;
import com.gy.show.entity.dos.TerminalInfo;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.entity.dto.external.*;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.InteractionSourceEnum;
import com.gy.show.enums.NodeSwitchStateEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.mapper.CommonMapper;
import com.gy.show.runnner.InterBootstrapRunner;
import com.gy.show.service.*;
import com.gy.show.socket.BinaryParser;
import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.socket.message.fields.*;
import com.gy.show.socket.server.UdpMulticastSender;
import com.gy.show.util.NodeSelection;
import com.gy.show.util.RedisUtil;
import com.gy.show.ws.FullViewTsServer;
import com.gy.show.ws.GlobalServer;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.*;
import static com.gy.show.constants.Constants.missileTerminalStatus;

@Slf4j
@Service
public class TerminalDataServiceImpl implements TerminalDataService {

    @Autowired
    private InterBootstrapRunner runner;

    @Autowired
    private FullViewTsServer server;

    @Autowired
    private GlobalServer globalServer;

    @Autowired
    private SysDataMappingService dataMappingService;

    @Autowired
    private DataGeneralService dataGeneralService;

    @Autowired
    private TerminalInfoService terminalInfoService;

    @Autowired
    private CommonMapper commonMapper;

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    @Autowired
    private StationSpaceStatusService stationSpaceStatusService;

    @Autowired
    private MissileService missileService;

    @Autowired
    private UavStationService uavStationService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private FileService fileService;

    private static final Integer RSS = 40;

    private Map<String, Object> parseMessageHead(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> head = new LinkedList<>();

        // 开始标志位 1 * 4
        UnSignedByteField flag1 = new UnSignedByteField("flag1");
        head.add(flag1);

        UnSignedByteField flag2 = new UnSignedByteField("flag2");
        head.add(flag2);

        UnSignedByteField flag3 = new UnSignedByteField("flag3");
        head.add(flag3);

        UnSignedByteField flag4 = new UnSignedByteField("flag4");
        head.add(flag4);

        // 信源 1
        UnSignedByteField source = new UnSignedByteField("source");
        head.add(source);

        // 信宿 1
        UnSignedByteField sink = new UnSignedByteField("sink");
        head.add(sink);

        // 保留字段 1
        UnSignedByteField revers = new UnSignedByteField("revers");
        head.add(revers);

        // 信息类型 1
        UnSignedByteField type = new UnSignedByteField("type");
        head.add(type);

        // 长度 4
        IntegerField length = new IntegerField("length");
        head.add(length);

        // 业务类型(4bit)、源节点地址(4bit) 1
        UnSignedByteField sourceAdd = new UnSignedByteField("sourceAdd");
        head.add(sourceAdd);

        // 下一跳地址 1
        UnSignedByteField nextAdd = new UnSignedByteField("nextAdd");
        head.add(nextAdd);

        // 业务优先级(1byte) 1
        UnSignedByteField priority = new UnSignedByteField("priority");
        head.add(priority);

        // 时间戳 5
        FixedLengthField time = new FixedLengthField("time", 5);
        head.add(time);

        // CRC 1
        UnSignedByteField crc = new UnSignedByteField("crc");
        head.add(crc);

        Map<String, Object> headMap = BinaryParser.parseFieldsLE(byteBuf, head);

        Integer sourceAddInt = Integer.parseInt(headMap.get("sourceAdd").toString());
        // 业务类型
        int bzType = (sourceAddInt >> 4) & 0x0F;
        headMap.put("bzType", bzType);

        // 源节点地址
        int sourceNode = sourceAddInt & 0x0F;
        headMap.put("sourceNode", sourceNode);

        Integer nextAddInt = Integer.parseInt(headMap.get("nextAdd").toString());
        // 目的节点地址
        int targetAddr = (nextAddInt >> 4) & 0x0F;
        headMap.put("targetAddr", targetAddr);

        // 下一跳地址
        int nextAddr = sourceAddInt & 0x0F;
        headMap.put("nextAddr", nextAddr);

        log.info("终端头部解析结果：{}", headMap);

        return headMap;
    }

    private Map<String, Object> parseMessageBody(ByteBuf byteBuf, Map<String, Object> head) {
        Map<String, Object> nodeMap = new HashMap<>(6);
        // 根据状态上报的节点填充0~5
        String sourceNode = head.get("sourceNode").toString();

        /** 2025.07.07 ADD 无人机模拟导弹的逻辑 **/
        sourceNode = simulaMissile(sourceNode);

        Map<String, Object> linkMessage = parseLinkMessage(byteBuf);
        nodeMap.put("nodeRelation", linkMessage);
        log.info("当前节点：{}，拓扑链接帧解析结果：{}", sourceNode, linkMessage);

        // 发送交互日志
        server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("接收到终端" + sourceNode +" 状态信息", InteractionSourceEnum.TERMINAL_STATE )));

        // 整机状态帧 80byte
        Map<String, Object> machineFrame = parseMachineFrame(byteBuf);
        nodeMap.put("machineFrame", machineFrame);

        // 如果源节点不是0号节点才需要往下进行解析
        if (!sourceNode.equals("0")) {
            while (byteBuf.readableBytes() > 0) {
                // 读取命令码
                int cmd = byteBuf.readIntLE();
                String hexCmd =Integer.toHexString(cmd);

                boolean errorFlag = false;
                switch (hexCmd) {
                    case "46":
                        // 2.1.2.3  无人机测控体制状态帧（68 byte）
                        Map<String, Object> droneFrame = parseDroneFrame(byteBuf);
                        nodeMap.put("droneFrame", droneFrame);
                        break;
                    case "44":
                        // 2.1.2.4  航天测控体制状态帧（76 byte）
                        Map<String, Object> spaceFrame = parseSpaceFrame(byteBuf);
                        nodeMap.put("spaceFrame", spaceFrame);
                        break;
                    case "51":
                        // 2.1.2.5  抗干扰状态帧（76 byte）
                        Map<String, Object> antiJammingFrame = parseAntiJammingFrame(byteBuf);
                        nodeMap.put("antiJammingFrame", antiJammingFrame);
                        break;
                    case "50":
                        // 2.1.2.6  频谱数据帧（570 byte）
                        Map<String, Object> spectrumFrame = parseSpectrumFrame(byteBuf);
                        nodeMap.put("spectrumFrame", spectrumFrame);
                        break;
                    case "60":
                        // 2.1.2.7  建链申请数据帧（58 byte）
                        Map<String, Object> createLinkDataFrame = parseCreateLinkDataFrame(byteBuf);
                        nodeMap.put("createLinkDataFrame", createLinkDataFrame);
                        break;
                    case "41":
                        //  2.1.2.5  DD测控体制状态帧（76 byte）
                        Map<String, Object> missileFrame = parseMissileFrame(byteBuf);
                        nodeMap.put("missileFrame", missileFrame);
                        break;
                    default:
                         log.error("当前命令码有误：{}，无法继续向后解析", hexCmd);
                         errorFlag = true;
                         break;
                }
                if (errorFlag) break;
            }
        }

        Map<String, Object> result = new HashMap<>(1);
        result.put(sourceNode, nodeMap);
        return result;
    }

    /**
     * 无人机模拟导弹逻辑
     * @param sourceNode
     * @return
     */
    private String simulaMissile(String sourceNode) {
        String result = RedisUtil.StringOps.get(CacheConstant.SIMULA_MISSILE);
        // 只需要判断终端2，终端2才是无人机
        if (StringUtils.isNotBlank(result) && result.equals("1") && sourceNode.equals("2")) {
            sourceNode = "6";
        }
        return sourceNode;
    }

    private Map<String, Object> parseMissileFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> missileFrame = new LinkedList<>();
        // 保留 40
        FixedLengthField reverse = new FixedLengthField("reverse", 40);
        missileFrame.add(reverse);

        // 锁相环锁定（遥控） 4
        IntegerField DDPhaseLockYk = new IntegerField("DDPhaseLockYk");
        missileFrame.add(DDPhaseLockYk);

        // 锁频环锁定（遥控） 4
        IntegerField DDFreqLockYk = new IntegerField("DDFreqLockYk");
        missileFrame.add(DDFreqLockYk);

        // 码环锁定（遥控） 4
        IntegerField DDCodeLockYk = new IntegerField("DDCodeLockYk");
        missileFrame.add(DDCodeLockYk);

        // 位同步锁定（遥控） 4
        IntegerField DDBitLockYk = new IntegerField("DDBitLockYk");
        missileFrame.add(DDBitLockYk);

        // 帧同步锁定（遥控） 4
        IntegerField DDFrameLockYk = new IntegerField("DDFrameLockYk");
        missileFrame.add(DDFrameLockYk);

        // 多普勒频率（遥控） 4
        IntegerField dplFreqYk = new IntegerField("dplFreqYk");
        missileFrame.add(dplFreqYk);

        // 信噪比（遥控） 4
        IntegerField snrDbYk = new IntegerField("snrDbYk");
        missileFrame.add(snrDbYk);

        // 下行发射状态 4
        IntegerField transModState = new IntegerField("transModState");
        missileFrame.add(transModState);

        Map<String, Object> missileFrameResult = BinaryParser.parseFieldsLE(byteBuf, missileFrame);

        // 单位转换
        Integer transModState1 = (Integer) missileFrameResult.get("transModState");
        missileFrameResult.put("transModState", missileTerminalStatus.get(Integer.toHexString(transModState1)));

        log.info("解析导弹测控体制状态帧完成，结果：{}", missileFrameResult);

        return missileFrameResult;
    }

    @Override
    public void parseTerminalMessage(ChannelHandlerContext ctx, ByteBuf byteBuf) {
        // 解析头部信息
        Map<String, Object> head = parseMessageHead(byteBuf);

        // 解析报文体
        Map<String, Object> message = parseMessageBody(byteBuf, head);

        // 获取映射表信息，查询节点对应的目标并返回
        queryDataMapping(message);

        // 发送终端接入日志
        sendTerminalLog(ctx, message);

        // 将终端状态存入缓存中
        cacheTerminal(message);

        // 随遇接入节点切换
        nodeSelection(message);

        // 从缓存中获取抗干扰决策结果
        findAntiJResult(message);

        // 发送数据到前端
        sendMessage2Front(message);
    }

    private void sendTerminalLog(ChannelHandlerContext ctx, Map<String, Object> message) {
        for (Map.Entry<String, Object> entry : message.entrySet()) {
            if (entry.getKey().equals("0")) return;
            String ter = RedisUtil.StringOps.get(TERMINAL_ALL_STATUS + entry.getKey());

            if (StringUtils.isBlank(ter)) {
                // 如果为空则表示第一次接入，或者断线重连
                server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("终端:" + entry.getKey() + "发起随遇接入", InteractionSourceEnum.TERMINAL_STATE)));
            }

            ctx.channel().attr(AttributeKey.valueOf("source")).set("跨域多模复合终端:" + entry.getKey());
            ctx.channel().attr(AttributeKey.valueOf("type")).set("跨域多模复合终端状态");
        }
    }

    private void cacheTerminal(Map<String, Object> message) {
        for (Map.Entry<String, Object> entry : message.entrySet()) {
            Map<String, Object> msg = (Map<String, Object>) entry.getValue();
            Map<String, Object> nodeRelation = (Map<String, Object>) msg.get("nodeRelation");

            Map<String, Object> sortedMap = new TreeMap<>(nodeRelation);

            log.debug("sortedMap -----> {}", sortedMap);
            int[] status = new int[6];int count = 0;
            for (Map.Entry<String, Object> node : sortedMap.entrySet()) {
                status[count] = Integer.parseInt(node.getValue().toString());
                count++;
            }

            // 终端状态存入缓存
            RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_ALL_STATUS + entry.getKey(), JSONArray.toJSONString(status), 10, TimeUnit.SECONDS);

            // 将整个终端发送的消息存入缓存，设置过期时间
            RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_ALL_MESSAGE + entry.getKey(), JSONArray.toJSONString(msg), 10, TimeUnit.SECONDS);

            // 将终端当前使用的体制存入缓存中，根据当前接收哪种体制来判断，航天需要判断频率
            cacheTerminalMode(entry.getKey(), msg);

            // 获取终端当前体制，返回给前端
            msg.put("terCurrentMode", RedisUtil.StringOps.get(CacheConstant.TERMINAL_CURRENT_MODE + entry.getKey()));
        }
    }

    private void cacheTerminalMode(String seq, Map<String, Object> msg) {
        /**
         * 将终端当前使用的体制存入缓存中，根据当前接收哪种体制来判断，航天需要判断频率
         * 判断哪个帧存在就是哪个体制
         */
        // 航天 还需要判断频率
        Object spaceFrame = msg.get("spaceFrame");
        if (spaceFrame != null) {
            Map<String, Object> machineFrame = (Map<String, Object>) msg.get("machineFrame");
            Double rxFreq = (Double) machineFrame.get("rxFreq");
            if (rxFreq >= 1750 && rxFreq <= 2120) {
                // S
                RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_CURRENT_MODE + seq,
                        "1", 15, TimeUnit.SECONDS);
            } else {
                // Ka
                RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_CURRENT_MODE + seq,
                        "2", 15, TimeUnit.SECONDS);
            }
            return;
        }

        // 导弹
        Object missileFrame = msg.get("missileFrame");
        if (missileFrame != null) {
            RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_CURRENT_MODE + seq,
                    "4", 15, TimeUnit.SECONDS);
            return;
        }

        // 无人机
        Object droneFrame = msg.get("droneFrame");
        if (droneFrame != null) {
            RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_CURRENT_MODE + seq,
                    "3", 15, TimeUnit.SECONDS);
            return;
        }

    }

    private void sendStationChange(Map<String, Object> message) {
        Map<String, Object> msg = getMapValue(message);
        NodeSelection.Output output = (NodeSelection.Output) msg.get("sysAntJResult");

        if (output != null && output.getSlctEn() == 1) {
            // 通过节点id获取映射的key
            List<SysDataMapping> dataMappings = dataMappingService.queryDataMapping(Arrays.asList(2));

            SysDataMapping openDataMapping = dataMappings.stream()
                    .filter(dm -> dm.getDataValue().contains(output.getNodeID() + ""))
                    .findAny().get();

            SysDataMapping closeDataMapping = dataMappings.stream()
                    .filter(dm -> dm.getDataValue().contains(output.getLastNode() + ""))
                    .findAny().get();

            // 打开对应站点
            switch (openDataMapping.getDataKey()) {
                case "_ka":
                case "_s":
                    // 打开对应站点
                    switchSpaceStation(openDataMapping.getDataKey(), 1);
                    break;
                case "_missile":
                    switchMissileStation(6);
                    break;
                case "_uav":
                    switchUavStation(0x31);
                    break;
            }

            // 关闭对应站点
            switch (closeDataMapping.getDataKey()) {
                case "_ka":
                case "_s":
                    // 关闭对应站点
                    switchSpaceStation(closeDataMapping.getDataKey(), 2);
                    break;
                case "_missile":
                    switchMissileStation(7);
                    break;
                case "_uav":
                    switchUavStation(0x30);
                    break;
            }

        }
    }

    /**
     * 打开或关闭对应无人机站点
     * @param isOpen
     */
    @Override
    public void switchUavStation(int isOpen) {
        UavStationDTO uavStationDTO = new UavStationDTO();
        uavStationDTO.setTransmitPower(isOpen);

        uavStationService.sendMessage(uavStationDTO);
    }

    /**
     * 打开或者关闭导弹测控站
     * @param isOpen
     */
    @Override
    public void switchMissileStation(int isOpen) {
        MissileDTO.MessageWord word = new MissileDTO.MessageWord();
        word.setData(0);
        word.setEncode(isOpen);
        word.setTableNo(20425);

        MissileDTO missileDTO = new MissileDTO();
        missileDTO.setType(4);// 测试指令，不带参数
        missileDTO.setWords(Collections.singletonList(word));

        log.info("对导弹测控站发送开关指令，指令内容：{}", missileDTO);
        missileService.sendMessage(missileDTO);
    }

    @Override
    public void testSwitchStation() {
        notifySwitchComplete("1", 30, null, NodeSwitchStateEnum.PENDING);
    }

    @Override
    public void switchSpaceStation(String stationId, int isOpen) {
        SpaceTargetControlDTO targetControlDTO = new SpaceTargetControlDTO();
        targetControlDTO.setMainSwitch(1);
        targetControlDTO.setStationId(stationId);

        SpaceTargetControlDTO.TargetControlCommand command1 = new SpaceTargetControlDTO.TargetControlCommand();
        command1.setSeq(1);
        command1.setIsOpen(isOpen);

        SpaceTargetControlDTO.TargetControlCommand command2= new SpaceTargetControlDTO.TargetControlCommand();
        command2.setSeq(2);
        command2.setIsOpen(2);

        SpaceTargetControlDTO.TargetControlCommand command3 = new SpaceTargetControlDTO.TargetControlCommand();
        command3.setSeq(3);
        command3.setIsOpen(2);

        SpaceTargetControlDTO.TargetControlCommand command4 = new SpaceTargetControlDTO.TargetControlCommand();
        command4.setSeq(4);
        command4.setIsOpen(2);
        targetControlDTO.setCommands(Arrays.asList(command1, command2, command3, command4));

        // 打开对应站点的开关
        log.info("【节点切换】已对航天站点：{}发送开关指令：{}", stationId, isOpen);
        stationSpaceStatusService.spaceCommand(targetControlDTO);
    }

    private void sendMachineChange(Map<String, Object> message) {
        Map<String, Object> msg = getMapValue(message);
        NodeSelection.Output output = (NodeSelection.Output) msg.get("sysAntJResult");

        if (output != null && output.getSlctEn() == 1) {
            TerminalMachineDTO terminalMachineDTO = new TerminalMachineDTO();
            terminalMachineDTO.setTargetNode(output.getTarID());

            // 获取终端映射配置
            List<TerminalConfigDTO> terminalConfig = Constants.terminalConfig;

            TerminalConfigDTO configDTO = terminalConfig.stream().filter(ter -> ter.getNodeId().equals(output.getNodeID() + "")).findAny().get();

            if (configDTO != null) {
                terminalMachineDTO.setRxFreq((int) configDTO.getRxFreq());
                terminalMachineDTO.setModeCtrl(output.getTerMode());
                terminalMachineDTO.setTxFreq((int) configDTO.getTxFreq());
                terminalMachineDTO.setTxAtte((int) configDTO.getTxAtte());

                // 发送消息
                log.info("【节点切换】发送整机干扰帧：{}", terminalMachineDTO);
                sendMachineParams(terminalMachineDTO);
            } else {
                log.error("【节点切换】终端ID未找到匹配项，请检查参数配置");
            }
        }
    }

    private void findAntiJResult(Map<String, Object> message) {
        Map<String, Object> msg = getMapValue(message);
        NodeSelection.Output output = (NodeSelection.Output) msg.get("sysAntJResult");

        if (output != null && output.getSysAntj() != 0) {
            // 获取映射关系表
            List<TerminalConfigDTO> terminalConfig = Constants.terminalConfig;

            TerminalConfigDTO result = terminalConfig.stream()
                    .filter(t -> t.getNodeId().equals(String.valueOf(output.getNodeID())))
                    .findAny().get();

            msg.put("terminalConfigResult", result);
        }
    }

    private void sendAntiJamming(Map<String, Object> message) {
        Map<String, Object> msg = getMapValue(message);
        NodeSelection.Output output = (NodeSelection.Output) msg.get("sysAntJResult");
        // 为1才需要发送
        if (output != null && output.getSlctEn() == 1) {
            TerminalAntiJammingDTO antiJammingDTO = new TerminalAntiJammingDTO();

            antiJammingDTO.setTargetNode(output.getTarID());
            antiJammingDTO.setDecisionMethod(2);
            antiJammingDTO.setCounterInterferenceMeasures(5);
            antiJammingDTO.setSwitchLink(output.getAccHandEn());
            antiJammingDTO.setSwitchLinkCode(0);

            // 发送抗干扰帧
            sendAntiJammingParams(antiJammingDTO);
            log.info("【节点切换】根据接入决策结果发送抗干扰帧成功:{}", antiJammingDTO);

            // 发送交互日志
            server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("根据接入决策结果发送抗干扰帧成功", InteractionSourceEnum.SELECTION_LOG )));
        }
    }

    private Map<String, Object> getMapValue(Map<String, Object> message) {
        String terminalId = message.keySet().stream().findAny().get();
        return  (Map<String, Object>) message.get(terminalId);
    }

    private NodeSelection.Output nodeSelection(Map<String, Object> message) {
        String terminalId = message.keySet().stream().findAny().get();
        if (terminalId.equals("0")) return null;

        // 根据当前终端体制获取对应节点ID
        Map<String, Object> msg = getMapValue(message);
        long terCurrentNode = NodeSelection.getTerCurrentNode(Integer.parseInt(terminalId));
        msg.put("terCurrentNode", terCurrentNode + "");

        // 获取当前接入节点名称
        String stationStr = RedisUtil.StringOps.get(STATION_INFO + terCurrentNode);
        if (StringUtils.isNotBlank(stationStr)) {
            JSONObject stationJson = JSON.parseObject(stationStr);
            msg.put("curStationName", stationJson.getString("name"));
        }

        // 2025.06.05 ADD 开关
//        String open = RedisUtil.StringOps.get(NODE_SELECT);
//        if (open.equals("0")) return null;

        // 检查是否已有处理中的请求
        String switchKey = NODE_SWITCH + terminalId;

        Map<Object, Object> switchState = RedisUtil.HashOps.hEntries(switchKey);

        // 场景1：已有待处理的切换请求
        if (switchState.containsKey("status")) {
            log.info("终端{}已有待处理的切换请求", terminalId);
            return null;
        }

        // 场景2：首次处理（原子性操作）
        String lockKey = CacheConstant.NODE_SELECT_LOCK + terminalId;
        if (StringUtils.isBlank(RedisUtil.StringOps.get(lockKey))) {
            Map<String, Object> dataMap = (Map<String, Object>) message.get(terminalId);

            // 调用算法获取结果
            NodeSelection.Output output = invokeNodeAccess(terminalId, dataMap);
//            NodeSelection.Output output = new NodeSelection.Output(1, 1, 1, 1, 1, 1, 1);
            dataMap.put("sysAntJResult", output);

            if (output != null && output.getSlctEn() == 1) {
                log.info("【节点切换】开始切换节点");
                // 原子性创建切换流程
                String luaScript =
                        "if redis.call('SETNX', KEYS[1], '1') == 1 then " +
                                "redis.call('HSET', KEYS[2], 'status', 'pending') " +
                                "redis.call('HSET', KEYS[2], 'createTime', ARGV[1]) " +
                                "redis.call('HSET', KEYS[2], 'algorithmResult', ARGV[2]) " +
                                "redis.call('EXPIRE', KEYS[1], 60) " + // 这个切换后的锁先设置成60秒，如果触发手动切换则重置该key过期时间为30秒
                                "redis.call('EXPIRE', KEYS[2], 60) " +
                                "return 1 " +
                                "else " +
                                "return 0 " +
                                "end";


                List<String> keys = Arrays.asList(lockKey, switchKey);
                List<String> args = Arrays.asList(
                        String.valueOf(System.currentTimeMillis()),
                        JSON.toJSONString(output)
                );

                RedisUtil.getRedisTemplate().execute(
                        new DefaultRedisScript<>(luaScript, Long.class),
                        keys,
                        args.toArray()
                );
            } else {
                log.info("【节点切换】本次无需切换节点");
            }

//            if (result == 1) {
//                // 启动前端通知流程
////                notifyFrontend(terminalId, output);
//            }
            return output;
        } else {
            log.info("终端：{},正在做节点切换中...", terminalId);
            return null;
        }
    }

    public void executeFinalSwitch(String terminalId) {
        log.info("【节点切换】开始节点切换");
        // 加锁
        // 1. 获取算法结果
        String resultJson = (String) RedisUtil.HashOps.hGet(
                NODE_SWITCH + terminalId,
                "algorithmResult"
        );
        NodeSelection.Output output = JSON.parseObject(resultJson, NodeSelection.Output.class);

        // 2. 构建消息体
        Map<String, Object> message = new HashMap<>();
        message.put(terminalId, new HashMap<String, Object>(){{
            put("sysAntJResult", output);
        }});

        // 3. 发送关键帧（同步顺序执行）
        sendAntiJamming(message);    // 抗干扰帧
        sendMachineChange(message);  // 整机控制帧
        sendStationChange(message);  // 节点切换信号

        // 4. 清理资源
        cleanupResources(terminalId);

        // 5. 通知前端切换完成
        notifySwitchComplete(terminalId, 0, null, NodeSwitchStateEnum.SUCCESS);
    }

    private void notifySwitchComplete(String terminalId, long timeLeft, Object algorithmResult, Object status) {
        WsMessageDTO wsMessageDTO = new WsMessageDTO();
        wsMessageDTO.setType(WebSocketTypeEnum.GLOBAL_NODE_SWITCH.getCode());
        Map<String, Object> msg = new HashMap<>();

        if (algorithmResult != null) {
            JSONObject outJson = JSON.parseObject(algorithmResult.toString());

            // 获取需要接入的节点信息
            String nodeId = outJson.getString("nodeID");

            String stationInfo = RedisUtil.StringOps.get(STATION_INFO + nodeId);
            if (StringUtils.isNotBlank(stationInfo)) {
                JSONObject stationObj = JSON.parseObject(stationInfo);
                msg.put("station", stationObj.getString("name"));
            }
        }

        // 获取终端对应的目标名称
        String targetStr = RedisUtil.StringOps.get(TERMINAL_NODE_PREFIX + terminalId);
        if (StringUtils.isNotBlank(targetStr)) {
            JSONObject targetObj = JSON.parseObject(targetStr);
            msg.put("target", targetObj.getString("name"));
        }

        msg.put("timeLeft", timeLeft);
        msg.put("sourceNode", terminalId);
        msg.put("output", algorithmResult);
        msg.put("state", status);

        wsMessageDTO.setData(msg);

        log.info("【节点切换】当前节点：{}，切换状态为{}", terminalId, status);

        // 推送消息给前端
        server.sendAll(JSON.toJSONString(wsMessageDTO));
    }

    private void cleanupResources(String terminalId) {
        // 清除锁定状态缓存
        redisService.deleteKeys(TERMINAL_MODE_STATUS);
    }

    @Override
    public void notifyFrontNodeSwitch(long timeLeft, Map<Object, Object> state, String key) {
        // 构建信息
        String sourceNode = StringUtils.replace(key, NODE_SWITCH, "");

        // 通知前端当前状态为 PENDING
        notifySwitchComplete(sourceNode, timeLeft, state.get("algorithmResult"), state.get("status"));
    }

    @Override
    public void confirmSwitch(NodeSelectDTO nodeSelectDTO) {
        String key = NODE_SWITCH + nodeSelectDTO.getTerminalId();
        String luaScript =
                "if redis.call('HGET', KEYS[1], 'status') == 'pending' then " +
                        "redis.call('HSET', KEYS[1], 'status', 'confirmed') " +
                        "return 1 " +
                        "else " +
                        "return 0 " +
                        "end";

        Long result = RedisUtil.getRedisTemplate().execute(
                new DefaultRedisScript<>(luaScript, Long.class),
                Collections.singletonList(key),
                new Object[]{}
        );

        if (result != null && result == 1) {
            log.info("【节点切换】手动确认切换成功");
            // 发送交互日志
            server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("【节点切换】手动确认切换成功", InteractionSourceEnum.SELECTION_LOG )));
            // 这里不调用通知方法，因为在最后节点切换结束的时候会发送成功或者失败的消息
        } else {
            Map<Object, Object> state = RedisUtil.HashOps.hEntries(key);
            log.error("【节点切换】手动确认切换失败, 当前缓存状态为：{}", state);

            // 通知前端当前状态为 FAILED
            notifySwitchComplete(nodeSelectDTO.getTerminalId(), 0, state.get("algorithmResult"), NodeSwitchStateEnum.FAILED);

            server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.errorLog("【节点切换】手动确认切换失败", InteractionSourceEnum.SELECTION_LOG )));
        }
    }

    @Override
    public void cancelSwitch(NodeSelectDTO nodeSelectDTO) {
        log.info("【节点切换】用户取消节点切换操作，将删除缓存key");

        String switchKey = NODE_SWITCH + nodeSelectDTO.getTerminalId();
        String lockKey = CacheConstant.NODE_SELECT_LOCK + nodeSelectDTO.getTerminalId();

//        RedisUtil.KeyOps.expire(switchKey, 30, TimeUnit.SECONDS);
        String luaScript =
                "if redis.call('HGET', KEYS[1], 'status') == 'pending' then " +
                        "redis.call('HSET', KEYS[1], 'status', 'cancel') " +
                        "return 1 " +
                        "else " +
                        "return 0 " +
                        "end";

        Long result = RedisUtil.getRedisTemplate().execute(
                new DefaultRedisScript<>(luaScript, Long.class),
                Collections.singletonList(switchKey),
                new Object[]{}
        );
        RedisUtil.KeyOps.expire(lockKey, 30, TimeUnit.SECONDS);

        if (result != null && result == 1) {
            log.info("【节点切换】Keys deleted successfully.");
        }

        // 发送交互日志
        server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("【节点切换】手动取消切换成功", InteractionSourceEnum.SELECTION_LOG )));

        // 通知前端当前状态为 CANCEL
        notifySwitchComplete(nodeSelectDTO.getTerminalId(), 0, null, NodeSwitchStateEnum.CANCEL);
    }

//    private NodeSelection.Output nodeSelection(Map<String, Object> message) {
//        String terminalId = message.keySet().stream().findAny().get();
//
//        // 0号节点无需往下执行
//        if (terminalId.equals("0")) return null;
//
//        // 获取终端锁，若不为空则表示还在做切换，需等待key过期
//        String lockFlag = RedisUtil.StringOps.get(CacheConstant.NODE_SELECT_LOCK + terminalId);
//        if (StringUtils.isBlank(lockFlag)) {
//            Map<String, Object> dataMap = (Map<String, Object>) message.get(terminalId);
//
//            // 调用接入算法
//            NodeSelection.Output output = invokeNodeAccess(terminalId, dataMap);
//
//            dataMap.put("sysAntJResult", output);
//
//            // 节点切换增加同步锁
//            nodeSelectionLock(message, terminalId);
//
//            // 向终端发送抗干扰帧
//            sendAntiJamming(message);
//
//            // 向终端发送整机控制帧
//            sendMachineChange(message);
//
//            // 发送节点切换信号
//            sendStationChange(message);
//
//            return output;
//        } else {
//            log.info("终端：{},正在做节点切换中...", terminalId);
//            return null;
//        }
//
//
//    }

    /**
     * 节点切换增加同步锁
     * @param message
     * @param terminalId
     */
    private void nodeSelectionLock(Map<String, Object> message, String terminalId) {
        Map<String, Object> msg = getMapValue(message);
        NodeSelection.Output output = (NodeSelection.Output) msg.get("sysAntJResult");

        // 为1表示做节点切换
        if (output != null && output.getSlctEn() == 1) {
            RedisUtil.StringOps.setEx(CacheConstant.NODE_SELECT_LOCK + terminalId, terminalId, 10, TimeUnit.SECONDS);
        }
    }

    private NodeSelection.Output invokeNodeAccess(String terminalId, Map<String, Object> dataMap) {
        // 获取抗干扰帧
        Map<String, Object> antiJammingFrame = (Map<String, Object>) dataMap.get("antiJammingFrame");
        Short yyxhqd = (Short) antiJammingFrame.get("yyxhqd");
        Short kgrsdjcjg = (Short) antiJammingFrame.get("kgrsdjcjg");
        Integer kgrzxqk = (Integer) antiJammingFrame.get("kgrzxqk");
        Short yyxhyw = (Short) antiJammingFrame.get("yyxhyw");
        Short grxhyw = (Short) antiJammingFrame.get("grxh");

        // 获取建链帧
        Map<String, Object> createLinkDataFrame = (Map<String, Object>) dataMap.get("createLinkDataFrame");
        double latitude = createLinkDataFrame != null ? (double) createLinkDataFrame.get("latitude") : 0;
        double longitude = createLinkDataFrame != null ? (double) createLinkDataFrame.get("longitude") : 0;
        double altitude = createLinkDataFrame != null ? (double) createLinkDataFrame.get("altitude") : 0;
        Object apply = createLinkDataFrame != null ? createLinkDataFrame.get("apply") : 0;

        // 获取当前终端状态
        String status = RedisUtil.StringOps.get(CacheConstant.TERMINAL_SINGLE_STATUS + terminalId);

        /** 2025.03.27 ADD 获取并转换终端传入的体制信息 1：航天 2：DD 3：无人机 **/
        Map<String, Object> mode = convertMode(terminalId, dataMap);

        int sta = 1;
        if (StringUtils.isNotBlank(status)) sta = Integer.parseInt(status);

        // 构建终端参数
        NodeSelection.Terminal terminal = new NodeSelection.Terminal(Integer.parseInt(terminalId),
                new double[]{latitude, longitude, altitude}, yyxhyw, yyxhqd, Integer.parseInt(apply.toString()), kgrsdjcjg, kgrzxqk, sta, mode, grxhyw);

        // 构建节点参数
//        List<DataGeneralDTO> dataGeneralDTOS = dataGeneralService.queryEquipmentByTypes(Arrays.asList(1, 2));
//
//        // 查询资源节点数据
//        List<Map<String, Object>> nodes = new ArrayList<>();
//        for (DataGeneralDTO dataGeneralDTO : dataGeneralDTOS) {
//            IPage<Map<String, Object>> data = commonMapper.getData(new Page<>(1, 100), dataGeneralDTO.getTableName(), null, null, null);
//
//            nodes.addAll(data.getRecords());
//        }

        // 获取实时上报站点数据
        // 查询映射关系表 2 表示航天站
        List<SysDataMapping> mapping = dataMappingService.queryDataMapping(Arrays.asList(2));

        // 从缓存中获取当前正在使用的节点
        String currentStation = RedisUtil.StringOps.get(CacheConstant.TERMINAL_CURRENT_STATION + terminalId);
        long currentNode = StringUtils.isNotBlank(currentStation) ? Long.parseLong(currentStation) : 0;

        List<Map<String, Object>> nodes = new ArrayList<>();
        double ebn0 = 0;
        for (SysDataMapping dataMapping : mapping) {
            String value = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_TIME + dataMapping.getDataKey());

            if (StringUtils.isNotBlank(value)) {
                Map<String, Object> node = JSON.parseObject(value, new TypeReference<Map<String, Object>>(){});

                // 判断当前上报节点是否在预规划的时间内
                LocalDateTime now = LocalDateTime.now();
                int count = dataEquipmentOccupancyService.count(Wrappers.<DataEquipmentOccupancy>lambdaQuery()
                        .eq(DataEquipmentOccupancy::getEquipmentId, node.get("id"))
                        .ge(DataEquipmentOccupancy::getStartTime, now)
                        .le(DataEquipmentOccupancy::getEndTime, now));

                node.put("used", count > 0 ? 0 : 1);
                nodes.add(node);
            }

            /** 获取当前使用的实时上报站点信息 **/
            
            // 先根据当前使用的节点ID获取对应的缓存ID, 不是对应站点则无需处理
            if (dataMapping.getDataValue().contains(currentNode + "")) {
                String station = RedisUtil.StringOps.get(CacheConstant.STATION_REAL_DATA + dataMapping.getDataKey());
                
                if (StringUtils.isNotBlank(station)) {
                    LinkedList<Map<String, Object>> stationList = JSON.parseObject(station, new TypeReference<LinkedList<Map<String, Object>>>(){});

                    Map<String, Object> stationMap;
                    if (stationList.size() > 1) {
                        int terId = Integer.parseInt(terminalId);
                        stationMap = stationList.get(terId == 5 ? terId - 2 : terId - 1);
                    } else {
                        // 如果只有一个元素那当前使用的站点是导弹或者无人机，直接获取第一个即可
                        stationMap = stationList.get(0);
                    }

                    ebn0 = stationMap.get("ebno") != null ? Double.parseDouble(stationMap.get("ebno").toString()) / 10 : 0;
                }
            }
        }

        NodeSelection.Node[] nodeList = nodes.stream()
                .map(node -> {
                    BigDecimal lat = (BigDecimal) node.get("latitude");
                    BigDecimal lng = (BigDecimal) node.get("longitude");
                    BigDecimal alt = (BigDecimal) node.get("altitude");
                    String nodeId = node.get("id").toString();
                    BigDecimal maxDistance = node.get("radius") != null ? (BigDecimal) (node.get("radius")) : (BigDecimal) (node.get("bottomRadius"));
                    int used = Integer.parseInt(node.get("used").toString());

                    return new NodeSelection.Node(new double[]{lat.doubleValue(), lng.doubleValue(), alt.doubleValue()}, new long[]{Long.parseLong(nodeId), (int) maxDistance.doubleValue(), used});
                }).toArray(NodeSelection.Node[]::new);

        // 查询已使用的终端序号
        List<TerminalInfo> usedTerminal = terminalInfoService.list(Wrappers.<TerminalInfo>lambdaQuery().eq(TerminalInfo::getTerminalStatus, 1));

        int[] inSchT = usedTerminal.stream()
                .map(TerminalInfo::getTerminalSeq).mapToInt(Integer::intValue).toArray();

        // 调用接入算法
        LocalDateTime start = LocalDateTime.now();
        NodeSelection.Output output = NodeSelection.func_syjr(terminal, nodeList, inSchT, currentNode, RSS, ebn0);
        LocalDateTime end = LocalDateTime.now();
        log.info("调用接入算法结果：{}，耗时:{}ms", output, Duration.between(start, end).toMillis());

        // 结果存入终端状态表中
        if (output.getSlctEn() == 1) {
            terminalInfoService.updateTerminalStatus(output.getTarID(), 1);
        }

        // 将当前使用站点存入缓存，下次使用
        RedisUtil.StringOps.setEx(CacheConstant.TERMINAL_CURRENT_STATION + terminalId, output.getNodeID() + "", 1, TimeUnit.HOURS);

        return output;
    }

    /**
     * 判断终端传入体制，封装成以下格式 1：航天 2：DD 3：无人机
     * @param terminalSeq 终端编号
     * @param dataMap 数据映射
     */
    private Map<String, Object> convertMode(String terminalSeq, Map<String, Object> dataMap) {
        Map<String, Object> result = new HashMap<>();

        // 航天测控
        Map<String, Object> spaceFrame = (Map<String, Object>) dataMap.get("spaceFrame");

        // DD测控
        Map<String, Object> missileFrame = (Map<String, Object>) dataMap.get("missileFrame");

        // 无人机测控
        Map<String, Object> droneFrame = (Map<String, Object>) dataMap.get("droneFrame");

        if (missileFrame != null) {
            result.put("type", 2);
            result.put("data", missileFrame);

            // 缓存
            cacheMode(terminalSeq, missileFrame);
        }
        if (droneFrame != null) {
            result.put("type", 3);
            result.put("data", droneFrame);

            // 缓存
            cacheMode(terminalSeq, droneFrame);
        }
        if (spaceFrame != null) {
            result.put("type", 1);
            result.put("data", spaceFrame);

            // 缓存
            cacheMode(terminalSeq, spaceFrame);
        }

        return result;
    }

    private void cacheMode(String terminalSeq, Map<String, Object> frameData) {
        // 对frameData中的所有value进行与运算
        int andResult = 1; // 与运算的初始值为1

        for (Map.Entry<String, Object> entry : frameData.entrySet()) {
            Object value = entry.getValue();
            int intValue;

            // 将value转换为int，如果是误码字符串则判定为0
            try {
                if (value instanceof Number) {
                    intValue = ((Number) value).intValue();
                } else {
                    String strValue = value.toString().trim();
                    // 尝试解析为整数，如果失败则认为是误码字符串，设为0
                    if ("0".equals(strValue) || "1".equals(strValue)) {
                        intValue = Integer.parseInt(strValue);
                    } else {
                        intValue = 0; // 误码字符串判定为0
                    }
                }

                // 确保值只能是0或1
                intValue = (intValue == 1) ? 1 : 0;
            } catch (Exception e) {
                // 解析异常时判定为0
                intValue = 0;
            }

            // 进行与运算
            andResult &= intValue;
        }

        // 构建缓存key，不再包含字段名，只存储与运算结果
        String cacheKey = TERMINAL_MODE_STATUS + terminalSeq + ":and_result";

        // 先从缓存中获取数据，如果不为空则判断value的值
        Map<Object, Object> cache = RedisUtil.HashOps.hEntries(cacheKey);

        Map<String, Object> cacheMap = new HashMap<>();
        cache.forEach((k, v) -> cacheMap.put(String.valueOf(k), v));

        if (CollUtil.isNotEmpty(cacheMap)) {
            // 如果与运算结果没有变更则不需要修改时间，反之需要修改值和时间
            String cachedValue = cacheMap.get("value") != null ? cacheMap.get("value").toString() : "";
            if (!cachedValue.equals(String.valueOf(andResult))) {
                // 记录改变前的时间（即当前缓存中的startTime作为endTime）
                String previousStartTime = cacheMap.get("startTime") != null ? cacheMap.get("startTime").toString() : null;
                if (previousStartTime != null) {
                    cacheMap.put("endTime", previousStartTime);
                }

                cacheMap.put("value", String.valueOf(andResult));
                cacheMap.put("startTime", DateUtil.now());

                // 更新缓存
                RedisUtil.HashOps.hPutAll0(cacheKey, cacheMap);
            }
        } else {
            Map<String, String> map = new HashMap<>();
            map.put("value", String.valueOf(andResult));
            map.put("startTime", DateUtil.now());
            // 初始状态没有endTime

            RedisUtil.HashOps.hPutAll(cacheKey, map);
        }
    }

    private void queryDataMapping(Map<String, Object> message) {
        // 查询终端类型映射关系表
        List<SysDataMapping> terminalMapping = dataMappingService.queryDataMapping(Arrays.asList(1));

        for (Map.Entry<String, Object> entry : message.entrySet()) {
            String key = entry.getKey();
            Map<String, Object> value = (Map<String, Object>) entry.getValue();

            // 查询缓存
            String targetValue = RedisUtil.StringOps.get(TERMINAL_NODE_PREFIX + key);
            Map<String, Object> targetMap;
            if (StringUtils.isBlank(targetValue)) {
                Map<String, List<SysDataMapping>> mapping = terminalMapping.stream()
                        .collect(Collectors.groupingBy(SysDataMapping::getDataKey));

                List<SysDataMapping> dataMappings = mapping.get(key);
                if (CollUtil.isEmpty(dataMappings)) return;

                SysDataMapping dataMapping = dataMappings.get(0);

                String[] strs = dataMapping.getDataValue().split("_");
                String generalId = strs[0];
                String id = strs[1];


                DataGeneral dataGeneral = dataGeneralService.getById(generalId);
                targetMap = commonMapper.getOne(dataGeneral.getTableName(), id);

                // 获取图片
                String imgUrl = fileService.getImageByType(dataGeneral.getDataType());

                targetMap.put("imgUrl", imgUrl);
                targetMap.put("targetTypeValue", dataGeneral.getTableComment());
                targetMap.put("dataType", dataGeneral.getDataType());

                RedisUtil.StringOps.setEx(TERMINAL_NODE_PREFIX + key, JSON.toJSONString(targetMap), 10, TimeUnit.MINUTES);
            } else {
                targetMap = JSON.parseObject(targetValue, new TypeReference<Map<String, Object>>() {});
            }

            value.put("bindingTarget", targetMap);
        }
    }

    private void sendMessage2Front(Map<String, Object> message) {
        WsMessageDTO dto = new WsMessageDTO();
        dto.setData(message);
        dto.setType(WebSocketTypeEnum.FULL_VIEW_TERMINAL.getCode());

//        Map<String, Object> copyMessage = copyMessage(message);
//        if (CollUtil.isNotEmpty(copyMessage)) {
//            WsMessageDTO copy = new WsMessageDTO();
//            copy.setData(copyMessage);
//            copy.setType(WebSocketTypeEnum.FULL_VIEW_TERMINAL.getCode());
//
//            server.sendAll(JSON.toJSONString(copy));
//        }

        server.sendAll(JSON.toJSONString(dto));
    }

    private Map<String, Object> copyMessage(Map<String, Object> message) {
        Map<String, Object> copy = new HashMap<>();
        for (Map.Entry<String, Object> entry : message.entrySet()) {
            if (entry.getKey().equalsIgnoreCase("4")) {
                Map<String, Object> value = (Map<String, Object>) entry.getValue();

                Map<String, Object> newMap = ObjectUtil.cloneByStream(value);

                Map<String, Object> shipTarget = commonMapper.getOne("data_ship_target", "1837788377185783810");
                String generalId = shipTarget.get("generalId").toString();

                DataGeneral dataGeneral = dataGeneralService.getById(generalId);
                shipTarget.put("targetTypeValue", dataGeneral.getTableComment());
                newMap.put("bindingTarget", shipTarget);

                copy.put("6", newMap);

            }

        }

        return copy;
    }

    /**
     * *******  整机参数设置帧
     * @param terminalMachineDTO
     */
    @Override
    public void sendMachineParams(TerminalMachineDTO terminalMachineDTO) {
        ByteBuf byteBuf = Unpooled.buffer();

        // 构建头部信息
        packageMessageHead(byteBuf, terminalMachineDTO.getTargetNode(), 69);

        // 构建报文体
        packageMachineParams(byteBuf, terminalMachineDTO);

        // 发送消息
        log.info("向终端：{}，发送整机设置帧：{}", terminalMachineDTO.getTargetNode(), terminalMachineDTO);
        server.sendInteractionLog(JSON.toJSONString(InteractionLogDTO.infoLog("向终端：" + terminalMachineDTO.getTargetNode() + "发送整机设置帧", InteractionSourceEnum.TERMINAL_STATE)));
        sendMessage(byteBuf);
    }

    /**
     * *******  抗干扰控制帧
     * @param terminalAntiJammingDTO
     */
    @Override
    public void sendAntiJammingParams(TerminalAntiJammingDTO terminalAntiJammingDTO) {
        ByteBuf byteBuf = Unpooled.buffer();

        // 构建头部信息
        packageMessageHead(byteBuf, terminalAntiJammingDTO.getTargetNode(), 61);

        // 构建报文体
        packageAntiJammingParams(byteBuf, terminalAntiJammingDTO);

        // 发送消息
        log.info("向终端发送抗干扰帧，内容：{}", terminalAntiJammingDTO);
        sendMessage(byteBuf);
    }

    @Override
    public void dataTransfer(TerminalDataTransferDTO dataTransferDTO) {
        ByteBuf byteBuf = Unpooled.buffer();

        // 封装报文头
        packageMessageHead(byteBuf, dataTransferDTO.getTargetNode(), 61);

        // 报文体
        packageDataTransferParams(byteBuf, dataTransferDTO);

        // 发送
        log.info("向终端发送数据传输控制帧：{}", dataTransferDTO);
        sendMessage(byteBuf);
    }

    private void sendMessage(ByteBuf byteBuf) {
        Map<String, UdpMulticastSender> senders = runner.getSenders();
        UdpMulticastSender sender = senders.get(ClientTypeEnum.TERMINAL.getMessage());

        if (sender != null) {
            sender.sendMessage(byteBuf);
        }
    }

    /**
     * 2.1.2.7  建链申请数据帧（58 byte）
     * @param byteBuf
     * @return
     */
    private Map<String, Object> parseCreateLinkDataFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> createLinkDataFrame = new LinkedList<>();

        // 保留 40
        FixedLengthField reverse = new FixedLengthField("reverse", 40);
        createLinkDataFrame.add(reverse);

        // 经度 4
        PositionField longitude = new PositionField("longitude");
        createLinkDataFrame.add(longitude);

        // 纬度 4
        PositionField latitude = new PositionField("latitude");
        createLinkDataFrame.add(latitude);

        // 高度 4
        IntegerField altitude = new IntegerField("altitude");
        createLinkDataFrame.add(altitude);

        // 建链申请 1
        UnSignedByteField apply = new UnSignedByteField("apply");
        createLinkDataFrame.add(apply);

        // 建链情况 1
        UnSignedByteField applyState = new UnSignedByteField("applyState");
        createLinkDataFrame.add(applyState);

        Map<String, Object> createLinkDataFrameResult = BinaryParser.parseFieldsLE(byteBuf, createLinkDataFrame);

        // 单位转换
        createLinkDataFrameResult.put("altitude", (double) Integer.parseInt(createLinkDataFrameResult.get("altitude").toString()) / 100);

        log.info("解析建链申请数据帧完成，结果：{}", createLinkDataFrameResult);

        return createLinkDataFrameResult;
    }

    /**
     * 2.1.2.6  频谱数据帧（570 byte）
     * @param byteBuf
     * @return
     */
    private Map<String, Object> parseSpectrumFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> spectrumFrame = new LinkedList<>();

        // 保留 40
        FixedLengthField reverse = new FixedLengthField("reverse", 40);
        spectrumFrame.add(reverse);

        // 终端编号 1
        UnSignedByteField seq = new UnSignedByteField("seq");
        spectrumFrame.add(seq);

        // 数据类型 1
        UnSignedByteField type = new UnSignedByteField("type");
        spectrumFrame.add(type);

        // 频率起始值 4
        IntegerField startFrequency = new IntegerField("startFrequency");
        spectrumFrame.add(startFrequency);

        // 频率截止值 4
        IntegerField endFrequency = new IntegerField("endFrequency");
        spectrumFrame.add(endFrequency);

        // 频率间隔值 4
        IntegerField intervalFrequency = new IntegerField("interval");
        spectrumFrame.add(intervalFrequency);

        // 各频段频谱幅度值 512
        FixedLengthField spectrumValues = new FixedLengthField("spectrumValues", 512);
        spectrumFrame.add(spectrumValues);

        Map<String, Object> spectrumFrameResult = BinaryParser.parseFieldsLE(byteBuf, spectrumFrame);

        // 数据转换
        Object typeValue = Constants.terminalDataType.get(spectrumFrameResult.get("type").toString());
        spectrumFrameResult.put("typeValue", typeValue);

        spectrumFrameResult.put("startFrequency", (double) Integer.parseInt(spectrumFrameResult.get("startFrequency").toString()) / 1000);
        spectrumFrameResult.put("endFrequency", (double) Integer.parseInt(spectrumFrameResult.get("endFrequency").toString()) / 1000);
        spectrumFrameResult.put("interval", (double) Integer.parseInt(spectrumFrameResult.get("interval").toString()) / 1000);


        log.info("解析频谱数据帧完成，结果：{}", spectrumFrameResult);

        return spectrumFrameResult;
    }

    /**
     * 2.1.2.5  抗干扰状态帧（76 byte）
     * @param byteBuf
     * @return
     */
    private Map<String, Object> parseAntiJammingFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> antiJammingFrame = new LinkedList<>();
        // 保留 40
        FixedLengthField reverse = new FixedLengthField("reverse", 40);
        antiJammingFrame.add(reverse);

        // 终端编号 1
        UnSignedByteField seq = new UnSignedByteField("seq");
        antiJammingFrame.add(seq);

        // 干扰信号有无 1
        UnSignedByteField grxh = new UnSignedByteField("grxh");
        antiJammingFrame.add(grxh);

        // 干扰强度 1 I8?
        ByteField grqd = new ByteField("grqd");
        antiJammingFrame.add(grqd);

        // 干扰类型 1
        UnSignedByteField grlx = new UnSignedByteField("grlx");
        antiJammingFrame.add(grlx);

        // 有用信号有无 1
        UnSignedByteField yyxhyw = new UnSignedByteField("yyxhyw");
        antiJammingFrame.add(yyxhyw);

        // 有用信号强度 1
        ByteField yyxhqd = new ByteField("yyxhqd");
        antiJammingFrame.add(yyxhqd);

        // 抗干扰手段决策结果 1
        UnSignedByteField kgrsdjcjg = new UnSignedByteField("kgrsdjcjg");
        antiJammingFrame.add(kgrsdjcjg);

        // 系统级抗干扰开启状态 1
        UnSignedByteField kgrskqzt = new UnSignedByteField("kgrskqzt");
        antiJammingFrame.add(kgrskqzt);

        // 干扰频点 4
        IntegerField grpd = new IntegerField("grpd");
        antiJammingFrame.add(grpd);

        // 干扰带宽 4
        IntegerField grdk = new IntegerField("grdk");
        antiJammingFrame.add(grdk);

        // 信号频点 4
        IntegerField xhpd = new IntegerField("xhpd");
        antiJammingFrame.add(xhpd);

        // 信号带宽 4
        IntegerField xhdk = new IntegerField("xhdk");
        antiJammingFrame.add(xhdk);

        // 重构链路代号 4
        IntegerField cglldh = new IntegerField("cglldh");
        antiJammingFrame.add(cglldh);

        // 备用参数 4
        IntegerField kgrzxqk = new IntegerField("kgrzxqk");
        antiJammingFrame.add(kgrzxqk);

        Map<String, Object> antiJammingFrameResult = BinaryParser.parseFieldsLE(byteBuf, antiJammingFrame);
        log.info("解析抗干扰状态帧完成，结果：{}", antiJammingFrameResult);

        return antiJammingFrameResult;
    }

    /**
     * 航天测控体制状态帧（76 byte）
     * @param byteBuf
     * @return
     */
    private Map<String, Object> parseSpaceFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> spaceFrame = new LinkedList<>();
        // 保留 40
        FixedLengthField reverse = new FixedLengthField("reverse", 40);
        spaceFrame.add(reverse);

        // 载波锁定（遥控) 4
        IntegerField KP2WaveLockYk = new IntegerField("KP2WaveLockYk");
        spaceFrame.add(KP2WaveLockYk);

        // 伪码锁定（遥控） 4
        IntegerField KP2CodeLockYk = new IntegerField("KP2CodeLockYk");
        spaceFrame.add(KP2CodeLockYk);

        // 位锁定（遥控） 4
        IntegerField KP2BitLockYk = new IntegerField("KP2BitLockYk");
        spaceFrame.add(KP2BitLockYk);

        // 帧同步锁定（遥控） 4
        IntegerField KP2FrameLockYk = new IntegerField("KP2FrameLockYk");
        spaceFrame.add(KP2FrameLockYk);

        // 载波锁定（测量） 4
        IntegerField KP2WaveLockCl = new IntegerField("KP2WaveLockCl");
        spaceFrame.add(KP2WaveLockCl);

        // 伪码锁定（测量） 4
        IntegerField KP2CodeLockCl = new IntegerField("KP2CodeLockCl");
        spaceFrame.add(KP2CodeLockCl);

        // 位锁定（测量） 4
        IntegerField KP2BitLockCl = new IntegerField("KP2BitLockCl");
        spaceFrame.add(KP2BitLockCl);

        // 帧同步锁定（测量） 4
        IntegerField KP2FrameLockCl = new IntegerField("KP2FrameLockCl");
        spaceFrame.add(KP2FrameLockCl);

        Map<String, Object> spaceFrameResult = BinaryParser.parseFieldsLE(byteBuf, spaceFrame);
        log.info("解析航天测控体制状态帧完成，结果：{}", spaceFrameResult);

        return spaceFrameResult;
    }

    /**
     * 无人机测控体制状态帧
     * @param byteBuf
     * @return
     */
    private Map<String, Object> parseDroneFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> droneFrame = new LinkedList<>();
        // 保留 40
        FixedLengthField reverse = new FixedLengthField("reverse", 40);
        droneFrame.add(reverse);

        // 载波锁定（遥测） 4
        IntegerField ycCWaveStat = new IntegerField("ycCWaveStat");
        droneFrame.add(ycCWaveStat);

        // 码锁定（遥测） 4
        IntegerField ycPNStat = new IntegerField("ycPNStat");
        droneFrame.add(ycPNStat);

        // 帧同步锁定（遥测） 4
        IntegerField ycFrameStat = new IntegerField("ycFrameStat");
        droneFrame.add(ycFrameStat);

        // 载波锁定（测量） 4
        IntegerField clCWaveStat = new IntegerField("clCWaveStat");
        droneFrame.add(clCWaveStat);

        // 码锁定（测量） 4
        IntegerField clPNStat = new IntegerField("clPNStat");
        droneFrame.add(clPNStat);

        // 帧同步锁定（测量） 4
        IntegerField clFrameStat = new IntegerField("clFrameStat");
        droneFrame.add(clFrameStat);

        Map<String, Object> droneFrameResult = BinaryParser.parseFieldsLE(byteBuf, droneFrame);
        log.info("解析无人机测控体制状态帧完成，结果：{}", droneFrameResult);

        return droneFrameResult;
    }

    /**
     * 整机状态帧 80byte
     * @param byteBuf
     * @return
     */
    private Map<String, Object> parseMachineFrame(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> machineFrame = new LinkedList<>();
        // 命令码 4
        IntegerField cmd = new IntegerField("cmd");
        machineFrame.add(cmd);

        // 保留 20
        FixedLengthField reverse = new FixedLengthField("reverse", 20);
        machineFrame.add(reverse);

        /** 2025.05.19 ADD **/
        // 随遇板FPGA温度值 4
        IntegerField syFPGATemp = new IntegerField("syFPGATemp");
        machineFrame.add(syFPGATemp);

        // 随遇板FPGA电压值 4
        IntegerField syFPGAVolt = new IntegerField("syFPGAVolt");
        machineFrame.add(syFPGAVolt);

        // 随遇板捕获频偏 4
        IntegerField sybphpp = new IntegerField("sybphpp");
        machineFrame.add(sybphpp);

        // 随遇板信噪比 4
        IntegerField sybxzb = new IntegerField("sybxzb");
        machineFrame.add(sybxzb);

        // 随遇板误帧数/S 4
        IntegerField sybwzs = new IntegerField("sybwzs");
        machineFrame.add(sybwzs);

        /** 2025.05.19 ADD **/

        // 测控板FPGA温度值 4
        IntegerField ckFPGAtemp = new IntegerField("ckFPGATemp");
        machineFrame.add(ckFPGAtemp);

        // 测控板FPGA电压值 4
        IntegerField ckFPGAVolt = new IntegerField("ckFPGAVolt");
        machineFrame.add(ckFPGAVolt);

        // TDD信道状态 4  电流 1
        UnSignedByteField tddElectricCurrent = new UnSignedByteField("tddElectricCurrent");
        machineFrame.add(tddElectricCurrent);

        // TDD信道状态 4  温度 1
        UnSignedByteField tddTemperature = new UnSignedByteField("tddTemperature");
        machineFrame.add(tddTemperature);

        // TDD信道状态 4  衰减值 1
        UnSignedByteField tddAttenuation = new UnSignedByteField("tddAttenuation");
        machineFrame.add(tddAttenuation);

        // TDD信道状态 4  锁定状态 1
        UnSignedByteField tddLockedStatus = new UnSignedByteField("tddLockedStatus");
        machineFrame.add(tddLockedStatus);

        // FDD信道状态 4  电流 1
        UnSignedByteField fddElectricCurrent = new UnSignedByteField("fddElectricCurrent");
        machineFrame.add(fddElectricCurrent);

        // FDD信道状态 4  温度 1
        UnSignedByteField fddTemperature = new UnSignedByteField("fddTemperature");
        machineFrame.add(fddTemperature);

        // FDD信道状态 4  衰减值 1
        UnSignedByteField fddAttenuation = new UnSignedByteField("fddAttenuation");
        machineFrame.add(fddAttenuation);

        // FDD信道状态 4  锁定状态 1
        UnSignedByteField fddLockedStatus = new UnSignedByteField("fddLockedStatus");
        machineFrame.add(fddLockedStatus);

        // 随遇板版本号 4
        IntegerField randomVersion = new IntegerField("randomVersion");
        machineFrame.add(randomVersion);

        // 测控板版本号 4
        IntegerField controlVersion = new IntegerField("controlVersion");
        machineFrame.add(controlVersion);

        // 随遇板收发频率 4
        IntegerField rsFreq = new IntegerField("rsFreq");
        machineFrame.add(rsFreq);

        // 测控板接收频率 4
        IntegerField rxFreq = new IntegerField("rxFreq");
        machineFrame.add(rxFreq);

        // 测控板发射频率 4
        IntegerField txFreq = new IntegerField("txFreq");
        machineFrame.add(txFreq);

        Map<String, Object> machineFrameResult = BinaryParser.parseFieldsLE(byteBuf, machineFrame);

        short tddLockedStatusByte = (short) machineFrameResult.get("tddLockedStatus");
        machineFrameResult.put("tddRevPllLock", tddLockedStatusByte & 1);
        machineFrameResult.put("tddLaunchPllLock", (tddLockedStatusByte >> 1) & 1);

        short fddLockedStatusByte = (short) machineFrameResult.get("fddLockedStatus");
        machineFrameResult.put("fddRevPllLock", fddLockedStatusByte & 1);
        machineFrameResult.put("fddLaunchPllLock", (fddLockedStatusByte >> 1) & 1);

        // 单位转换
        machineFrameResult.put("sybxzb", (double) Integer.parseInt(machineFrameResult.get("sybxzb").toString()) / 100);
        machineFrameResult.put("syFPGATemp", (double) Integer.parseInt(machineFrameResult.get("syFPGATemp").toString()) / 100);
        machineFrameResult.put("syFPGAVolt", (double) Integer.parseInt(machineFrameResult.get("syFPGAVolt").toString()) / 100);
        machineFrameResult.put("ckFPGATemp", (double) Integer.parseInt(machineFrameResult.get("ckFPGATemp").toString()) / 100);
        machineFrameResult.put("ckFPGAVolt", (double) Integer.parseInt(machineFrameResult.get("ckFPGAVolt").toString()) / 100);
        machineFrameResult.put("tddElectricCurrent", (double) Integer.parseInt(machineFrameResult.get("tddElectricCurrent").toString()) / 10);
        machineFrameResult.put("fddElectricCurrent", (double) Integer.parseInt(machineFrameResult.get("fddElectricCurrent").toString()) / 10);
        machineFrameResult.put("rsFreq", (double) Integer.parseInt(machineFrameResult.get("rsFreq").toString()) / 10);
        machineFrameResult.put("rxFreq", (double) Integer.parseInt(machineFrameResult.get("rxFreq").toString()) / 10);
        machineFrameResult.put("txFreq", (double) Integer.parseInt(machineFrameResult.get("txFreq").toString()) / 10);
        machineFrameResult.put("randomVersion", Integer.toHexString(Integer.parseInt(machineFrameResult.get("randomVersion").toString())));
        machineFrameResult.put("controlVersion", Integer.toHexString(Integer.parseInt(machineFrameResult.get("controlVersion").toString())));

        // 解析信道 锁定状态
        log.info("解析整机状态帧完成，结果：{}", machineFrameResult);

        return machineFrameResult;
    }

    private Map<String, Object> parseLinkMessage(ByteBuf byteBuf) {
        LinkedList<FieldDefinition> status = new LinkedList<>();
        // 源节点与0号节点连接状态 1
        UnSignedByteField status0 = new UnSignedByteField("node0");
        status.add(status0);

        // 源节点与1号节点连接状态 1
        UnSignedByteField status1 = new UnSignedByteField("node1");
        status.add(status1);

        // 源节点与2号节点连接状态 1
        UnSignedByteField status2 = new UnSignedByteField("node2");
        status.add(status2);

        // 源节点与3号节点连接状态 1
        UnSignedByteField status3 = new UnSignedByteField("node3");
        status.add(status3);

        // 源节点与4号节点连接状态 1
        UnSignedByteField status4 = new UnSignedByteField("node4");
        status.add(status4);

        // 源节点与5号节点连接状态 1
        UnSignedByteField status5 = new UnSignedByteField("node5");
        status.add(status5);

        Map<String, Object> statusMap = BinaryParser.parseFieldsLE(byteBuf, status);
        log.info("解析<拓扑连接信息帧>完成，结果：{}", statusMap);

        return statusMap;
    }

    private void packageMessageHead(ByteBuf byteBuf, Integer node, Integer length) {
        // 帧数起始标志 4
        byteBuf.writeByte(0xD6);
        byteBuf.writeByte(0x3C);
        byteBuf.writeByte(0xDA);
        byteBuf.writeByte(0x80);

        // 信源 1
        byteBuf.writeByte(0x01);

        // 信宿 1
        byteBuf.writeByte(0x02);

        // 保留字段 1
        byteBuf.writeByte(0);

        // 信息类型 1
        byteBuf.writeByte(0xB1);

        // 数据长度 4
        byteBuf.writeIntLE(length);

        // 业务类型(4bit)、源节点地址(4bit) 1
        byteBuf.writeByte(buildSingleByte(5, 0));

        // 目的节点地址(4bit)、下一跳地址(4bit) 1
        byteBuf.writeByte(buildSingleByte(node, 1));

        // 业务优先级(1byte) 1
        byteBuf.writeByte(0);

        // 时间戳(5byte) TODO 待完善
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);
        byteBuf.writeByte(1);

        // 数据帧头CRC校验(1byte)
        byteBuf.writeByte(1);
    }

    private int buildSingleByte(int high, int low) {
        return (high << 4) | low;
    }

    private void packageDataTransferParams(ByteBuf byteBuf, TerminalDataTransferDTO terminalDataTransferDTO) {
        // 命令码 4
        byteBuf.writeIntLE(0x40);

        // 保留 40
        byteBuf.writeBytes(new byte[40]);

        // 体制选择 4
        byteBuf.writeIntLE(terminalDataTransferDTO.getModeCtrl());

        // 下行发射控制 4
        byteBuf.writeIntLE(terminalDataTransferDTO.getTransModState());

    }
    /**
     * 整机参数设置帧 60
     * @param byteBuf
     * @param terminalMachineDTO
     */
    private void packageMachineParams(ByteBuf byteBuf, TerminalMachineDTO terminalMachineDTO) {
        // 命令码 4
        byteBuf.writeIntLE(0x48);

        // 保留 40
        byteBuf.writeBytes(new byte[40]);

        // 体制选择 4
        byteBuf.writeIntLE(terminalMachineDTO.getModeCtrl());

        // 接收频率 4
        byteBuf.writeIntLE(terminalMachineDTO.getRxFreq() * 10);

        // 发射频率 4
        byteBuf.writeIntLE(terminalMachineDTO.getTxFreq() * 10);

        // 发射衰减 4
        byteBuf.writeIntLE(terminalMachineDTO.getTxAtte());

    }

    /**
     * 抗干扰控制帧 52
     * @param byteBuf
     * @param terminalAntiJammingDTO
     */
    private void packageAntiJammingParams(ByteBuf byteBuf, TerminalAntiJammingDTO terminalAntiJammingDTO) {
        // 命令码 4
        byteBuf.writeIntLE(0x52);

        // 保留 40
        byteBuf.writeBytes(new byte[40]);

        // 终端编号 1
        byteBuf.writeByte(terminalAntiJammingDTO.getTargetNode());

        // 监控控制命令使能 1
        byteBuf.writeByte(1);

        // 决策方式 1
        byteBuf.writeByte(terminalAntiJammingDTO.getDecisionMethod());

        // 抗干扰手段 1
        byteBuf.writeByte(terminalAntiJammingDTO.getCounterInterferenceMeasures());

        // 系统级决策结果使能 1
        byteBuf.writeByte(1);

        // 链路切换方式 1
        byteBuf.writeByte(terminalAntiJammingDTO.getSwitchLink());

        // 切换链路代号 1
        byteBuf.writeByte(terminalAntiJammingDTO.getSwitchLinkCode());

        // 备用 1
        byteBuf.writeByte(0);
    }

    @Override
    public Map<String, Object> getTerminalModeStatus(String terminalSeq) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询指定终端的所有体制状态缓存
            Set<String> keys = RedisUtil.KeyOps.keys(TERMINAL_MODE_STATUS + terminalSeq + ":*");

            if (CollUtil.isNotEmpty(keys)) {
                for (String key : keys) {
                    // 提取体制类型（去掉前缀和终端编号）
                    String modeType = key.replace(TERMINAL_MODE_STATUS + terminalSeq + ":", "");

                    // 获取该体制的状态信息
                    Map<Object, Object> modeStatus = RedisUtil.HashOps.hEntries(key);

                    if (CollUtil.isNotEmpty(modeStatus)) {
                        result.put(modeType, modeStatus);
                    }
                }
            }

            log.debug("查询终端{}的体制状态信息: {}", terminalSeq, result);

        } catch (Exception e) {
            log.error("查询终端{}体制状态信息失败", terminalSeq, e);
        }

        return result;
    }

}
