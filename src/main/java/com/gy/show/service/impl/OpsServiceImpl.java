package com.gy.show.service.impl;

import com.alibaba.fastjson.JSON;
import com.gy.show.common.ServiceException;
import com.gy.show.entity.dto.RequirementFileDTO;
import com.gy.show.entity.dto.WsMessageDTO;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.service.OpsService;
import com.gy.show.ws.GlobalServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class OpsServiceImpl implements OpsService {

    @Value("${file.path}")
    private String filePath;

    @Autowired
    private GlobalServer globalServer;

    private static final String TEMP_PATH = "/temp/";

    private Map<String, RequirementFileDTO> files = new HashMap<>();

    @Override
    public void uploadFile(MultipartFile file) {
        File directory = new File(filePath + TEMP_PATH);

        if (!directory.exists()) {
            directory.mkdirs();
        }

        String originalFilename = file.getOriginalFilename();
        String path = filePath + TEMP_PATH + originalFilename;

        try {
            file.transferTo(new File(path));
            log.info("需求文件上传成功");
        } catch (IOException e) {
            log.error("需求文件上传失败", e);
            throw new ServiceException("上传文件失败");
        }

        String id = UUID.randomUUID().toString();
        // 给前端推送消息
        RequirementFileDTO dto = new RequirementFileDTO();
        dto.setId(id);
        dto.setFileName(file.getOriginalFilename());
        dto.setFileSize(file.getSize());
        dto.setReceiveTime(LocalDateTime.now());
        dto.setFilePath(path);

        WsMessageDTO messageDTO = new WsMessageDTO();
        messageDTO.setData(dto);
        messageDTO.setType(WebSocketTypeEnum.FILE.getCode());

        globalServer.sendAll(JSON.toJSONString(messageDTO));

        // 保存文件信息，后面解析还需要使用
        files.put(id, dto);
        log.info("当前存量文件数量：{}", files.size());
    }

    @Override
    public Map<String, RequirementFileDTO> getRequirementFile() {
        log.info("---->{}", files);
        return files;
    }
}
