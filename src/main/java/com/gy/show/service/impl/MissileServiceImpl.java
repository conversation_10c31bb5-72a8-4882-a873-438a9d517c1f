package com.gy.show.service.impl;

import com.gy.show.entity.dto.external.MissileDTO;
import com.gy.show.entity.dto.external.MissileHeadDTO;
import com.gy.show.enums.ClientTypeEnum;
import com.gy.show.enums.WebSocketTypeEnum;
import com.gy.show.runnner.InterBootstrapRunner;
import com.gy.show.service.ExternalDataService;
import com.gy.show.service.MissileService;
import com.gy.show.socket.BinaryParser;
import com.gy.show.socket.message.FieldDefinition;
import com.gy.show.socket.message.fields.FixedLengthField;
import com.gy.show.socket.message.fields.IntegerField;
import com.gy.show.socket.message.fields.ShortField;
import com.gy.show.socket.message.fields.UnSignedByteField;
import com.gy.show.socket.server.UdpMulticastSender;
import com.gy.show.util.ByteUtil;
import com.gy.show.util.DateUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;

import static com.gy.show.constants.Constants.missileStatusMapping;
import static com.gy.show.util.ByteUtil.calculateChecksum;

@Slf4j
@Service
public class MissileServiceImpl extends StationServiceImpl implements MissileService {

    @Autowired
    private InterBootstrapRunner runner;

    @Autowired
    private ExternalDataService externalDataService;

    private LinkedList<String> fields;

    @PostConstruct
    public void init() {
        fields = new LinkedList<>();

        fields.add("remoteControlStatus"); // 远控/本控状态 1
        fields.add("selfCheckStatus"); // 当前自检状态 2
        fields.add("workModel");// 工作模式 3
        fields.add("selfCheckResult"); // 自检测试结果 4
        fields.add("carrierLock"); // 载波锁定指示 5
        fields.add("frameSyncLock"); // 帧同步锁定指示 6
        fields.add("bitSyncLock"); // 位同步锁定指示 7
        fields.add("channelStatus"); // 信道状态 8
        fields.add("fireControlLaunchStatus"); // 安控发射状态 9
        fields.add("outPutPower"); // 输出功率 10
        fields.add("k1SendCount"); // K1发送计数 11
        fields.add("k2SendCount"); // K2发送计数 12
        fields.add("k4SendCount"); // K4发送计数 13
        fields.add("k1RecvCount"); // K1接收计数 14
        fields.add("k2RecvCount"); // K2接收计数 15
        fields.add("k4RecvCount"); // K4接收计数 16
        fields.add("signalTelemetryOnlineStatus"); // 遥测 信号处理在线状态 17
        fields.add("signalControlOnlineStatus"); // 安控 信号处理在线状态 18
        fields.add("controlPowerAmplifierStatus"); // 安控功放状态 19
        fields.add("feedPowerStatus"); // 馈电状态 20
        fields.add("Reserve21");//21
        fields.add("Reserve22");//22
        fields.add("Reserve23");//23
        fields.add("Reserve24");//24
        fields.add("Reserve25");//25
        fields.add("Reserve26");//26
        fields.add("Reserve27");//27
        fields.add("ebn0");// EbN0  28
    }

    @Override
    public void parseMessage(ByteBuf byteBuf, String id) {
        // 解析头部
        Map<String, Object> headMap = parseHeadMessage(byteBuf);

        Short frameType = (Short) headMap.get("frameType");
        log.info("接收到导弹测控站帧类型：{}， 数据长度：{}", frameType, byteBuf.readableBytes());
        switch (frameType) {
            case 10:
                // 心跳帧
                log.info("接收到导弹心跳帧");
                break;
            case 8:
                // 不带时标状态数据
                log.info("接收到导弹不带时标状态数据");
                Map<String, Object> result = parseBodyMessage(byteBuf);

                // 映射站点
                Map<String, Object> stationInfo = mappingStation(id);

                // 数据处理
                Map<String, Object> newResult = dataHandler(result, stationInfo);

                // 缓存站点数据
                cacheStationData(Collections.singletonList(newResult), id);

                // 发送到前端
                sendMessage2Front(newResult, WebSocketTypeEnum.FULL_VIEW_MISSILE_STATION.getCode());

                break;
            case 12:
                log.info("接收到导弹遥测数据，数据长度：{}", byteBuf.readableBytes());

                byteBuf.skipBytes(16);

                // 发送站点实时交互日志
//                sendStationMessage2Front(byteBuf, LogTypeEnum.INFO.getMessage(), "接收到导弹测控站遥测数据", id);

                byte[] data = new byte[70];
                byteBuf.readBytes(data);

                // 发送业务数据
                sendBusinessData(data, id, null);
                break;
        }

    }

    private Map<String, Object> dataHandler(Map<String, Object> result, Map<String, Object> stationInfo) {
        result.put("stationInfo", stationInfo);

        Map<String, Object> newResult = new HashMap<>();
        for (Map.Entry<String, Object> entry : result.entrySet()) {
            Map<String, Object> value = (Map<String, Object>) entry.getValue();

            newResult.put(entry.getKey(), value.get("data"));

            if (entry.getKey().equalsIgnoreCase("fireControlLaunchStatus")) {
                newResult.put(entry.getKey(), newResult.get("fireControlLaunchStatus").equals("aa") ? "开发射" : "关发射");
            }

            if (entry.getKey().equalsIgnoreCase("signalTelemetryOnlineStatus")) {
                newResult.put(entry.getKey(), newResult.get("signalTelemetryOnlineStatus").equals("aa") ? "数传" : "遥测");
            }

            if (entry.getKey().equalsIgnoreCase("signalControlOnlineStatus")) {
                newResult.put(entry.getKey(), newResult.get("signalControlOnlineStatus").equals("aa") ? "在线" : "未在线");
            }

            if (entry.getKey().equalsIgnoreCase("controlPowerAmplifierStatus")) {
                newResult.put(entry.getKey(), newResult.get("controlPowerAmplifierStatus").equals("aa") ? "开功放" : "关功放");
            }

            if (entry.getKey().equalsIgnoreCase("feedPowerStatus")) {
                newResult.put(entry.getKey(), newResult.get("feedPowerStatus").equals("aa") ? "馈电" : "未馈电");
            }

            if (entry.getKey().equalsIgnoreCase("remoteControlStatus")) {
                newResult.put(entry.getKey(), newResult.get("remoteControlStatus").equals("aa") ? "本控" : "远控");
            }

            if (entry.getKey().equalsIgnoreCase("workModel")) {
                newResult.put(entry.getKey(), newResult.get("workModel").equals("aa") ? "遥测 + 安控" : "数传 + 安控");
            }

            if (entry.getKey().equalsIgnoreCase("selfCheckResult")) {
                newResult.put(entry.getKey(), missileStatusMapping.get(newResult.get("selfCheckResult").toString()));
            }

            if (entry.getKey().equalsIgnoreCase("selfCheckStatus")) {
                newResult.put(entry.getKey(), missileStatusMapping.get(newResult.get("selfCheckStatus").toString()));
            }
        }

        newResult.put("stationInfo", stationInfo);

        return newResult;
    }

    @Override
    public void sendHeartBeat() {
        ByteBuf byteBuf = Unpooled.buffer();

        MissileHeadDTO missileHeadDTO = new MissileHeadDTO();
        missileHeadDTO.setFrameType(10);// 10 表示心跳帧
        missileHeadDTO.setLength(0);
        missileHeadDTO.setSum(0);
        // 头
        packageHeadMessage(byteBuf, missileHeadDTO);

        doSendMessage(byteBuf);

        log.info("向导弹测控站发送心跳数据成功");
    }

    private void doSendMessage(ByteBuf byteBuf) {
        UdpMulticastSender sender = runner.getSenders().get(ClientTypeEnum.MISSILE.getMessage());

        // 发送消息
        sender.sendMessage(byteBuf);
    }

    @Override
    public void sendMessage(MissileDTO missileDTO) {
        ByteBuf byteBuf = Unpooled.buffer();

        MissileHeadDTO missileHeadDTO = new MissileHeadDTO();
        missileHeadDTO.setFrameType(missileDTO.getType());// 测试指令（不带参数）
        missileHeadDTO.setLength(36 + 16 * missileDTO.getWords().size()); // 这里填多少
        missileHeadDTO.setSum(checkSum(missileDTO));
        checkSum(missileDTO);

        // head
        packageHeadMessage(byteBuf, missileHeadDTO);

        for (MissileDTO.MessageWord word : missileDTO.getWords()) {
            // 数传安控测试模式
            packageSingleWord(byteBuf, word.getTableNo(), word.getEncode(), word.getData());
        }

        // 发送消息
        log.info("向导弹发送指令：{}", missileDTO);
        doSendMessage(byteBuf);
    }

    private int checkSum(MissileDTO missileDTO) {
        LocalDateTime now = LocalDateTime.now();
        int result = 0xEB + 0x90 + calculateChecksum((36 + 16 * missileDTO.getWords().size())) + 23 + calculateChecksum(now.getYear()) + calculateChecksum(now.getMonthValue())
                + calculateChecksum(now.getDayOfMonth()) + calculateChecksum(((int) DateUtil.getDaySeconds() / 10)) + (missileDTO.getType()) + calculateChecksum(missileDTO.getWords().size());

        List<MissileDTO.MessageWord> words = missileDTO.getWords();
        for (MissileDTO.MessageWord word : words) {
            result += calculateChecksum(word.getTableNo());
            result += calculateChecksum(word.getEncode());
            result += calculateChecksum(word.getData());
        }
        log.debug("校验和：{}", result);

        return result;
    }

    private int int2SumChar(Integer source) {
        byte[] bytes = ByteUtil.int2ByteLe(source);
        int sum = 0;
        for (byte byt : bytes) {
            sum += byt;
        }
        return sum;
    }

    private Map<String, Object> parseBodyMessage(ByteBuf byteBuf) {
        Map<String, Object> result = new HashMap<>();
        for (String field : fields) {
            Map<String, Object> parseResult = parseSingleWord(byteBuf);

            result.put(field, parseResult);
        }

        return result;
    }

    /**
     * 解析单个信息字
     * @param byteBuf
     */
    private Map<String, Object> parseSingleWord(ByteBuf byteBuf) {
        List<FieldDefinition> messageList = new LinkedList<>();
        // 表号 2
        ShortField tableNo = new ShortField("tableNo");
        messageList.add(tableNo);

        // 编码 2
        ShortField encode = new ShortField("encode");
        messageList.add(encode);

        // 数据 4
        IntegerField data = new IntegerField("data");
        messageList.add(data);

        // 超差标识 2
        ShortField ccFlag = new ShortField("ccFlag");
        messageList.add(ccFlag);

        // 数据类型 2
        ShortField dataType = new ShortField("dataType");
        messageList.add(dataType);

        // 备用 4
        IntegerField backup = new IntegerField("backup");
        messageList.add(backup);

        Map<String, Object> singleFieldMap = BinaryParser.parseFieldsLE(byteBuf, messageList);

        // 单位转换
        Integer dataInt = (Integer) singleFieldMap.get("data");
        singleFieldMap.put("data", Integer.toHexString(dataInt));

        Short ccFlagInt = (Short) singleFieldMap.get("ccFlag");
        singleFieldMap.put("ccFlag", Integer.toHexString(ccFlagInt));

        Short dataTypeInt = (Short) singleFieldMap.get("dataType");
        singleFieldMap.put("dataType", Integer.toHexString(dataTypeInt));
        log.info("信息字解析完成：{}", singleFieldMap);

        return singleFieldMap;
    }

    private Map<String, Object> parseHeadMessage(ByteBuf byteBuf) {
        List<FieldDefinition> headList = new LinkedList<>();
        // 解析头 EB 90
        UnSignedByteField bf1 = new UnSignedByteField("bf1");
        UnSignedByteField bf2 = new UnSignedByteField("bf2");
        headList.add(bf1);
        headList.add(bf2);

        // 长度 2
        ShortField length = new ShortField("length");
        headList.add(length);

        // 源地址 系统编码 1
        UnSignedByteField sourceSystemCode = new UnSignedByteField("sourceSystemCode");
        headList.add(sourceSystemCode);

        // 源地址 机器编码 1
        UnSignedByteField sourceMachineCode = new UnSignedByteField("sourceMachineCode");
        headList.add(sourceMachineCode);

        // 目的地址 系统编码 1
        UnSignedByteField targetSystemCode = new UnSignedByteField("targetSystemCode");
        headList.add(targetSystemCode);

        // 目的地址 机器编码 1
        UnSignedByteField targetMachineCode = new UnSignedByteField("targetMachineCode");
        headList.add(targetMachineCode);

        // 日期与时间 8 暂时不解析
        FixedLengthField datetime = new FixedLengthField("datetime", 8);
        headList.add(datetime);

        // 帧类型 2
        ShortField frameType = new ShortField("frameType");
        headList.add(frameType);

        // 重传次数 2
        ShortField sendTimes = new ShortField("sendTimes");
        headList.add(sendTimes);

        // 帧计数 4
        IntegerField frameCount = new IntegerField("frameCount");
        headList.add(frameCount);

        // 信息字个数 4
        IntegerField words = new IntegerField("words");
        headList.add(words);

        // 备用 4
        IntegerField backup = new IntegerField("backup");
        headList.add(backup);

        // 校验和 4
        IntegerField checkSum = new IntegerField("checkSum");
        headList.add(checkSum);

        Map<String, Object> headMap = BinaryParser.parseFieldsLE(byteBuf, headList);
        log.info("导弹站协议头部解析结果：{}", headMap);

        return headMap;
    }

    private void packageHeadMessage(ByteBuf byteBuf, MissileHeadDTO missileHeadDTO) {
        // 帧头 1 * 2
        byteBuf.writeByte(0xEB);

        byteBuf.writeByte(0x90);

        // 长度 2
        byteBuf.writeShortLE(missileHeadDTO.getLength());

        // 源地址 系统编码 1
        byteBuf.writeByte(0x02);

        // 源地址 机器编码 1
        byteBuf.writeByte(0x01);

        // 目的地址 系统编码 1
        byteBuf.writeByte(0x02);

        // 目的地址 机器编码 1
        byteBuf.writeByte(0x12);

        // 日期与时间 8
        generalDate(byteBuf);

        // 帧类型 2
        byteBuf.writeShortLE(missileHeadDTO.getFrameType());

        // 重传次数 2
        byteBuf.writeShortLE(0);

        // 帧计数 4
        byteBuf.writeIntLE(0);

        // 信息字个数 4
        byteBuf.writeIntLE(1);

        // 备用 4
        byteBuf.writeIntLE(0);

        // 校验和 4
        byteBuf.writeIntLE(missileHeadDTO.getSum());

    }

    private void generalDate(ByteBuf byteBuf) {
        LocalDateTime now = LocalDateTime.now();

        // 年 2
        byteBuf.writeShortLE(now.getYear());

        // 月 1
        byteBuf.writeByte(now.getMonth().getValue());

        // 日 1
        byteBuf.writeByte(now.getDayOfMonth());

        // 时间 4 当日的积秒
        byteBuf.writeIntLE((int) DateUtil.getDaySeconds() / 10);
    }

    private void packageSingleWord(ByteBuf byteBuf, Integer tableNo, Integer encode, Integer data) {
        // 表号 2
        byteBuf.writeShortLE(tableNo);

        // 编码 2
        byteBuf.writeShortLE(encode);

        // 数据 4
        byteBuf.writeIntLE(data);

        // 超差标志 2
        byteBuf.writeShortLE(0);

        // 数据类型 2
        byteBuf.writeShortLE(0);

        // 备用 4
        byteBuf.writeIntLE(0);
    }
}
