package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.SysDictionary;
import com.gy.show.mapper.SysDictionaryMapper;
import com.gy.show.service.SysDictionaryService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SysDictionaryServiceImpl extends ServiceImpl<SysDictionaryMapper, SysDictionary> implements SysDictionaryService {

    @Override
    public List<SysDictionary> getDicByType(String type) {
        return list(Wrappers.<SysDictionary>lambdaQuery().eq(SysDictionary::getDictType, type));
    }

    @Override
    public void convertDictionary(Map<String, Object> map) {
        List<SysDictionary> dictionaries = list();

        map.forEach((k, v) -> {
            List<SysDictionary> types = getDictionary(k, dictionaries);
            if (CollUtil.isNotEmpty(types)) {
                Map<Integer, String> dictMap = types.stream()
                        .collect(Collectors.toMap(
                                SysDictionary::getDictValue,
                                SysDictionary::getDictName));

                map.put(k, dictMap.get(v.toString()));
            }
        });
    }

    private List<SysDictionary> getDictionary(String dictType, List<SysDictionary> dictionaries) {
         Map<String, List<SysDictionary>> groupByType = dictionaries.stream()
                .collect(Collectors.groupingBy(SysDictionary::getDictType));

        return groupByType.get(dictType);
    }

}
