package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.DataPresetTrack;
import com.gy.show.entity.dto.DataPresetTrackDTO;
import com.gy.show.mapper.DataPresetTrackMapper;
import com.gy.show.service.DataPresetTrackService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DataPresetTrackServiceImpl extends ServiceImpl<DataPresetTrackMapper, DataPresetTrack> implements DataPresetTrackService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createPresetTrack(DataPresetTrackDTO dataPresetTrackDTO) {
        // 删除已存在的航迹
        remove(Wrappers.<DataPresetTrack>lambdaQuery().eq(DataPresetTrack::getPresetId, dataPresetTrackDTO.getPresetId()));

        List<DataPresetTrack> tracks = dataPresetTrackDTO.getTracks();

        tracks.forEach(point -> point.setPresetId(dataPresetTrackDTO.getPresetId()));

        // 保存新航迹
        saveOrUpdateBatch(tracks);
    }

    @Override
    public DataPresetTrackDTO pagePresetTracks(String presetId) {
        List<DataPresetTrack> tracks = list(Wrappers.<DataPresetTrack>lambdaQuery().eq(DataPresetTrack::getPresetId, presetId));

        DataPresetTrackDTO trackDTO = new DataPresetTrackDTO();
        trackDTO.setTracks(tracks);
        trackDTO.setPresetId(presetId);

        return trackDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTrackById(String presetId) {
        remove(Wrappers.<DataPresetTrack>lambdaQuery().eq(DataPresetTrack::getPresetId, presetId));
    }
}
