package com.gy.show.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gy.show.entity.dos.SysConfig;
import com.gy.show.mapper.SysConfigMapper;
import com.gy.show.service.SysConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

    @Override
    public SysConfig getConfigByName(String name) {
        List<SysConfig> sysConfigs = list(Wrappers.<SysConfig>lambdaQuery().eq(SysConfig::getName, name));
        return CollUtil.isNotEmpty(sysConfigs) ? sysConfigs.get(0) : null;
    }
}
