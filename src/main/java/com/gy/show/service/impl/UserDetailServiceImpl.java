package com.gy.show.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.security.JwtUser;
import com.github.security.service.JwtUserDetailsService;
import com.gy.show.entity.dos.SysUser;
import com.gy.show.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

@Slf4j
@Service
public class UserDetailServiceImpl extends JwtUserDetailsService {

    @Autowired
    private SysUserService sysUserService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.info("当前登录用户：{}", username);
        SysUser user = sysUserService.getOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getUsername, username));

        JwtUser jwtUser = new JwtUser(username, user.getPassword(), new ArrayList<>());
        jwtUser.setUserId(user.getId());
        return jwtUser;
    }
}
