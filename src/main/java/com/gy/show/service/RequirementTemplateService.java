package com.gy.show.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dto.RequirementTemplateDTO;

public interface RequirementTemplateService extends IService<RequirementTemplate> {

    RequirementTemplate addRequirementTemplate(RequirementTemplateDTO requirementTemplateDTO);

    void updateRequirementTemplate(RequirementTemplateDTO requirementTemplate);

    IPage<RequirementTemplateDTO> getAllRequirementTemplate(Integer pageNum, Integer pageSize, String keyword);

    RequirementTemplateDTO getRequirementTemplateById(String id);

    void deleteRequirementTemplate(String id);
}
