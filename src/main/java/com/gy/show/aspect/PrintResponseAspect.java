package com.gy.show.aspect;

import com.gy.show.common.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.time.LocalDateTime;

@Aspect
@Slf4j
@Component
public class PrintResponseAspect {

    @Around("com.gy.show.aspect.SystemArchitecture.inPrintResponseLayer()")
    public Object process(ProceedingJoinPoint pjp) throws Throwable {
        LocalDateTime start = LocalDateTime.now();
        StringBuilder print = new StringBuilder();
        //获取request
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(sra, "bad request");
        HttpServletRequest request = sra.getRequest();
        //url
        print.append("当前请求url:[")
                .append(request.getRequestURI())
                .append("]");
        //请求参数
        StringBuilder requestParams = new StringBuilder();
        Object[] args = pjp.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                requestParams.append("<")
                        .append(arg)
                        .append(">");
            }
        }
        //
        print.append(",请求参数：")
                .append(requestParams);
        Object proceed = null;
        Exception ex = null;

        try {
            proceed = pjp.proceed();
        } catch (Exception e) {
            ex = e;
        } finally {
            //设置响应码
            HttpServletResponse response = sra.getResponse();
            int status = response.getStatus();
            print.append(",响应码:[").append(status).append("]");

            //处理时长
            LocalDateTime end = LocalDateTime.now();
            Duration duration = Duration.between(start, end);
            print.append(",处理时长:[").append(duration.toMillis()).append("ms],");

            if (ex != null) {
                //打印异常
                print.append("异常类:[")
                        .append(ex.getClass().getSimpleName())
                        .append("]")
                        .append("异常描述:[")
                        .append(ex.getMessage())
                        .append("]");
                //打印堆栈
                StackTraceElement[] stackTrace = ex.getStackTrace();
                if (stackTrace != null && stackTrace.length > 0) {
                    print.append("异常堆栈:[").append(stackTrace[0]).append("]");
                }

                if (ex instanceof ServiceException) {
                    log.warn(print.toString());
                } else {
                    log.error(print.toString());
                }

            } else {
                //正常打印日子
                print.append(",响应body[").append(print).append("]");
                log.info(print.toString());
            }
            if (ex != null)
                throw ex;

            return proceed;
        }

    }
}
