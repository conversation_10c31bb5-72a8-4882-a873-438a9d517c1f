package com.gy.show.aspect;



import com.gy.show.enums.OperationTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SysLog {

    /**
     *  分类
     */
    String clazz() default "";

    /**
     * 日志操作类型
     */
    OperationTypeEnum operationType() default OperationTypeEnum.OTHER;
}
