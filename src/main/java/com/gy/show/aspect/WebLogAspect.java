//package com.gy.show.Aspect;
//
//import com.alibaba.fastjson.JSONObject;
//import com.gy.show.enums.OperationTypeEnum;
//import com.gy.show.common.Result;
//import com.gy.show.entity.dos.rw.UserOperationLog;
//import com.gy.show.mapper.UserOperationLogMapper;
//import com.xiaoleilu.hutool.http.HttpStatus;
//import com.xiaoleilu.hutool.util.CollectionUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.annotation.AfterReturning;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Before;
//import org.aspectj.lang.annotation.Pointcut;
//import org.springframework.stereotype.Component;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.lang.reflect.Method;
//import java.util.Enumeration;
//
///**
// * 用户操作日志切片
// */
//@Slf4j
//@Aspect
//@Component
//public class WebLogAspect {
//
//    @Resource
//    UserOperationLogMapper userOperationLogMapper;
//
//    @Pointcut("@annotation(com.gy.show.Aspect.SysLog)")
//    public void webLog(){
//
//    }
//
//    @Before("webLog()")
//    public void doBefore(JoinPoint joinPoint){
//
//    }
//
//    /**
//     *  日志切面
//     */
//    @AfterReturning(value = "webLog()", returning = "rvt")
//    public void afterReturning(JoinPoint joinPoint, Result rvt) {
//        // 参数入库
//        userOperationLogMapper.insert(recodeLogData(joinPoint, rvt));
//    }
//
//    /**
//     *  操作记录保存
//     * @param joinPoint
//     * @param rvt
//     * @return
//     */
//    private UserOperationLog recodeLogData(JoinPoint joinPoint,Result rvt) {
//        // 操作日志组装
//        UserOperationLog OperationLog = new UserOperationLog();
//        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//        Method method = currentMethod(joinPoint,joinPoint.getSignature().getName());
//        SysLog sysLog = method.getAnnotation(SysLog.class);
//        String authorization = request.getHeader("Authorization");
//
//        // 参数封装
//        Enumeration<String> names = request.getParameterNames();
//        JSONObject jsonObject = new JSONObject();
//        while (names.hasMoreElements()){
//            String name = names.nextElement();
//            jsonObject.put(name,request.getParameter(name));
//        }
//        OperationLog.setOperator(StringUtils.isNotBlank(authorization) ? authorization.split(",")[0] : request.getParameter("userName"));//操作人
//        OperationLog.setParam(CollectionUtil.isEmpty(jsonObject)?JSONObject.toJSONString(joinPoint.getArgs()[0]):JSONObject.toJSONString(jsonObject));  // 请求参数
//        OperationLog.setClazz(sysLog.clazz());//分类
//        OperationLog.setType(OperationTypeEnum.getMsg(sysLog.operationType()));//操作类型
//        OperationLog.setUrl(request.getRequestURI());// 请求地址
//        OperationLog.setCode(String.valueOf(rvt.getCode())); //返回值
//        OperationLog.setStatus(rvt.getCode() == HttpStatus.HTTP_OK ? "成功" : "失败");
//        OperationLog.setMsg(OperationLog.getClazz() + OperationLog.getType() + OperationLog.getStatus());
//        return OperationLog;
//    }
//
//    /**
//     *  获取当前方法
//     * @param joinPoint
//     * @param methodName
//     * @return
//     */
//    private Method currentMethod(JoinPoint joinPoint, String methodName) {
//        Method[] methods = joinPoint.getTarget().getClass().getMethods();
//        Method resultMethod = null;
//        for (Method method : methods) {
//            if (method.getName().equals(methodName)){
//                resultMethod = method;
//                break;
//            }
//        }
//        return resultMethod;
//    }
//
//}
