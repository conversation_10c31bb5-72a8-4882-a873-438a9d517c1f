package com.gy.show.constants;

import com.gy.show.entity.dto.external.TerminalConfigDTO;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 静态常量类
 */
@Component
public class Constants {

    // 重连的基础时间
    public static int BASE_SLEEP_TIMES_MS = 1000;

    // 重连的最大尝试次数
    public static final int MAX_RETRIES = 5;

    //  任务类型
    public static final Map<String, Object> taskType = new HashMap<>();

    //  任务类型转换
    public static final Map<String, Integer> taskTypeConvert = new HashMap<>();

    // 底盘各部分状态
    public static final Map<String, Integer> chassisMultiState = new HashMap<>();

    // 底盘状态
    public static final Map<String, Integer> chassisState = new HashMap<>();

    // 目标跟踪状态
    public static final Map<String, Integer> targetTrackState = new HashMap<>();

    // 任务周期
    public static final Map<String, Integer> repeatType = new HashMap<>();

    // 目标可支持的频段
    public static final Map<String, Integer> targetFrequency = new HashMap<>();

    // 工作体制
    public static final Map<String, Integer> workSystem = new HashMap<>();

    // 天线类型
    public static final Map<String, Integer> antennaType = new HashMap<>();

    // 目标所属域
    public static final Map<String, Integer> area = new HashMap<>();

    // 终端频谱数据类型
    public static final Map<String, Object> terminalDataType = new HashMap<>();

    // 终端配置参数
    public static final List<TerminalConfigDTO> terminalConfig = new ArrayList<>();

    // DD测控体制 -> 下行发射状态
    public static final Map<String, Object> missileTerminalStatus = new HashMap<>();

    // DD状态映射
    public static final Map<String, Object> missileStatusMapping = new HashMap<>();

    // 无人艇态势数据 --> 链路连接状态
    public static final Map<String, Integer> shipLineStatus = new HashMap<>();

    // 无人艇态势数据 --> 控制权
    public static final Map<String, Integer> controlStatus = new HashMap<>();

    // 无人艇态势数据 --> 控制模式
    public static final Map<String, Integer> controlMode = new HashMap<>();

    // 无人车态势数据 --> 车辆工作模式
    public static final Map<String, Integer> carWorkMode = new HashMap<>();

    // 无人机测控站数据 --> 发射状态
    public static final Map<String, Integer> fireStatus = new HashMap<>();

    // 测控站ID集合
    public static final List<String> stationList = Arrays.asList("_ka", "_s", "_missile", "_uav");

    static {
        // 无人机测控站数据 --> 发射状态
        controlStatus.put("遥控手动", 1);
        controlStatus.put("船长手动", 2);
        controlStatus.put("自主控制", 3);

        // 无人机测控站数据 --> 控制模式
        controlMode.put("遥控手动", 1);
        controlMode.put("船长手动", 2);
        controlMode.put("暂停", 4);
        controlMode.put("自主航行", 50);

        // 无人机测控站数据 --> 发射状态
        fireStatus.put("静默", 48);
        fireStatus.put("小功率", 49);
        fireStatus.put("大功率", 50);

        carWorkMode.put("前远光灯", 0b00000001);
        carWorkMode.put("前近光灯", 0b00000010);
        carWorkMode.put("双闪灯", 0b00000100);
        carWorkMode.put("喇叭", 0b00001000);
        carWorkMode.put("刹车灯", 0b00010000);

        // 无人艇态势数据 --> 链路连接状态
        shipLineStatus.put("电台链路连接情况", 0b00000001);
        shipLineStatus.put("测控链路连接情况", 0b00000010);

        // DD状态映射
        missileStatusMapping.put("0", "未自检");
        missileStatusMapping.put("55", "故障");
        missileStatusMapping.put("aa", "正常");
        missileStatusMapping.put("ff", "自检中");
        missileStatusMapping.put("11", "数传自闭环");
        missileStatusMapping.put("10", "数传测试");
        missileStatusMapping.put("21", "遥测自闭环");
        missileStatusMapping.put("20", "遥测测试");
        missileStatusMapping.put("31", "安控自闭环");
        missileStatusMapping.put("30", "安控测试");
        // DD测控体制 -> 下行发射状态
        missileTerminalStatus.put("b3d2", "遥测任务");
        missileTerminalStatus.put("c72f", "数传任务");
        missileTerminalStatus.put("4c2d", "遥测测试");
        missileTerminalStatus.put("3968", "数传测试");
        missileTerminalStatus.put("235a", "单音测试");

        // 终端配置参数
        // 导弹
        TerminalConfigDTO terminalConfigDTO3 = new TerminalConfigDTO();
        terminalConfigDTO3.setNodeId("1838766928349425666");
        terminalConfigDTO3.setMode(10);
        terminalConfigDTO3.setRxFreq(1770);
        terminalConfigDTO3.setTxFreq(2250);
        terminalConfigDTO3.setTxAtte(0);

        // 无人机
        TerminalConfigDTO terminalConfigDTO2 = new TerminalConfigDTO();
        terminalConfigDTO2.setNodeId("1834121686784696321");
        terminalConfigDTO2.setMode(9);
        terminalConfigDTO2.setRxFreq(1800);
        terminalConfigDTO2.setTxFreq(2300);
        terminalConfigDTO2.setTxAtte(0);

        // Ka
        TerminalConfigDTO terminalConfigDTO1 = new TerminalConfigDTO();
        terminalConfigDTO1.setNodeId("1834055782248001538");
        terminalConfigDTO1.setMode(1);
        terminalConfigDTO1.setRxFreq(30000);
        terminalConfigDTO1.setTxFreq(21000);
        terminalConfigDTO1.setTxAtte(0);

        // S
        TerminalConfigDTO terminalConfigDTO0 = new TerminalConfigDTO();
        terminalConfigDTO0.setNodeId("1834120494834802689");
        terminalConfigDTO0.setMode(5);
        terminalConfigDTO0.setRxFreq(2070);
        terminalConfigDTO0.setTxFreq(2260);
        terminalConfigDTO0.setTxAtte(0);

        terminalConfig.add(terminalConfigDTO0);
        terminalConfig.add(terminalConfigDTO1);
        terminalConfig.add(terminalConfigDTO2);
        terminalConfig.add(terminalConfigDTO3);

        // 终端频谱数据类型
        terminalDataType.put("1", "L频段");
        terminalDataType.put("2", "S频段");
        terminalDataType.put("3", "Ka频段");

        // 任务类型
        area.put("陆军", 0);
        area.put("空军", 1);
        area.put("海军", 2);

        // 任务类型
        taskType.put("遥控", 0b00000001);
        taskType.put("遥测", 0b00000010);
        taskType.put("测量", 0b00000100);
        taskType.put("数传", 0b00001000);

        // 任务类型转换
        taskTypeConvert.put("1", 0b00000001);
        taskTypeConvert.put("2", 0b00000010);
        taskTypeConvert.put("3", 0b00000100);
        taskTypeConvert.put("4", 0b00001000);

        // 底盘各部分状态
        chassisMultiState.put("CAN总线通讯", 0b00000001);
        chassisMultiState.put("能源系统", 0b00000010);
        chassisMultiState.put("发电机组（APU）", 0b00000100);
        chassisMultiState.put("制动系统",  0b00001000);
        chassisMultiState.put("驱动系统", 0b00010000);
        chassisMultiState.put("以太网网络通讯", 0b00100000);
        chassisMultiState.put("电气系统", 0b01000000);
        chassisMultiState.put("高压电池电量不足警示", 0b10000000);

        // 底盘状态
        chassisState.put("1级故障", 1);
        chassisState.put("2级故障", 2);
        chassisState.put("3级故障", 3);

        // 目标跟踪状态
        targetTrackState.put("未初始化", 0);
        targetTrackState.put("初始化失败", 1);
        targetTrackState.put("初始化成功", 2);
        targetTrackState.put("正在跟踪", 3);
        targetTrackState.put("目标丢失", 4);

        // 任务周期
        repeatType.put("仅一次", 0);
        repeatType.put("每天重复", 1);
        repeatType.put("每月重复", 2);

        // 可支持频段
        targetFrequency.put("L", 0b00000001);
        targetFrequency.put("S", 0b00000010);
        targetFrequency.put("C", 0b00000100);
        targetFrequency.put("X",  0b00001000);
        targetFrequency.put("Ku", 0b00010000);
        targetFrequency.put("K", 0b00100000);
        targetFrequency.put("Ka", 0b01000000);

        // 工作体制
        workSystem.put("扩频测控模式二体制", 0b00000001);
        workSystem.put("常规导弹测控体制", 0b00000010);
        workSystem.put("扩频四合一综合测控体制", 0b00000100);

        // 天线类型
        antennaType.put("平板天线", 0);
        antennaType.put("抛物面天线", 1);
        antennaType.put("相控阵天线", 2);
    }

}
