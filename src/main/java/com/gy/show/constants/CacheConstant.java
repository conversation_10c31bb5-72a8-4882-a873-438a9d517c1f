package com.gy.show.constants;

/**
 * @Description 缓存key
 */
public interface CacheConstant {

    //任务点迹缓存key
    String POINT_CACHE_PREFIX = "requirement:point:";

    // 轨迹文件缓存key
    String COORDINATE_FILE_PREFIX = "coordinate:file:";

    String TERMINAL_NODE_PREFIX = "terminal:node:";

    // 当前使用站点存入缓存,接入算法使用
    String TERMINAL_CURRENT_STATION = "terminal:current:station:";

    // 当前终端所使用的体制
    String TERMINAL_CURRENT_MODE = "terminal:current:mode:";

    // 实时上报的测控站缓存key
    String STATION_REAL_TIME = "station:real:time:";

    String STATION_INFO = "station:info:";

    // 实时上报的航天站缓存key
    String STATION_REAL_DATA = "station:real:data:";

    // 映射关系表缓存key
    String DATA_MAPPING = "data:mapping";

    String REAL_TARGET_INFO = "target:info:";

    // 终端状态缓存key
    String TERMINAL_ALL_STATUS = "terminal:all:status:";

    // 终端缓存key
    String TERMINAL_ALL_MESSAGE = "terminal:all:message:";

    // 单个终端状态缓存key
    String TERMINAL_SINGLE_STATUS = "terminal:single:status:";

    // 终端体制状态Hash key
    String TERMINAL_MODE_STATUS = "terminal:mode:status:";

    // 节点切换状态缓存key
    String NODE_SWITCH = "node:switch:";

    // 接入算法节点切换锁key
    String NODE_SELECT_LOCK = "node:select:lock:";

    // 操控端态势数据绑定对应目标详情缓存
    String CONTROL_TARGET_DETAIL = "control:target:detail:";

    // 节点接入算法开关
    String NODE_SELECT = "node:select";

    // 操控端发送需求消息
    String EXTERNAL_REQUIREMENT = "external:requirement:";

    // 无人机模拟导弹开关
    String SIMULA_MISSILE = "simula:missile";

    // 遥测数据转发配置
    String TELEMETRY_DISPATCHER = "telemetry:dispatcher:";

    // 操控端实时位置
    String CONTROL_REAL_POSITION = "control:real:position:";

}
