//package com.gy.show.handler;
//
//import org.orekit.bodies.GeodeticPoint;
//import org.orekit.bodies.OneAxisEllipsoid;
//import org.orekit.data.DataContext;
//import org.orekit.data.DataProvidersManager;
//import org.orekit.data.DirectoryCrawler;
//import org.orekit.data.ZipJarCrawler;
//import org.orekit.errors.OrekitException;
//import org.orekit.frames.Frame;
//import org.orekit.frames.FramesFactory;
//import org.orekit.propagation.analytical.tle.TLE;
//import org.orekit.propagation.analytical.tle.TLEPropagator;
//import org.orekit.time.AbsoluteDate;
//import org.orekit.time.TimeScale;
//import org.orekit.time.TimeScalesFactory;
//import org.orekit.utils.Constants;
//import org.orekit.utils.IERSConventions;
//import org.orekit.utils.PVCoordinates;
//
//import java.io.File;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.time.ZonedDateTime;
//import java.util.ArrayList;
//import java.util.List;
//
//public class SatelliteOrbitCoordinates {
//    public static void main(String[] args) {
//        try {
//            // 初始化Orekit库
//            File orekitData = new File("/Users/<USER>/Desktop/satellite/Orekit-12.1.1.zip");
//            DataProvidersManager manager = DataContext.getDefault().getDataProvidersManager();
//            manager.addProvider(new ZipJarCrawler(orekitData));
//
//            // 读取TLE文件
//            String line1 = "1 25544U 98067A   20356.12345678  .00001234  00000-0  10270-4 0  9992";
//            String line2 = "2 25544  51.6434  21.2345 0001234 123.4567 345.6789 15.50384039237388";
//            TLE tle = new TLE(line1, line2);
//
//            // 创建TLE传播器
//            TLEPropagator propagator = TLEPropagator.selectExtrapolator(tle);
//
//            // 获取当前时间
//            ZonedDateTime nowBeijing = ZonedDateTime.now(ZoneId.systemDefault());
//
////             将ZonedDateTime转换为LocalDateTime和ZoneOffset
//            LocalDateTime localDateTime = nowBeijing.toLocalDateTime();
//            int year = localDateTime.getYear();
//            int month = localDateTime.getMonthValue();
//            int day = localDateTime.getDayOfMonth();
//            int hour = localDateTime.getHour();
//            int minute = localDateTime.getMinute();
//            double second = localDateTime.getSecond() + localDateTime.getNano() / 1.0e9;
//
//            // 获取UTC时间尺度
//            TimeScale utc = TimeScalesFactory.getUTC();
////            AbsoluteDate currentDate = new AbsoluteDate(ZonedDateTime.now(ZoneId.systemDefault()).toInstant().toString(), utc);
//
//            // 创建AbsoluteDate对象
//            AbsoluteDate currentDate = new AbsoluteDate(year, month, day, hour, minute, second, utc);
//            AbsoluteDate endDate = currentDate.shiftedBy(86400); // 1天后
//
//            // 创建地球模型
//            Frame itrf = FramesFactory.getITRF(IERSConventions.IERS_2010, true);
//            OneAxisEllipsoid earth = new OneAxisEllipsoid(Constants.WGS84_EARTH_EQUATORIAL_RADIUS,
//                    Constants.WGS84_EARTH_FLATTENING, itrf);
//
//            // 存储轨道数据
//            List<double[]> trackPoints = new ArrayList<>();
//
//            // 进行预报，每分钟一次
//            AbsoluteDate stepDate = currentDate;
//            while (stepDate.compareTo(endDate) <= 0) {
//                PVCoordinates pvCoordinates = propagator.getPVCoordinates(stepDate, FramesFactory.getEME2000());
//                GeodeticPoint geoPoint = earth.transform(pvCoordinates.getPosition(), FramesFactory.getEME2000(), stepDate);
//
//                double longitude = Math.toDegrees(geoPoint.getLongitude());
//                double latitude = Math.toDegrees(geoPoint.getLatitude());
//                double altitude = geoPoint.getAltitude();
//
//                trackPoints.add(new double[]{longitude, latitude, altitude});
//
//                stepDate = stepDate.shiftedBy(60); // 每分钟
//            }
//
//            // 输出轨道数据
//            for (double[] point : trackPoints) {
//                System.out.println("经度: " + point[0] + ", 纬度: " + point[1] + ", 高度: " + point[2]);
//            }
//
//        } catch (OrekitException e) {
//            System.out.println("Orekit异常: " + e.getLocalizedMessage());
//        }
//    }
//}
//
