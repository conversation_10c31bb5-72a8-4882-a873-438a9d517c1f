//package com.gy.show.handler;
//
//import org.hipparchus.ode.ODEIntegrator;
//import org.orekit.bodies.GeodeticPoint;
//import org.orekit.bodies.OneAxisEllipsoid;
//import org.orekit.data.DataContext;
//import org.orekit.data.DataProvidersManager;
//import org.orekit.data.ZipJarCrawler;
//import org.orekit.forces.gravity.HolmesFeatherstoneAttractionModel;
//import org.orekit.forces.gravity.potential.GravityFieldFactory;
//import org.orekit.forces.gravity.potential.NormalizedSphericalHarmonicsProvider;
//import org.orekit.frames.Frame;
//import org.orekit.frames.FramesFactory;
//import org.orekit.orbits.OrbitType;
//import org.orekit.propagation.SpacecraftState;
//import org.orekit.propagation.analytical.tle.TLE;
//import org.orekit.propagation.analytical.tle.TLEPropagator;
//import org.orekit.propagation.conversion.DormandPrince853IntegratorBuilder;
//import org.orekit.propagation.numerical.NumericalPropagator;
//import org.orekit.time.AbsoluteDate;
//import org.orekit.time.TimeScale;
//import org.orekit.time.TimeScalesFactory;
//import org.orekit.utils.Constants;
//import org.orekit.utils.IERSConventions;
//import org.orekit.utils.TimeStampedPVCoordinates;
//
//import java.io.File;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.time.ZonedDateTime;
//
//public class SatelliteTrajectory {
//    public static void main(String[] args) {
//        try {
//            // 初始化Orekit库
//            File orekitData = new File("/Users/<USER>/Desktop/satellite/Orekit-12.1.1.zip");
//            DataProvidersManager manager = DataContext.getDefault().getDataProvidersManager();
//            manager.addProvider(new ZipJarCrawler(orekitData));
//
//            // 读取TLE文件
//            String line1 = "1 25544U 98067A   20356.12345678  .00001234  00000-0  10270-4 0  9992";
//            String line2 = "2 25544  51.6434  21.2345 0001234 123.4567 345.6789 15.50384039237388";
//            TLE tle = new TLE(line1, line2);
//
//            // 创建TLE传播器
//            TLEPropagator propagator = TLEPropagator.selectExtrapolator(tle);
//
//            // 获取当前时间
//            ZonedDateTime nowBeijing = ZonedDateTime.now(ZoneId.systemDefault());
//
////             将ZonedDateTime转换为LocalDateTime和ZoneOffset
//            LocalDateTime localDateTime = nowBeijing.toLocalDateTime();
//            int year = localDateTime.getYear();
//            int month = localDateTime.getMonthValue();
//            int day = localDateTime.getDayOfMonth();
//            int hour = localDateTime.getHour();
//            int minute = localDateTime.getMinute();
//            double second = localDateTime.getSecond() + localDateTime.getNano() / 1.0e9;
//
//            // 获取UTC时间尺度
//            TimeScale utc = TimeScalesFactory.getUTC();
////            AbsoluteDate currentDate = new AbsoluteDate(ZonedDateTime.now(ZoneId.systemDefault()).toInstant().toString(), utc);
//
//            // 创建AbsoluteDate对象
//            AbsoluteDate currentDate = new AbsoluteDate(year, month, day, hour, minute, second, utc);
//
//            AbsoluteDate endDate = currentDate.shiftedBy(3600); // 1小时后
//
//            // 创建数值预报器
//            Frame itrf = FramesFactory.getITRF(IERSConventions.IERS_2010, true);
//            OneAxisEllipsoid earth = new OneAxisEllipsoid(Constants.WGS84_EARTH_EQUATORIAL_RADIUS,
//                    Constants.WGS84_EARTH_FLATTENING, itrf);
//
//            double minStep = 0.001;
//            double maxStep = 300;
//            double positionTolerance = 10.0;
//            ODEIntegrator integrator = new DormandPrince853IntegratorBuilder(minStep, maxStep, positionTolerance)
//                    .buildIntegrator(propagator.getInitialState().getOrbit(), OrbitType.KEPLERIAN);
//            NumericalPropagator numericalPropagator = new NumericalPropagator(integrator);
//            numericalPropagator.setInitialState(new SpacecraftState(propagator.getInitialState().getOrbit(), 1000));
//
//            // 添加重力场模型
//            NormalizedSphericalHarmonicsProvider nshp = GravityFieldFactory.getNormalizedProvider(2, 0);
//            HolmesFeatherstoneAttractionModel gravityField = new HolmesFeatherstoneAttractionModel(earth.getBodyFrame(), nshp);
//            numericalPropagator.addForceModel(gravityField);
//
//            // 进行预报，每分钟一次
//            AbsoluteDate stepDate = currentDate;
//            while (stepDate.compareTo(endDate) <= 0) {
//                TimeStampedPVCoordinates pvCoordinates = numericalPropagator.getPVCoordinates(stepDate, FramesFactory.getEME2000());
//                GeodeticPoint geoPoint = earth.transform(pvCoordinates.getPosition(), FramesFactory.getEME2000(), stepDate);
//
//                System.out.println("时间（UTC）: " + stepDate);
//                System.out.println("经度: " + Math.toDegrees(geoPoint.getLongitude()));
//                System.out.println("纬度: " + Math.toDegrees(geoPoint.getLatitude()));
//                System.out.println("高度: " + geoPoint.getAltitude());
//
//                stepDate = stepDate.shiftedBy(1); // 每分钟
//                Thread.sleep(1000);
//            }
//        } catch (Exception e) {
//            System.out.println("Orekit异常: " + e.getLocalizedMessage());
//        }
//    }
//}
//
