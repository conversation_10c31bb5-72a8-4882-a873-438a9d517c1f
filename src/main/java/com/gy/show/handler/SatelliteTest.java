//package com.gy.show.handler;
//
//import org.orekit.bodies.GeodeticPoint;
//import org.orekit.bodies.OneAxisEllipsoid;
//import org.orekit.data.DataContext;
//import org.orekit.data.DataProvidersManager;
//import org.orekit.data.ZipJarCrawler;
//import org.orekit.frames.Frame;
//import org.orekit.frames.FramesFactory;
//import org.orekit.orbits.Orbit;
//import org.orekit.propagation.analytical.tle.TLE;
//import org.orekit.propagation.analytical.tle.TLEPropagator;
//import org.orekit.time.AbsoluteDate;
//import org.orekit.time.TimeScale;
//import org.orekit.time.TimeScalesFactory;
//import org.orekit.utils.Constants;
//import org.orekit.utils.IERSConventions;
//import org.orekit.utils.PVCoordinates;
//
//import java.io.BufferedReader;
//import java.io.File;
//import java.io.FileReader;
//import java.io.IOException;
//import java.time.Duration;
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.time.ZonedDateTime;
//import java.util.ArrayList;
//import java.util.List;
//
//public class SatelliteTest {
//
//    public static void main(String[] args) {
//        try {
//            LocalDateTime start = LocalDateTime.now();
//
//            // 初始化Orekit库
//            File orekitData = new File("/Users/<USER>/Desktop/satellite/Orekit-12.1.1.zip");
//            DataProvidersManager manager = DataContext.getDefault().getDataProvidersManager();
//
//            manager.addProvider(new ZipJarCrawler(orekitData));
//
//            // 读取TLE文件
//            List<String> tleLines = readTLEFile("/Users/<USER>/Desktop/satellite/GAOFEN.txt");
//            if (tleLines.size() < 2) {
//                System.out.println("TLE文件格式不正确");
//                return;
//            }
//
//            // 定义TLE数据
//            String line1 = tleLines.get(0);
//            String line2 = tleLines.get(1);
//            TLE tle = new TLE(line1, line2);
//
//            // 创建TLE传播器
//            TLEPropagator propagator = TLEPropagator.selectExtrapolator(tle);
//
//            // 获取当前时间
//            ZonedDateTime nowBeijing = ZonedDateTime.now(ZoneId.systemDefault());
//
//            // 将ZonedDateTime转换为LocalDateTime和ZoneOffset
//            LocalDateTime localDateTime = nowBeijing.toLocalDateTime();
//            int year = localDateTime.getYear();
//            int month = localDateTime.getMonthValue();
//            int day = localDateTime.getDayOfMonth();
//            int hour = localDateTime.getHour();
//            int minute = localDateTime.getMinute();
//            double second = localDateTime.getSecond() + localDateTime.getNano() / 1.0e9;
//
//            // 获取UTC时间尺度
//            TimeScale utc = TimeScalesFactory.getUTC();
//
//            // 创建AbsoluteDate对象
//            AbsoluteDate currentDate = new AbsoluteDate(year, month, day, hour, minute, second, utc);
//
//
//            // 计算实时位置
////            Orbit currentOrbit = propagator.propagate(currentDate).getOrbit();
////            PVCoordinates pvCoordinates = propagator.propagate(currentDate).getPVCoordinates();
////            System.out.println("当前时间: " + currentDate);
////            System.out.println("实时位置: " + currentOrbit.getPVCoordinates().getPosition());
////            System.out.println("实时速度: " + currentOrbit.getPVCoordinates().getVelocity());
//
//            // 计算实时位置
//            PVCoordinates pvCoordinates = propagator.propagate(currentDate).getPVCoordinates();
//
//            //定义地球，J2000坐标系下
//            final OneAxisEllipsoid earth = new OneAxisEllipsoid(Constants.WGS84_EARTH_EQUATORIAL_RADIUS,Constants.WGS84_EARTH_FLATTENING, FramesFactory.getITRF(IERSConventions.IERS_2010, true));
//
//
//            LocalDateTime end1 = LocalDateTime.now();
//            Duration between = Duration.between(start, end1);
//            System.out.println("time:" + between.getSeconds());
//
//            // 创建地球模型
//            Frame itrf = FramesFactory.getITRF(IERSConventions.IERS_2010, true);
////            OneAxisEllipsoid earth = new OneAxisEllipsoid(Constants.WGS84_EARTH_EQUATORIAL_RADIUS,
////                    Constants.WGS84_EARTH_FLATTENING, itrf);
//
//            // 将ECI坐标转换为地理坐标（经度、纬度、高度）
//            GeodeticPoint geoPoint = earth.transform(pvCoordinates.getPosition(), itrf, currentDate);
//
//            System.out.println("当前时间（UTC）: " + currentDate);
//            System.out.println("经度: " + Math.toDegrees(geoPoint.getLongitude()));
//            System.out.println("纬度: " + Math.toDegrees(geoPoint.getLatitude()));
//            System.out.println("高度: " + geoPoint.getAltitude());
//
//            LocalDateTime end2 = LocalDateTime.now();
//            Duration between2 = Duration.between(start, end2);
//            System.out.println("time:" + between2.getSeconds());
//
//            // 定义轨迹计算的时间段
//            AbsoluteDate startDate = currentDate.shiftedBy(-3600.0); // 1 hour before
//            AbsoluteDate endDate = currentDate.shiftedBy(3600.0); // 1 hour after
//            double step = 600.0; // 每10分钟计算一次位置
//
//            List<Orbit> orbits = new ArrayList<>();
//            for (double t = 0; t <= (endDate.durationFrom(startDate)); t += step) {
//                AbsoluteDate date = startDate.shiftedBy(t);
//                orbits.add(propagator.propagate(date).getOrbit());
//            }
//
//            // 输出关键轨道坐标
//            for (Orbit orbit : orbits) {
//                System.out.println("Date: " + orbit.getDate());
//                System.out.println("Position: " + orbit.getPVCoordinates().getPosition());
//                System.out.println("Velocity: " + orbit.getPVCoordinates().getVelocity());
//                System.out.println("-------");
//            }
//
//            LocalDateTime end3 = LocalDateTime.now();
//            Duration between3 = Duration.between(start, end3);
//            System.out.println("time:" + between3.getSeconds());
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private static List<String> readTLEFile(String filePath) {
//        List<String> tleLines = new ArrayList<>();
//        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
//            String line;
//            while ((line = br.readLine()) != null) {
//                if (!line.trim().isEmpty() && (line.startsWith("1 ") || line.startsWith("2 "))) {
//                    tleLines.add(line.trim());
//                }
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return tleLines;
//    }
//
//}
