package com.gy.show.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
@ApiModel(value="DragParamVo对象", description="视图层拖拽参数对象")
public class DragParamVO {

    @ApiModelProperty(value = "当前时间点(前端传频率值)")
    private Integer timePoint;

    @ApiModelProperty(value = "需求id")
    private String requirementId;

    @ApiModelProperty(value = "业务唯一id")
    private String bzId;

    @ApiModelProperty(value = "任务id集合")
    private List<String> taskIds;

    @ApiModelProperty(value = "需要过滤的任务id")
    private String filterTaskId;

    @ApiModelProperty(value = "用于计算任务进度的任务")
    private List<String> progressTaskIds;

    @ApiModelProperty(value = "是否拖拽")
    private Boolean drag;

    @ApiModelProperty(value = "倍数")
    private Integer step;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    @ApiModelProperty(value = "是否是模拟实时推送")
    private Boolean isSimulate;

    @ApiModelProperty(value = "实时推送目标ID")
    private String targetId;

    @ApiModelProperty(value = "实时推送资源ID")
    private String equipmentId;

}
