package com.gy.show.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "DragReturnVo对象", description = "视图层拖拽返回对象")
public class DragReturnVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务目标id")
    private String mbId;

    @ApiModelProperty(value = "装备编码")
    private String zbbm;

    @ApiModelProperty(value = "任务目标实例化id")
    private Integer slhId;

    @ApiModelProperty(value = "信号或设备实例化id")
    private List<String> childSlhIds;

    @ApiModelProperty(value = "目标类型")
    private Integer type;

    @ApiModelProperty(value = "开始时间点")
    private Integer startDate;

    @ApiModelProperty(value = "返回插值点")
    private List<PointVO> points = new ArrayList<>();

    @ApiModelProperty("是否只有实例化点")
    private Boolean isOnlyOne;


    public List<PointVO> getDragAllPoints(Integer start, Integer end) {
        List<PointVO> result = new ArrayList<>();
        start -= startDate;
        end -= startDate - 1;
        int size = points.size();
        start = start < 0 ? 0 : start;
        return getPointVOS(size, result, start, end);
    }

    public List<PointVO> getPlayPoints(Integer range, Integer timePoint) {
        int size = points.size();
        List<PointVO> result = new ArrayList<>();
        Integer start = timePoint < 0 ? 0 : timePoint;
        start = start >= size ? size - 1 : start;
        if (start <= 0) {
            start = 0;
        }
        Integer end = timePoint + range + 1;
        return getPointVOS(size, result, start, end);
    }

    private List<PointVO> getPointVOS(int size, List<PointVO> result, Integer start, Integer end) {
        end = end > size ? size : end;
        if (end <= 0) {
            end = 1;
        }
        if (!CollectionUtils.isEmpty(points.subList(start, end))) result.addAll(points.subList(start, end));
        return result;
    }


}
