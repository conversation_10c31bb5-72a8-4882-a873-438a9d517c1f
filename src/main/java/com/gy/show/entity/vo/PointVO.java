package com.gy.show.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "pointVo", description = "点参数封装")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PointVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "经度")
    private double x;

    @ApiModelProperty(value = "纬度")
    private double y;

    private double z;

    @ApiModelProperty(value = "时间点")
    private Integer timePoint;

    @ApiModelProperty(value = "方向")
    private double angle;

    @ApiModelProperty(value = "速度(km/h)")
    private double speed;

    @ApiModelProperty(value = "是否为关键点")
    private Boolean primary;

    @ApiModelProperty(value = "是否变色")
    private Boolean changeColor;

    public PointVO(double x, double y, double z, Integer timePoint, double angle, double speed, Boolean primary) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.timePoint = timePoint;
        this.angle = angle;
        this.primary = primary;
        this.speed = speed;
    }
}
