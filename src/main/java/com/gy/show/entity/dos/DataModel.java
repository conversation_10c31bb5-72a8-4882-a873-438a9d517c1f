package com.gy.show.entity.dos;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 平台资源模型表
 */
@Data
public class DataModel implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 模型名称
     */
    private String mxmc;

    /**
     * 模型类型
     */
    private Byte mxlx;

    /**
     * 存储路径
     */
    private String cclj;

    /**
     * 创建时间
     */
    private Date cjsj;

    /**
     * 修改时间
     */
    private Date xgsj;

    private static final long serialVersionUID = 1L;
}