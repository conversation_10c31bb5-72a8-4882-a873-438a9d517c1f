package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.SysUserDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 
 */
@Data
public class SysUser extends ObjectConvert<SysUserDTO> implements Serializable {

    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户名
     */
    private String username;

    /**
     * 哈希后密码
     */
    private String password;

    /**
     * 1 男 2 女 0 其他
     */
    private Integer gender;

    /**
     * 电子邮件地址
     */
    private String email;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 状态 0 启用 1 禁用
     */
    private Boolean status;

    /**
     * 创建时间
     */
    private LocalDateTime createAt;

    /**
     * 修改时间
     */
    private LocalDateTime updateAt;

    private static final long serialVersionUID = 1L;
}