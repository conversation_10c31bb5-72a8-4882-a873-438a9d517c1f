package com.gy.show.entity.dos;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.RequirementTemplateDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RequirementTemplate extends ObjectConvert<RequirementTemplateDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 需求模板名称
     */
    private String templateName;

    /**
     * 需求模板类型
     */
    private Byte requirementType;
    /**
     * 需求模板重要程度
     */
    private Integer importance;

    /**
     * 轨迹文件ID
     */
    private String fileId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime updateTime;

    /**
     * 需求模板描述
     */
    private String requirementComment;


    private static final long serialVersionUID = 1L;
}