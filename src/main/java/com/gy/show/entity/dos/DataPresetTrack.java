package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.DataPresetTrackDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 目标轨迹表
 */
@Data
public class DataPresetTrack extends ObjectConvert<DataPresetTrackDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 航迹名称
     */
    private String presetId;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 时间点
     */
    private Integer time;

    /**
     * 速度
     */
    private BigDecimal speed;

    /**
     * 方向
     */
    private BigDecimal direct;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}