package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.RequirementTaskDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 任务信息表
 */
@Data
public class RequirementTask extends ObjectConvert<RequirementTaskDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 需求ID
     */
    private String requirementId;

    /**
     * 关联目标ID
     */
    private String targetRelationId;

    /**
     * 重要程度 1 一般 2 重要
     */
    private Integer importance;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型 1 遥控 2 遥测 3 测量 4 数传
     */
    private Integer taskType;

    /**
     * 重复周期 0 仅一次 1 每天 1 每周 2 每月
     */
    private Integer repeatType;

    /**
     * 调度状态 0 待调度 1调度中 2已调度
     */
    private Integer status;

    /**
     * 调度状态 0 待调度 1调度中 2调度成功 3调度失败
     */
    private Integer scheduleStatus;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 任务描述
     */
    private String taskComment;

    private static final long serialVersionUID = 1L;
}