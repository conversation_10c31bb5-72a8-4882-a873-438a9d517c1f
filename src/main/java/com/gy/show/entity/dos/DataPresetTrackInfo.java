package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.DataPresetTrackInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 目标轨迹表
 */
@Data
public class DataPresetTrackInfo extends ObjectConvert<DataPresetTrackInfoDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 航迹名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String description;

    private Double viewHeight;

    /**
     * 速度
     */
    private BigDecimal speed;

    /**
     * 航迹宽度
     */
    private Integer trackWidth;

    /**
     * 航迹颜色
     */
    private String trackColor;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}