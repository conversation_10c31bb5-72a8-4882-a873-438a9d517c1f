package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.ScheduleLogDTO;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> 
 */
@Data
public class ScheduleLog extends ObjectConvert<ScheduleLogDTO> {
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 调度结果 0 调度失败 1 调度成功
     */
    private Integer status;

    /**
     * 调度类型 1 一级 2 二级
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 修改时间
     */
    private Date updateAt;

    /**
     * 日志记录信息
     */
    private String record;

}