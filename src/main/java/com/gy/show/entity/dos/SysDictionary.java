package com.gy.show.entity.dos;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 
 */
@Data
public class SysDictionary implements Serializable {
    private Integer id;

    private String dictType;

    private String dictTypeName;

    private String dictName;

    private Integer dictValue;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}