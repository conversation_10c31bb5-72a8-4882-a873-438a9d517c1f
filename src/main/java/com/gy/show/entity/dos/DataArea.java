package com.gy.show.entity.dos;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.DataAreaDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 资源域信息表
 */
@Data
public class DataArea extends ObjectConvert<DataAreaDTO> implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 资源域名称
     */
    private String areaName;

    /**
     * 资源域类型 1 资源  2 目标
     */
    private Byte areaType;

    private String areaPosition;

    private Integer source;

    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime updateTime;

    /**
     * 资源域描述
     */
    private String areaDescribe;

    private static final long serialVersionUID = 1L;
}