package com.gy.show.entity.dos;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.RequirementTemplateDTO;
import com.gy.show.entity.dto.TemplateBusinessRelationDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class TemplateBusinessRelation extends ObjectConvert<TemplateBusinessRelationDTO>  implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;
    /**
     *目标关系id
     */
    private String targetRelationId;
    /**
     *业务类型id
     */
    private Integer taskType;
    /**
     *二级分类id（确定是同一条任务数据）
     */
    private String secondGradeId;
    /**
     *业务开始时间(时分秒)
     */
    private String startTime;
    /**
     *业务结束时间(时分秒)
     */
    private String endTime;
    /**
     *执行周期
     */
    private Integer repeatType;
    /**
     *创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime createTime;
    /**
     *修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(insertStrategy = FieldStrategy.NOT_NULL)
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}