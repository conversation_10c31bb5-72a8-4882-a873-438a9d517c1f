package com.gy.show.entity.dos;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.DataGeneralDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 目标数据表
 */
@Data
public class DataGeneral extends ObjectConvert<DataGeneralDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表描述
     */
    private String tableComment;

    /**
     * 查询关键字
     */
    private String queryKeys;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}