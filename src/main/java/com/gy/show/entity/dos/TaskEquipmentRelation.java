package com.gy.show.entity.dos;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 设备任务关联表
 */
@Data
public class TaskEquipmentRelation implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 总表ID
     */
    private String generalId;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 侦察范围
     */
    private String visibilityRange;

    /**
     * 侦察类型 0 全方位 1 扇形 2 圆锥
     */
    private Byte visibilityType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 基础信息
     */
    private String basicInfo;

    private static final long serialVersionUID = 1L;
}