package com.gy.show.entity.dos;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 
 */
@Data
public class SysConfig implements Serializable {
    private Integer id;

    /**
     * 配置项名称
     */
    private String name;

    /**
     * 配置项类型
     */
    private String configType;

    /**
     * 配置项描述
     */
    private String description;

    /**
     * 额外信息
     */
    private String extra;

    /**
     * 配置值
     */
    private String value;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    private static final long serialVersionUID = 1L;
}