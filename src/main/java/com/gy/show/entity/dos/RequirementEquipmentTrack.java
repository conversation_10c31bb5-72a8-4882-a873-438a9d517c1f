package com.gy.show.entity.dos;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 设备轨迹表
 */
@Data
public class RequirementEquipmentTrack implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务设备关联ID
     */
    private String relationId;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 时间点
     */
    private Integer time;

    /**
     * 方向
     */
    private BigDecimal direct;

    /**
     * 卫星设备文件ID
     */
    private String fileId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}