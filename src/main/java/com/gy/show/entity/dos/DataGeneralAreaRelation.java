package com.gy.show.entity.dos;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <AUTHOR> 目标资源域关联表
 */
@Data
public class DataGeneralAreaRelation implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String id;

    /**
     * 基础数据ID
     */
    private String generalId;

    /**
     * 资源域ID
     */
    private String areaId;

    private static final long serialVersionUID = 1L;
}