package com.gy.show.entity.dos;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import lombok.Data;

/**
 * <AUTHOR> 任务目标关联表
 */
@Data
public class TaskTargetRelation extends ObjectConvert<TaskTargetRelationDTO> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 目标总表ID
     */
    private String generalId;

    /**
     * 目标ID
     */
    private String targetId;

    /**
     * 需求ID
     */
    private String requirementId;

    /**
     * 模型ID
     */
    private String modelId;

    /**
     * 目标名称
     */
    private String name;

    /**
     * 类型 1 目标 2 设备
     */
    private Integer type;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 航迹宽度
     */
    private Integer trackWidth;

    /**
     * 航迹颜色
     */
    private String trackColor;

    /**
     * 航迹速度
     */
    private BigDecimal trackSpeed;

    /**
     * 航迹开始时间
     */
    private LocalDateTime startTime;

    /**
     * 航迹结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 基础信息
     */
    private String basicInfo;

    private static final long serialVersionUID = 1L;
}