package com.gy.show.entity.dos;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dto.RequirementTargetTrackDTO;
import lombok.Data;

/**
 * <AUTHOR> 目标轨迹表
 */
@Data
public class RequirementTargetTrack extends ObjectConvert<RequirementTargetTrackDTO> implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 任务目标关联ID
     */
    private String relationId;

    /**
     * 航迹信息表ID
     */
    private String presetId;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 时间点
     */
    private Integer time;

    /**
     * 方向
     */
    private BigDecimal direct;

    /**
     * 速度
     */
    private BigDecimal speed;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}