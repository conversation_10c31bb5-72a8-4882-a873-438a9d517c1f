package com.gy.show.entity.context;

import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dos.RequirementTask;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.TaskTargetRelation;
import com.gy.show.entity.dto.TaskWithTargets;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 一次性查询所有数据的结果
 */
@Data
public class AllDataResult {

    /**
     * 任务列表
     */
    private List<RequirementTask> tasks;

    /**
     * 任务目标关系
     */
    private List<TaskTargetRelation> taskTargetRelations;

    /**
     * 目标航迹
     */
    private List<RequirementTargetTrack> tracks;

    /**
     * 设备占用数据
     */
    private List<DataEquipmentOccupancy> equipments;

    /**
     * 通用数据
     */
    private List<DataGeneral> dataGenerals;

    /**
     * 构建的任务目标数据
     */
    private List<TaskWithTargets> taskWithTargets;

    /**
     * 原始数据Map
     */
    private Map<String, Object> rawData;
}
