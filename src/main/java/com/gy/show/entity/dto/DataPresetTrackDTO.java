package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataPresetTrack;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 目标轨迹表
 */
@Data
public class DataPresetTrackDTO extends ObjectConvert<DataPresetTrack> implements Serializable {

    /**
     * 航迹名
     */
    private String presetId;

    private List<DataPresetTrack> tracks;

}