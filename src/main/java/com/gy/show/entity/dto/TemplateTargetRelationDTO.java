package com.gy.show.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dos.TemplateTargetRelation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TemplateTargetRelationDTO extends ObjectConvert<TemplateTargetRelation> implements Serializable {
    /**
     * 主键
     */
    private String id;
    /**
     * 需求模板id
     */
    private String rTemplateId;
    /**
     * 目标总表ID
     */
    private String generalId;
    /**
     * 目标ID
     */
    private String targetId;
    /**
     * 目标名称
     */
    private String targetName;
    /**
     * 航迹id
     */
    private String trackPresetId;
    /**
     * 航迹开始时间(时分秒)
     */
    private String trackStartTime;
    /**
     * 航迹结束时间(时分秒)
     */
    private String trackEndTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 业务关系实体集合
     */
    private List<TemplateBusinessRelationDTO> tasks;


    private static final long serialVersionUID = 1L;
}