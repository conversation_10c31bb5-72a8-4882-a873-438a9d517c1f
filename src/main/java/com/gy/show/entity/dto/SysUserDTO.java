package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.SysUser;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 
 */
@Data
public class SysUserDTO extends ObjectConvert<SysUser> implements Serializable {
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户名
     */
    private String username;

    private String password;

    /**
     * 1 男 2 女 0 其他
     */
    private Integer gender;

    /**
     * 电子邮件地址
     */
    private String email;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 状态 0 启用 1 禁用
     */
    private Integer status;

    private static final long serialVersionUID = 1L;
}