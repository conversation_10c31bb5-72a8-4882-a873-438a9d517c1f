package com.gy.show.entity.dto;

import com.gy.show.entity.dos.RequirementTask;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TaskWithTargets {
    private RequirementTask task;
    private List<TargetWithTracks> targets;

    private String targetId;

    private Integer relativeTime;

    public TaskWithTargets () {}

    public TaskWithTargets(RequirementTask task, List<TargetWithTracks> targets) {
        this.task = task;
        this.targets = targets;
    }

    public RequirementTask getTask() {
        return task;
    }

    public List<TargetWithTracks> getTargets() {
        return targets;
    }
}

