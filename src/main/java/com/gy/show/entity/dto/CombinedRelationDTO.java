package com.gy.show.entity.dto;

import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.TaskTargetRelation;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CombinedRelationDTO {

    private List<RequirementTargetTrack> tracks = new ArrayList<>();

    private List<TaskTargetRelation> taskTargetRelations = new ArrayList<>();

    public void addTaskTargetRelation(TaskTargetRelation taskTargetRelation) {
        taskTargetRelations.add(taskTargetRelation);
    }

    public void addTrack(RequirementTargetTrack track) {
        tracks.add(track);
    }
}
