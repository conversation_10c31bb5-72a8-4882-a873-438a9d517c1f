package com.gy.show.entity.dto;

import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.TaskTargetRelation;
import lombok.Data;

import java.util.List;

@Data
public class TargetWithTracks {
    private List<RequirementTargetTrack> tracks;
    private TaskTargetRelation target;

    private Integer type;

    public TargetWithTracks(TaskTargetRelation target, List<RequirementTargetTrack> tracks, Integer type) {
        this.target = target;
        this.tracks = tracks;
        this.type = type;
    }

}
