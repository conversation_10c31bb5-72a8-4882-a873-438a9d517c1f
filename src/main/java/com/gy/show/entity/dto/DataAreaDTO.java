package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataArea;
import com.gy.show.entity.dos.DataGeneral;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 资源域信息表
 */
@Data
public class DataAreaDTO extends ObjectConvert<DataArea> implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 资源域名称
     */
    private String areaName;

    /**
     * 资源域类型 1 资源  2 目标
     */
    private Byte areaType;

    /**
     * 资源域描述
     */
    private String areaDescribe;

    private String areaPosition;

    private Integer source;

    /**
     * 主表ID集合
     */
    private List<String> generalIds;
    /**
     *
     */
    @NotEmpty(message = "请至少选择一个类型")
    private List<DataGeneralDTO> dataGenerals;
    /**
     *
     */
    private boolean isShow;

    private boolean isConfirm;
}