package com.gy.show.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dos.TemplateBusinessRelation;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TemplateBusinessRelationDTO extends ObjectConvert<TemplateBusinessRelation> implements Serializable {
    /**
     * 主键
     */
    private String id;
    /**
     *目标关系id
     */
    private String targetRelationId;
    /**
     *业务类型id
     */
    private List<Integer> taskType;
    /**
     *二级分类id（确定是同一条任务数据）
     */
    private String secondGradeId;
    /**
     *业务开始时间(时分秒)
     */
    private String startTime;
    /**
     *业务结束时间(时分秒)
     */
    private String endTime;
    /**
     *执行周期
     */
    private Integer repeatType;
    /**
     *创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     *修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}