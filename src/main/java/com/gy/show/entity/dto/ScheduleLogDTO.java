package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.ScheduleLog;
import lombok.Data;

/**
 * <AUTHOR> 
 */
@Data
public class ScheduleLogDTO extends ObjectConvert<ScheduleLog> {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 设备ID
     */
    private String equipmentId;

    /**
     * 调度结果 0 调度失败 1 调度成功
     */
    private Integer status;

    /**
     * 调度类型 1 一级 2 二级
     */
    private Integer type;

    /**
     * 日志记录信息
     */
    private String record;

}