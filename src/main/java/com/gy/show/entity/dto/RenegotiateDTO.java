package com.gy.show.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RenegotiateDTO {

    @ApiModelProperty("客户端ID，用于WebSocket推送")
    private String clientId;

    @ApiModelProperty("分配失败的设备列表")
    private List<Map<String, Object>> failedEquipments;

    @ApiModelProperty("已成功分配的设备列表（marked=1，占用容量）")
    private List<Map<String, Object>> successEquipments;

    @ApiModelProperty("备选设备列表，按任务ID分组")
    private Map<String, List<Map<String, Object>>> backupEquipmentsGroup;

    @ApiModelProperty("需要重新分配的任务ID列表")
    private List<String> taskIds;

    @ApiModelProperty("调度类型：1-一级调度，2-二级调度")
    private Integer scheduleType = 2;
}
