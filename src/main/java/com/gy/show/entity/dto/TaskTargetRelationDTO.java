package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.TaskTargetRelation;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 任务目标关联表
 */
@Data
public class TaskTargetRelationDTO extends ObjectConvert<TaskTargetRelation> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 目标总表ID
     */
    private String generalId;

    /**
     * 目标ID
     */
    private String targetId;

    /**
     * 模型ID
     */
    private String modelId;

    private String targetType;

    /**
     * 目标类型
     */
    private Integer dataType;

    private String dataTypeValue;

    /**
     * 目标名称
     */
    private String name;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 航迹宽度
     */
    private Integer trackWidth;

    /**
     * 航迹颜色
     */
    private String trackColor;

    @Range(min = 600, max = 2000)
    private BigDecimal trackSpeed;

    /**
     * 基础信息
     */
    private String basicInfo;

    /**
     * 航迹开始时间
     */
    private LocalDateTime startTime;

    /**
     * 航迹结束时间
     */
    private LocalDateTime endTime;

    /**
     * 航迹信息
     */
    private List<RequirementTargetTrackDTO> targetTrack;

    private List<double[]> taskCoverPoint;

    private static final long serialVersionUID = 1L;
}