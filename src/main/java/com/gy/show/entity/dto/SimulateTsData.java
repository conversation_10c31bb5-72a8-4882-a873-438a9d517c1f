package com.gy.show.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimulateTsData {

    private String name;

    private List<Point> keyPoint;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public class Point {
        private double latitude;

        private double longitude;
    }
}
