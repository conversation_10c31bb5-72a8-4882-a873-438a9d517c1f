package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dos.RequirementTask;
import lombok.Data;
import org.apache.tomcat.jni.Local;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 任务信息表
 */
@Data
public class RequirementTaskDTO extends ObjectConvert<RequirementTask> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 需求ID
     */
    private String requirementId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 重要程度 1 一般 2 重要
     */
    private Integer importance;

    /**
     * 调度状态 0 待调度 1调度中 2已调度
     */
    private Integer status;

    /**
     * 任务类型 1 遥控 2 遥测 3 测量 4 数传
     */
    private List<Integer> taskType;

    /**
     * 重复周期 0 仅一次 1 每天 1 每周 2 每月
     */
    private Integer repeatType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务描述
     */
    private String taskComment;

    /**
     * 任务关联目标列表
     */
    private TaskTargetRelationDTO target;

    /**
     * 设备占用时间
     */
    private List<DataEquipmentOccupancy> occupancies;

    private Double process;

    /**
     * 当前时间，主要用于实时态势计算
     */
    private LocalDateTime currentTime;

    private List<Period> periods = new ArrayList<>();

    private static final long serialVersionUID = 1L;

    @Data
    public static class Period {

        private LocalDateTime startTime;

        private LocalDateTime endTime;

        private String equipmentName;

        private String equipmentId;

        private String targetId;
    }
}