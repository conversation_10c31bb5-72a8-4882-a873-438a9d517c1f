package com.gy.show.entity.dto;

import com.gy.show.entity.dos.RequirementTargetTrack;
import com.gy.show.entity.dos.RequirementTask;
import lombok.Data;
import org.locationtech.jts.geom.LineString;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Data
public class InvokeAlgorithmDTO {

    /**
     * 目标及航迹信息，key表示目标，value是具体航迹
     */
    private Map<String, List<RequirementTargetTrack>> tracks;

    /**
     * 空闲资源列表，key表示资源设备类型，value是具体的资源设备
     */
    private Map<String, List<Map<String, Object>>> equipments;

    /**
     * 过滤出的设备ID, key -> id, value -> trackCover
     */
    private Map<String, List<Map<String, Object>>> coverEquipment;

    /**
     * 优先级列表,主要存储为dataType
     */
    private LinkedList<Integer> priorityList;

    private RequirementTask task;

    private Map<String, LineString> lineStringMap;

}

