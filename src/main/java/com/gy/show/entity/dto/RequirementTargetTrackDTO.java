package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.RequirementTargetTrack;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> 目标轨迹表
 */
@Data
public class RequirementTargetTrackDTO extends ObjectConvert<RequirementTargetTrack> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 任务目标关联ID
     */
    private String relationId;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 维度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private BigDecimal altitude;

    /**
     * 时间点
     */
    private Integer time;

    /**
     * 速度
     */
    private BigDecimal speed;

    /**
     * 方向
     */
    private BigDecimal direct;


    private static final long serialVersionUID = 1L;
}