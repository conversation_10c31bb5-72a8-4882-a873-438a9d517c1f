package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataArea;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> 目标数据表
 */
@Data
public class DataGeneralDTO extends ObjectConvert<DataArea> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 模型ID
     */
    private String modelId;

    private Integer count;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表描述
     */
    private String tableComment;

    /**
     * 查询关键字
     */
    private String queryKeys;

    /**
     * 数据类型
     */
    private Integer dataType;

    private String dataTypeValue;
    /**
     *
     */
    private boolean isShow;
    /**
     *
     */
    private Map<String, Object> targetInfo;

    private static final long serialVersionUID = 1L;
}