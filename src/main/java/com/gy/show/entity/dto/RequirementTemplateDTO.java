package com.gy.show.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataFile;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dos.TemplateTargetRelation;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 需求模板表
 */
@Data
public class RequirementTemplateDTO extends ObjectConvert<RequirementTemplate> implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 需求模板名称
     */
    private String templateName;

    /**
     * 需求模板类型
     */
    private Byte requirementType;

    /**
     * 需求模板重要程度
     */
    private Integer importance;

    /**
     * 轨迹文件ID
     */
    private String fileId;

    private DataFile file;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 需求模板描述
     */
    private String requirementComment;

    /**
     * 模板需求实体
     */
    private List<TemplateTargetRelationDTO> targetInfos;

    private static final long serialVersionUID = 1L;
}