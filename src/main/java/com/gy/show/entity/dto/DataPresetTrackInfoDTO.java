package com.gy.show.entity.dto;

import com.gy.show.common.ObjectConvert;
import com.gy.show.entity.dos.DataPresetTrack;
import com.gy.show.entity.dos.DataPresetTrackInfo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 目标轨迹表
 */
@Data
public class DataPresetTrackInfoDTO extends ObjectConvert<DataPresetTrackInfo> implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 航迹名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 速度
     */
    private BigDecimal speed;

    private Double viewHeight;

    /**
     * 航迹宽度
     */
    private Integer trackWidth;

    /**
     * 航迹颜色
     */
    private String trackColor;

    /**
     * 描述
     */
    private String description;

    private List<DataPresetTrack> tracks;

    private static final long serialVersionUID = 1L;
}