package com.gy.show.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@Data
public class CalculateCoverDTO {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 主表ID
     */
    private String generalId;

    /**
     * 设备ID
     */
    private String equipmentId;

    @NotEmpty(message = "请先选择设备")
    private List<Map<String, Object>> equipments;



}

