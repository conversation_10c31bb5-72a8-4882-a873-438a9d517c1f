package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.DataPresetTrackDTO;
import com.gy.show.service.DataPresetTrackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "预设航迹管理")
@RestController
@RequestMapping("/preset/track")
public class DataPresetTrackController extends BaseController {

    @Autowired
    private DataPresetTrackService dataPresetTrackService;

    // 新增用户
    @ApiOperation("新增预设航迹")
    @PostMapping
    public Result createPresetTrack(@RequestBody DataPresetTrackDTO dataPresetTrackDTO) {
        dataPresetTrackService.createPresetTrack(dataPresetTrackDTO);
        return Result.ok();
    }

    // 分页查询
    @ApiOperation("查询预设航迹")
    @GetMapping
    public Result pagePresetTracks(@RequestParam("presetId") String presetId) {
        DataPresetTrackDTO result = dataPresetTrackService.pagePresetTracks(presetId);
        return Result.ok(result);
    }

    @ApiOperation("根据预设ID删除整条航迹")
    @DeleteMapping("/{presetId}")
    public Result deleteTrackById(@PathVariable("presetId") String presetId) {
        dataPresetTrackService.deleteTrackById(presetId);
        return Result.ok();
    }

}
