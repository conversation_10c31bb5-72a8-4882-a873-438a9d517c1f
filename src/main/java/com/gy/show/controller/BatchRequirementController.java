package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dto.BatchRequirementCreateDTO;
import com.gy.show.service.BatchRequirementService;
import com.gy.show.util.BatchRequirementConfigExample;
import com.gy.show.util.CommonMapperTestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 批量目标控制器
 */
@Slf4j
@Api(tags = "批量目标管理")
@RestController
@RequestMapping("/api/batch-requirement")
public class BatchRequirementController {

    @Autowired
    private BatchRequirementService batchRequirementService;

    @Autowired
    private BatchRequirementConfigExample configExample;

    @Autowired
    private CommonMapperTestUtil commonMapperTestUtil;

    @ApiOperation("批量创建目标（在一个需求中创建多个目标）")
    @PostMapping("/create-targets")
    public Result batchCreateTargets(@Valid @RequestBody BatchRequirementCreateDTO batchConfig) {
        try {
            log.info("接收到批量创建目标请求，在一个需求中创建{}个目标", batchConfig.getTargetCount());

            RequirementInfo requirement = batchRequirementService.batchCreateTargets(batchConfig);

            log.info("批量创建目标完成，在需求{}中成功创建{}个目标",
                requirement.getRequirementName(), batchConfig.getTargetCount());
            return Result.ok(requirement, "批量创建目标成功");

        } catch (Exception e) {
            log.error("批量创建目标失败", e);
            return Result.error("批量创建目标失败：" + e.getMessage());
        }
    }

    @ApiOperation("创建混合类型目标示例（30个无人机+20个无人艇+15个无人车+5个弹）")
    @PostMapping("/create-mixed-example")
    public Result createMixedTargetsExample() {
        try {
            BatchRequirementCreateDTO config = configExample.createMixedTargetsExample();
            RequirementInfo requirement = batchRequirementService.batchCreateTargets(config);

            return Result.ok(requirement, "混合类型目标创建成功");
        } catch (Exception e) {
            log.error("创建混合类型目标示例失败", e);
            return Result.error("创建混合类型目标示例失败：" + e.getMessage());
        }
    }

    @ApiOperation("创建无人机专项目标示例（100个无人机目标）")
    @PostMapping("/create-uav-example")
    public Result createUavOnlyExample() {
        try {
            BatchRequirementCreateDTO config = configExample.createUavOnlyExample();
            RequirementInfo requirement = batchRequirementService.batchCreateTargets(config);

            return Result.ok(requirement, "无人机专项目标创建成功");
        } catch (Exception e) {
            log.error("创建无人机专项目标示例失败", e);
            return Result.error("创建无人机专项目标示例失败：" + e.getMessage());
        }
    }

    @ApiOperation("创建大规模目标示例（1000个混合目标）")
    @PostMapping("/create-large-scale-example")
    public Result createLargeScaleExample() {
        try {
            BatchRequirementCreateDTO config = configExample.createLargeScaleExample();
            RequirementInfo requirement = batchRequirementService.batchCreateTargets(config);

            return Result.ok(requirement, "大规模目标创建成功");
        } catch (Exception e) {
            log.error("创建大规模目标示例失败", e);
            return Result.error("创建大规模目标示例失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取配置示例")
    @GetMapping("/config-examples")
    public Result getConfigExamples() {
        try {
            return Result.ok()
                .data("mixedExample", configExample.createMixedTargetsExample())
                .data("uavOnlyExample", configExample.createUavOnlyExample())
                .data("largeScaleExample", configExample.createLargeScaleExample());
        } catch (Exception e) {
            log.error("获取配置示例失败", e);
            return Result.error("获取配置示例失败：" + e.getMessage());
        }
    }

    @ApiOperation("测试模糊查询功能")
    @PostMapping("/test-fuzzy-query")
    public Result testFuzzyQuery(@RequestParam("tableName") String tableName,
                                @RequestParam("keyword") String keyword) {
        try {
            log.info("开始测试模糊查询功能，表名：{}，关键字：{}", tableName, keyword);

            commonMapperTestUtil.testGetDataWithKeyword(tableName, keyword);
            commonMapperTestUtil.testGetDataByFieldLike(tableName, "name", keyword);
            commonMapperTestUtil.compareQueryMethods(tableName, keyword);

            return Result.ok("模糊查询测试完成，请查看日志");
        } catch (Exception e) {
            log.error("测试模糊查询功能失败", e);
            return Result.error("测试模糊查询功能失败：" + e.getMessage());
        }
    }

    @ApiOperation("测试无人机目标查询")
    @PostMapping("/test-uav-query")
    public Result testUavQuery() {
        try {
            commonMapperTestUtil.testUavTargetQuery();
            return Result.ok("无人机目标查询测试完成，请查看日志");
        } catch (Exception e) {
            log.error("测试无人机目标查询失败", e);
            return Result.error("测试无人机目标查询失败：" + e.getMessage());
        }
    }

    @ApiOperation("测试所有目标类型查询")
    @PostMapping("/test-all-target-types")
    public Result testAllTargetTypes() {
        try {
            commonMapperTestUtil.testAllTargetTypes();
            return Result.ok("所有目标类型查询测试完成，请查看日志");
        } catch (Exception e) {
            log.error("测试所有目标类型查询失败", e);
            return Result.error("测试所有目标类型查询失败：" + e.getMessage());
        }
    }
}
