package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.service.TsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Ts相关接口相关接口
 */
@RestController
@RequestMapping("/ts")
@Api(tags = "Ts相关接口相关接口")
public class TSController extends BaseController {

    @Autowired
    private TsService tsService;

    @ApiOperation("模拟所有基础数据的实时ts（随机）")
    @GetMapping("/simulateTsData")
    public Result simulateTsData() {
        tsService.simulateTsData();
        return Result.ok();
    }

    @ApiOperation("根据需求模拟基础数据的实时态势数据")
    @GetMapping("/simulateByParam")
    public Result simulateByParam(@RequestParam("requirementId") String requirementId) {
        tsService.simulateTsData0(requirementId);
        return Result.ok();
    }

}
