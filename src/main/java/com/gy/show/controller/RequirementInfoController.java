package com.gy.show.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dto.RequirementFileDTO;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.external.ExternalRequirementDTO;
import com.gy.show.entity.dto.external.ReceiveExternalDTO;
import com.gy.show.enums.RequirementSourceEnum;
import com.gy.show.service.OpsService;
import com.gy.show.service.RequirementInfoService;
import com.gy.show.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.gy.show.constants.CacheConstant.EXTERNAL_REQUIREMENT;

@Slf4j
@RestController
@RequestMapping("/requirement")
@Api(tags = "需求信息管理")
public class RequirementInfoController {

    @Autowired
    private RequirementInfoService requirementInfoService;

    @Autowired
    private OpsService opsService;

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取需求信息")
    public Result getRequirementInfoById(@PathVariable String id) {
        RequirementInfoDTO result =  requirementInfoService.getRequirementInfoById(id);
        return Result.ok(result);
    }

    @PostMapping
    @ApiOperation("新增需求信息")
    public Result addRequirementInfo(@RequestBody RequirementInfoDTO requirementInfoDTO) {
        if (requirementInfoDTO.getSource() == null) {
            requirementInfoDTO.setSource(RequirementSourceEnum.MANUAL.getCode());
        }
        RequirementInfo result = requirementInfoService.addRequirementInfo(requirementInfoDTO);
        return Result.ok(result);
    }

    @PutMapping
    @ApiOperation("更新需求信息")
    public Result updateRequirementInfo(@RequestBody RequirementInfo requirementInfo) {
        requirementInfoService.updateById(requirementInfo);
        return Result.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除需求信息")
    public Result deleteRequirementInfo(@PathVariable String id) {
        requirementInfoService.deleteRequirementInfo(id);
        return Result.ok();
    }

    @GetMapping("/list")
    @ApiOperation("分页查询需求信息")
    public Result listRequirementInfos(@RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                       @RequestParam(required = false) String keyword) {
        IPage<RequirementInfo> page = requirementInfoService.getAllRequirementInfos(pageNum, pageSize, keyword);
        return Result.ok(page);
    }

    @GetMapping("/getTaskById")
    @ApiOperation("根据需求ID获取任务信息及相关联的目标信息")
    public Result getTaskById(@RequestParam("requirementId") String requirementId) {
        Object result = requirementInfoService.getTaskById(requirementId);
        return Result.ok(result);
    }

    @GetMapping("/getFileTemplate")
    @ApiOperation("生成excel文件模板")
    public ResponseEntity getFileTemplate(HttpServletResponse response) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=template.xlsx");
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

        ClassPathResource resource = new ClassPathResource("static/template.xlsx");

        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(resource);
    }

    @GetMapping("/parseExcel")
    @ApiOperation("解析excel文件")
    public Result parseExcel(@RequestParam("id") String id) {
        requirementInfoService.parseExcel(id);

        return Result.ok();
    }

    @GetMapping("/getRequirementFile")
    @ApiOperation("获取文件列表")
    public Result getRequirementFile() {
        Map<String, RequirementFileDTO> result = opsService.getRequirementFile();
        return Result.ok(result);
    }

    @GetMapping("/deleteRequirementFile")
    @ApiOperation("删除文件")
    public Result deleteRequirementFile(@RequestParam("id") String id) {
        requirementInfoService.deleteRequirementFile(id);
        return Result.ok();
    }

    @GetMapping("/getExternalRequirement")
    @ApiOperation("获取外部传入的需求信息")
    public Result getExternalRequirement() {
        Set<String> keys = RedisUtil.KeyOps.keys(EXTERNAL_REQUIREMENT + "*");

        List<Map<Object, Object>> result = new LinkedList<>();
        for (String key : keys) {
            Map<Object, Object> map = RedisUtil.HashOps.hGetAll(key);

            Object targetInfo = null;
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                if (entry.getKey().equals("targetInfos")) {
                    targetInfo = JSONArray.parseArray(entry.getValue().toString());
                }
            }
            map.put("targetInfos", targetInfo);
            result.add(map);
        }

        return Result.ok(result);
    }

    @ApiOperation("接收外部需求")
    @PostMapping("/receiveExternalRequirement")
    public Result receiveExternalRequirement(@RequestBody ReceiveExternalDTO receiveExternalDTO) {
        requirementInfoService.receiveExternalRequirement(receiveExternalDTO);
        return Result.ok();
    }

}
