package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.DataArea;
import com.gy.show.entity.dto.DataAreaDTO;
import com.gy.show.service.DataAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "资源域相关接口")
@RestController
@RequestMapping("/dataAreas")
public class DataAreaController {

    @Autowired
    private DataAreaService dataAreaService;

    /**
     * 根据ID获取资源域信息
     *
     * @param id 资源域ID
     * @return 资源域信息
     */
    @ApiOperation("根据ID获取资源域信息")
    @GetMapping("/{id}")
    public Result getDataAreaById(@PathVariable("id") String id) {
        DataArea area = dataAreaService.getDataAreaById(id);
        return Result.ok(area);
    }

    /**
     * 创建资源域信息
     *
     * @param dataArea 资源域信息
     * @return 创建后的资源域信息
     */
    @ApiOperation("创建资源域信息")
    @PostMapping
    public Result createDataArea(@RequestBody DataAreaDTO dataArea) {
        dataAreaService.createDataArea(dataArea);
        return Result.ok();
    }

    /**
     * 更新资源域信息
     *
     * @param id       资源域ID
     * @param dataArea 更新后的资源域信息
     * @return 更新后的资源域信息
     */
    @ApiOperation("更新资源域信息")
    @PutMapping
    public Result updateDataArea(@RequestBody DataAreaDTO dataArea) {
        dataAreaService.updateDataArea(dataArea);
        return Result.ok();
    }

    /**
     * 删除资源域信息
     *
     * @param id 资源域ID
     */
    @ApiOperation("删除资源域信息")
    @DeleteMapping("/{id}")
    public Result deleteDataArea(@PathVariable("id") String id) {
        dataAreaService.deleteDataArea(id);
        return Result.ok();
    }

    /**
     * 分页获取所有资源域信息
     *
     * @param page    当前页码，默认为1
     * @param size    每页记录数，默认为10
     * @param keyword 关键字，模糊查询资源域名称
     * @return 资源域信息分页列表
     */
    @ApiOperation("分页获取所有资源域信息")
    @GetMapping
    public Result getAllDataAreas(@RequestParam(name = "page", defaultValue = "1") int page,
                                  @RequestParam(name = "size", defaultValue = "10") int size,
                                  @RequestParam(name = "areaType", required = false) Integer areaType,
                                  @RequestParam(name = "keyword", required = false) String keyword,
                                  @RequestParam(name = "source", required = false) Integer source) {
        IPage<DataAreaDTO> result = dataAreaService.getAllDataAreas(page, size, areaType, keyword, source);
        return Result.ok(result);
    }

    @ApiOperation("通过目标反查资源域及目标信息")
    @GetMapping(value = "getAreaAndTargetInfo")
    public Result getAreaAndTargetInfo(@RequestParam(name = "generalId") String generalId,
                                  @RequestParam(name = "targetId") String targetId) {
        List<DataAreaDTO> areaAndTargetInfo = dataAreaService.getAreaAndTargetInfo(generalId, targetId);
        return Result.ok(areaAndTargetInfo );
    }
}
