package com.gy.show.controller;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/2/20 18:50
 * @description 基础控制器
 */
@Slf4j
public class BaseController {
    private final static String PAGE = "page";
    private final static String SIZE = "size";
    private final static String ORDER_BY_COLUMN = "orderByColumn";
    private final static String IS_DESC = "isDesc";

    /**
     * 分页查询
     *
     * @return 分页对象
     */
    protected <T> IPage<T> getPage() {
        HttpServletRequest request = getRequest();
        log.debug("当前页数: [{}], 每页数量: [{}], 排序字段: [{}], 是否降序: [{}]",
                request.getParameter(PAGE),
                request.getParameter(SIZE),
                request.getParameter(ORDER_BY_COLUMN),
                request.getParameter(IS_DESC));
        Integer page = Convert.toInt(request.getParameter(PAGE));
        Integer size = Convert.toInt(request.getParameter(SIZE));
        String orderByColumn = Convert.toStr(request.getParameter(ORDER_BY_COLUMN));
        Boolean isDesc = Convert.toBool(request.getParameter(IS_DESC));
        return page(page, size, orderByColumn, isDesc);
    }

    /**
     * 分页查询
     *
     * @param orderByColumn 排序字段
     * @param isDesc        是否倒序
     * @return 分页对象
     */
    protected <T> IPage<T> getPage(String orderByColumn, boolean isDesc) {
        HttpServletRequest request = getRequest();
        log.debug("当前页数: [{}], 每页数量: [{}], 排序字段: [{}], 是否降序: [{}]",
                request.getParameter(PAGE),
                request.getParameter(SIZE),
                orderByColumn,
                isDesc);
        Integer page = Convert.toInt(request.getParameter(PAGE));
        Integer size = Convert.toInt(request.getParameter(SIZE));
        return page(page, size, orderByColumn, isDesc);
    }

    protected <T> IPage<T> page(Integer page,
                              Integer size,
                              String orderByColumn,
                              Boolean isDesc) {
        Page<T> resultPage = new Page<>();
        if (Objects.nonNull(page)) {
            resultPage.setCurrent(page);
        }
        if (Objects.nonNull(size)) {
//            if (size > 200) size = 200;
            resultPage.setSize(size);
        }
        if (StringUtils.isNotBlank(orderByColumn)) {
            if (Optional.ofNullable(isDesc).orElse(false)) {
                return resultPage.addOrder(OrderItem.desc(orderByColumn));
            }
            return resultPage.addOrder(OrderItem.asc(orderByColumn));
        }
        return resultPage;
    }

    private HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder
                .getRequestAttributes()))
                .getRequest();
    }

}
