package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.entity.dos.DataModel;
import com.gy.show.service.DataModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/dataModels")
@Api(tags = "平台资源模型管理")
public class DataModelController {

    @Autowired
    private DataModelService dataModelService;

    @ApiOperation("根据ID获取平台资源模型信息")
    @GetMapping("/{id}")
    public DataModel getDataModelById(@PathVariable("id") String id) {
        return dataModelService.getDataModelById(id);
    }

    @ApiOperation("创建平台资源模型信息")
    @PostMapping
    public DataModel createDataModel(@RequestBody DataModel dataModel) {
        return dataModelService.createDataModel(dataModel);
    }

    @ApiOperation("更新平台资源模型信息")
    @PutMapping("/{id}")
    public DataModel updateDataModel(@PathVariable("id") String id, @RequestBody DataModel newDataModel) {
        return dataModelService.updateDataModel(id, newDataModel);
    }

    @ApiOperation("删除平台资源模型信息")
    @DeleteMapping("/{id}")
    public void deleteDataModel(@PathVariable("id") String id) {
        dataModelService.deleteDataModel(id);
    }

    @ApiOperation("分页获取所有平台资源模型信息")
    @GetMapping
    public IPage<DataModel> getAllDataModels(@RequestParam(name = "page", defaultValue = "1") int page,
                                             @RequestParam(name = "size", defaultValue = "10") int size,
                                             @RequestParam(name = "keyword", required = false) String keyword) {
        return dataModelService.getAllDataModels(page, size, keyword);
    }
}
