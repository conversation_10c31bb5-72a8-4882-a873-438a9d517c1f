package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.vo.DragParamVO;
import com.gy.show.service.SituationService;
import com.gy.show.util.DataSimulator;
import com.gy.show.util.MaritimeDataSimulator;
import com.gy.show.util.TargetDataSimulator;
import com.gy.show.util.VehicleDataSimulator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/situation")
@Api(tags = "态势推演相关接口")
public class SituationController extends BaseController {

    @Autowired
    private SituationService situationService;

    @Autowired
    private OpsController opsController;

    @ApiOperation("根据需求ID查询需求下所有目标以及设备数据")
    @GetMapping("/getData")
    public Result getData(@RequestParam("id") String id) {
        Map<String, Object> result = situationService.getData(id);
        return Result.ok(result);
    }

    @PostMapping("/dragOrPlay")
    @ApiOperation("预览--任务航迹拖拽或播放")
    public Result dragOrPlay(@RequestBody DragParamVO dragParamVO) {
        // 用于实时态势推演控制推演需求ID
        if (StringUtils.isBlank(dragParamVO.getRequirementId())) {
            dragParamVO.setRequirementId(opsController.getGlobalRequirementId());
        }
        Map<String, Object> data = situationService.handleDragOrPlay(dragParamVO);
        return Result.ok(data);
    }

    @GetMapping("/simulaTargetState")
    @ApiOperation("模拟目标状态")
    public Result simulaTargetState(@ApiParam("1 无人机 2 无人车 3 无人艇 4 蛋") @RequestParam("type") Integer type) {
        if (type == 1) {
            DataSimulator.TargetData targetData = DataSimulator.generateRandomTargetData();
            return Result.ok(targetData);
        }
        if (type == 2) {
            VehicleDataSimulator.VehicleData vehicleData = VehicleDataSimulator.generateRandomVehicleData();
            return Result.ok(vehicleData);
        }
        if (type == 3) {
            MaritimeDataSimulator.MaritimeData maritimeData = MaritimeDataSimulator.generateRandomMaritimeData();
            return Result.ok(maritimeData);
        }
        if (type == 4) {
            TargetDataSimulator.TargetDData targetDData = TargetDataSimulator.generateRandomTargetData();
            return Result.ok(targetDData);
        }
        return Result.ok();
    }

}
