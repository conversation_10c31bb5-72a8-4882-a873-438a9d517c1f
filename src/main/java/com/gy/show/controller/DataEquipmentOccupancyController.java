package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.DataEquipmentOccupancy;
import com.gy.show.entity.dto.DataEquipmentOccupancyDTO;
import com.gy.show.service.DataEquipmentOccupancyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Api(tags = "设备占用状态")
@RestController
@RequestMapping("/occupancy")
public class DataEquipmentOccupancyController {

    @Autowired
    private DataEquipmentOccupancyService dataEquipmentOccupancyService;

    /**
     * 获取当前资源域下设备占用情况
     *
     */
    @GetMapping("/getOccupancyByArea")
    public Result getOccupancyByArea(@RequestParam("areaId") String areaId) {
        Map<String, Object> result = dataEquipmentOccupancyService.getOccupancyByArea(areaId);
        return Result.ok(result);
    }

    /**
     * 根据ID获取目标数据信息
     *
     * @param id 目标数据ID
     * @return 目标数据信息
     */
    @GetMapping("/{id}")
    public Result getDataEquipmentOccupancyById(@PathVariable("id") String id) {
        return Result.ok(dataEquipmentOccupancyService.getDataEquipmentOccupancyById(id));
    }

    /**
     * 创建目标数据信息
     *
     * @param dataEquipmentOccupancy 目标数据信息
     * @return 创建后的目标数据信息
     */
    @PostMapping
    public Result createDataEquipmentOccupancy(@RequestBody DataEquipmentOccupancy dataEquipmentOccupancy) {
        return Result.ok(dataEquipmentOccupancyService.createDataEquipmentOccupancy(dataEquipmentOccupancy));
    }

    /**
     * 更新目标数据信息
     *
     * @param id                     目标数据ID
     * @param newDataEquipmentOccupancy 更新后的目标数据信息
     * @return 更新后的目标数据信息
     */
    @PutMapping("/{id}")
    public Result updateDataEquipmentOccupancy(@PathVariable("id") String id, @RequestBody DataEquipmentOccupancy newDataEquipmentOccupancy) {
        return Result.ok(dataEquipmentOccupancyService.updateDataEquipmentOccupancy(id, newDataEquipmentOccupancy));
    }

    /**
     * 删除目标数据信息
     *
     * @param id 目标数据ID
     */
    @DeleteMapping("/{id}")
    public Result deleteDataEquipmentOccupancy(@PathVariable("id") String id) {
        dataEquipmentOccupancyService.deleteDataEquipmentOccupancy(id);
        return Result.ok();
    }

    /**
     * 分页获取占用设备列表
     *
     * @param page    当前页码，默认为1
     * @param size    每页记录数，默认为10
     * @return 目标数据信息分页列表
     */
    @ApiOperation("分页获取占用设备列表")
    @GetMapping("getEquipmentByTask")
    public Result getEquipmentByTask(@RequestParam(name = "page", defaultValue = "1") int page,
                                     @RequestParam(name = "size", defaultValue = "10") int size,
                                     @RequestParam(name = "taskId") String taskId) {
        IPage<DataEquipmentOccupancyDTO> result = dataEquipmentOccupancyService.getEquipmentByTask(page, size, taskId);
        return Result.ok(result);
    }

    @GetMapping("/getDataByEquipment")
    @ApiModelProperty("根据设备查询任务和目标")
    public Result getDataByEquipment(@RequestParam("equipmentId") String equipmentId,
                                     @RequestParam("requirementId") String requirementId) {
        Object result = dataEquipmentOccupancyService.getDataByEquipment(equipmentId, requirementId);
        return Result.ok(result);
    }
}
