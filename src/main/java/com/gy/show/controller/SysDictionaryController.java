package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dos.SysDictionary;
import com.gy.show.service.StatsService;
import com.gy.show.service.SysDictionaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dict")
@Api(tags = "数据字典相关接口")
public class SysDictionaryController extends BaseController {

    @Autowired
    private SysDictionaryService sysDictionaryService;

    @ApiOperation("根据类型查询该类型下所有字典")
    @GetMapping("/getByType")
    public Result typeStats(@RequestParam("type") String type) {
        List<SysDictionary> dicByType = sysDictionaryService.getDicByType(type);
        return Result.ok(dicByType);
    }

    @ApiOperation("查询字典所有类型")
    @GetMapping("/listType")
    public Result listType() {
        List<SysDictionary> list = sysDictionaryService.list();
        return Result.ok(list.stream().map(SysDictionary::getDictType).distinct().collect(Collectors.toList()));
    }

    @ApiOperation("查询所有字典")
    @GetMapping("/allType")
    public Result allType() {
        List<SysDictionary> list = sysDictionaryService.list();
        return Result.ok(list.stream().distinct().collect(Collectors.toList()));
    }

}
