package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.service.CacheWarmupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 缓存预热控制器
 */
@Api(tags = "缓存预热管理")
@RestController
@RequestMapping("/cache")
@Slf4j
public class CacheWarmupController {

    @Autowired
    private CacheWarmupService cacheWarmupService;

    @PostMapping("/warmup")
    @ApiOperation("预热缓存 - 根据任务ID列表预先计算并缓存数据")
    public Result warmupCache(@ApiParam("任务ID列表") @RequestBody List<String> taskIds) {
        try {
            String warmupId = cacheWarmupService.startWarmup(taskIds);
            return Result.ok("缓存预热已启动");
        } catch (Exception e) {
            log.error("启动缓存预热失败", e);
            return Result.error("启动缓存预热失败: " + e.getMessage());
        }
    }

    @GetMapping("/warmup/status/{warmupId}")
    @ApiOperation("查询预热进度")
    public Result getWarmupStatus(@ApiParam("预热ID") @PathVariable String warmupId) {
        try {
            Map<String, Object> status = cacheWarmupService.getWarmupStatus(warmupId);
            return Result.ok(status);
        } catch (Exception e) {
            log.error("查询预热状态失败", e);
            return Result.error("查询预热状态失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/warmup/{warmupId}")
    @ApiOperation("停止预热")
    public Result stopWarmup(@ApiParam("预热ID") @PathVariable String warmupId) {
        try {
            cacheWarmupService.stopWarmup(warmupId);
            return Result.ok("预热已停止");
        } catch (Exception e) {
            log.error("停止预热失败", e);
            return Result.error("停止预热失败: " + e.getMessage());
        }
    }

    @GetMapping("/warmup/list")
    @ApiOperation("获取所有预热任务列表")
    public Result getWarmupList() {
        try {
            List<Map<String, Object>> warmupList = cacheWarmupService.getWarmupList();
            return Result.ok(warmupList);
        } catch (Exception e) {
            log.error("获取预热列表失败", e);
            return Result.error("获取预热列表失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/clear/{bzId}")
    @ApiOperation("清除指定业务的缓存")
    public Result clearCache(@ApiParam("业务ID") @PathVariable String bzId) {
        try {
            cacheWarmupService.clearCache(bzId);
            return Result.ok("缓存已清除");
        } catch (Exception e) {
            log.error("清除缓存失败", e);
            return Result.error("清除缓存失败: " + e.getMessage());
        }
    }
}
