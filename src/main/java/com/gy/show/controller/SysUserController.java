package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dto.SysUserDTO;
import com.gy.show.entity.dto.UserPasswordDTO;
import com.gy.show.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController {

    @Autowired
    private SysUserService sysUserService;

    // 新增用户
    @ApiOperation("新增用户")
    @PostMapping
    public Result createUser(@RequestBody SysUserDTO userDTO) {
        sysUserService.save(userDTO);
        return Result.ok();
    }

    // 分页查询
    @ApiOperation("分页查询用户")
    @GetMapping
    public Result getUsers(@RequestParam(value = "keyword", required = false) String keyword) {
        IPage<SysUserDTO> result = sysUserService.getUsers(getPage(), keyword);
        return Result.ok(result);
    }

    // 根据ID获取用户
    @ApiOperation("根据ID获取用户详情")
    @GetMapping("/{id}")
    public Result getUserById(@PathVariable("id") String id) {
        SysUserDTO result = sysUserService.getUserById(id);
        return Result.ok(result);
    }

    // 更新用户
    @ApiOperation("更新用户")
    @PutMapping
    public Result updateUser(@RequestBody SysUserDTO user) {
        sysUserService.updateUser(user);
        return Result.ok();
    }

    // 删除用户
    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    public Result deleteUser(@PathVariable("id") String id) {
        sysUserService.removeById(id);
        return Result.ok();
    }

    // 修改用户状态
    @ApiOperation("修改用户状态")
    @PutMapping("/changeStatus/{userId}")
    public Result changeUserStatus(@PathVariable("userId") String userId) {
        sysUserService.changeStatus(userId);
        return Result.ok();
    }

    // 修改用户密码
    @PostMapping("/changePassword")
    public Result changeUserPassword(@RequestBody UserPasswordDTO userPasswordDTO) {
        sysUserService.changeUserPassword(userPasswordDTO);
        return Result.ok();
    }
}
