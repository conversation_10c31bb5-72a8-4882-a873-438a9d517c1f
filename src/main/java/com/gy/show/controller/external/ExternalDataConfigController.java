package com.gy.show.controller.external;

import com.gy.show.common.Result;
import com.gy.show.enums.DataSourceModeEnum;
import com.gy.show.service.ExternalDataConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部数据配置管理控制器
 */
@Slf4j
@Api(tags = "外部数据配置管理")
@RestController
@RequestMapping("/external/config")
public class ExternalDataConfigController {

    @Autowired
    private ExternalDataConfigService externalDataConfigService;

    @ApiOperation("获取数据跟随状态")
    @GetMapping("/follow/status")
    public Result getFollowStatus() {
        try {
            boolean status = externalDataConfigService.getFollowStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", status);
            result.put("statusText", status ? "开启" : "关闭");
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取数据跟随状态失败", e);
            return Result.error("获取数据跟随状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("设置数据跟随状态")
    @PostMapping("/follow/status")
    public Result setFollowStatus(@ApiParam(value = "跟随状态，true表示开启，false表示关闭", required = true) 
                                  @RequestParam("status") Boolean status) {
        try {
            if (status == null) {
                return Result.error("状态参数不能为空");
            }
            
            externalDataConfigService.setFollowStatus(status);
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", status);
            result.put("statusText", status ? "开启" : "关闭");
            result.put("message", "数据跟随状态已设置为: " + (status ? "开启" : "关闭"));
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("设置数据跟随状态失败", e);
            return Result.error("设置数据跟随状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换数据跟随状态")
    @PostMapping("/follow/toggle")
    public Result toggleFollowStatus() {
        try {
            boolean newStatus = externalDataConfigService.toggleFollowStatus();
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", newStatus);
            result.put("statusText", newStatus ? "开启" : "关闭");
            result.put("message", "数据跟随状态已切换为: " + (newStatus ? "开启" : "关闭"));
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("切换数据跟随状态失败", e);
            return Result.error("切换数据跟随状态失败: " + e.getMessage());
        }
    }

    @ApiOperation("开启数据跟随")
    @PostMapping("/follow/enable")
    public Result enableFollow() {
        try {
            externalDataConfigService.setFollowStatus(true);
            
            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", true);
            result.put("statusText", "开启");
            result.put("message", "数据跟随已开启");
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("开启数据跟随失败", e);
            return Result.error("开启数据跟随失败: " + e.getMessage());
        }
    }

    @ApiOperation("关闭数据跟随")
    @PostMapping("/follow/disable")
    public Result disableFollow() {
        try {
            externalDataConfigService.setFollowStatus(false);

            Map<String, Object> result = new HashMap<>();
            result.put("followStatus", false);
            result.put("statusText", "关闭");
            result.put("message", "数据跟随已关闭");

            return Result.ok(result);
        } catch (Exception e) {
            log.error("关闭数据跟随失败", e);
            return Result.error("关闭数据跟随失败: " + e.getMessage());
        }
    }

    // ==================== 数据源模式管理接口 ====================

    @ApiOperation("获取当前数据源模式")
    @GetMapping("/datasource/mode")
    public Result getDataSourceMode() {
        try {
            DataSourceModeEnum mode = externalDataConfigService.getDataSourceMode();
            Map<String, Object> result = new HashMap<>();
            result.put("mode", mode.getCode());
            result.put("modeText", mode.getDescription());
            result.put("isRealData", mode.isRealData());
            result.put("isSimulation", mode.isSimulationData());
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取数据源模式失败", e);
            return Result.error("获取数据源模式失败: " + e.getMessage());
        }
    }

    @ApiOperation("设置数据源模式")
    @PostMapping("/datasource/mode")
    public Result setDataSourceMode(@ApiParam(value = "数据源模式，real表示真实数据，simulation表示模拟数据", required = true)
                                    @RequestParam("mode") String mode) {
        try {
            DataSourceModeEnum modeEnum = DataSourceModeEnum.fromCode(mode);
            externalDataConfigService.setDataSourceMode(modeEnum);

            Map<String, Object> result = new HashMap<>();
            result.put("mode", modeEnum.getCode());
            result.put("modeText", modeEnum.getDescription());
            result.put("isRealData", modeEnum.isRealData());
            result.put("isSimulation", modeEnum.isSimulationData());
            result.put("message", "数据源模式已设置为: " + modeEnum.getDescription());

            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            return Result.error("无效的数据源模式: " + mode + "，支持的模式: real, simulation");
        } catch (Exception e) {
            log.error("设置数据源模式失败", e);
            return Result.error("设置数据源模式失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换数据源模式")
    @PostMapping("/datasource/toggle")
    public Result toggleDataSourceMode() {
        try {
            DataSourceModeEnum newMode = externalDataConfigService.toggleDataSourceMode();

            Map<String, Object> result = new HashMap<>();
            result.put("mode", newMode.getCode());
            result.put("modeText", newMode.getDescription());
            result.put("isRealData", newMode.isRealData());
            result.put("isSimulation", newMode.isSimulationData());
            result.put("message", "数据源模式已切换为: " + newMode.getDescription());

            return Result.ok(result);
        } catch (Exception e) {
            log.error("切换数据源模式失败", e);
            return Result.error("切换数据源模式失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换到真实数据模式")
    @PostMapping("/datasource/real")
    public Result switchToRealData() {
        try {
            externalDataConfigService.setDataSourceMode(DataSourceModeEnum.REAL_DATA);

            Map<String, Object> result = new HashMap<>();
            result.put("mode", DataSourceModeEnum.REAL_DATA.getCode());
            result.put("modeText", DataSourceModeEnum.REAL_DATA.getDescription());
            result.put("isRealData", true);
            result.put("isSimulation", false);
            result.put("message", "已切换到真实数据模式");

            return Result.ok(result);
        } catch (Exception e) {
            log.error("切换到真实数据模式失败", e);
            return Result.error("切换到真实数据模式失败: " + e.getMessage());
        }
    }

    @ApiOperation("切换到模拟数据模式")
    @PostMapping("/datasource/simulation")
    public Result switchToSimulationData() {
        try {
            externalDataConfigService.setDataSourceMode(DataSourceModeEnum.SIMULATION_DATA);

            Map<String, Object> result = new HashMap<>();
            result.put("mode", DataSourceModeEnum.SIMULATION_DATA.getCode());
            result.put("modeText", DataSourceModeEnum.SIMULATION_DATA.getDescription());
            result.put("isRealData", false);
            result.put("isSimulation", true);
            result.put("message", "已切换到模拟数据模式");

            return Result.ok(result);
        } catch (Exception e) {
            log.error("切换到模拟数据模式失败", e);
            return Result.error("切换到模拟数据模式失败: " + e.getMessage());
        }
    }
}
