package com.gy.show.controller.external;

import cn.hutool.core.date.DateUtil;
import com.gy.show.enums.InteractionSourceEnum;
import com.gy.show.enums.LogTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InteractionLogDTO {

    /**
     * 日志记录时间
     */
    private String time;

    /**
     * 日志类型 INFO ERROR
     */
    private String type;

    /**
     * 日志来源类型
     */
    private String source;

    /**
     * 日志信息
     */
    private String message;

    public static InteractionLogDTO infoLog(String message, InteractionSourceEnum interactionSourceEnum) {
        return getInteractionLogDTO(message, interactionSourceEnum, LogTypeEnum.INFO);
    }

    public static InteractionLogDTO errorLog(String message, InteractionSourceEnum interactionSourceEnum) {
        return getInteractionLogDTO(message, interactionSourceEnum, LogTypeEnum.ERROR);
    }

    private static InteractionLogDTO getInteractionLogDTO(String message, InteractionSourceEnum interactionSourceEnum, LogTypeEnum error) {
        InteractionLogDTO interactionLogDTO = new InteractionLogDTO();
        interactionLogDTO.setTime(DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss.SSS"));
        interactionLogDTO.setType(error.getMessage());
        interactionLogDTO.setMessage(message);
        interactionLogDTO.setSource(interactionSourceEnum.getMessage());

        return interactionLogDTO;
    }

}
