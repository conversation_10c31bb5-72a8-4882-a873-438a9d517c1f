package com.gy.show.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = "用户权限相关接口")
@RequestMapping
@RestController
public class AuthController {

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public void login(@RequestBody Map<String, Object> user) {
    }

    @ApiOperation("用户登出")
    @PostMapping("logout")
    public void logout() {

    }
}
