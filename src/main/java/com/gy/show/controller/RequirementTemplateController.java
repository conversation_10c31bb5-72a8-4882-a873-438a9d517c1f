package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.RequirementInfo;
import com.gy.show.entity.dos.RequirementTemplate;
import com.gy.show.entity.dto.RequirementInfoDTO;
import com.gy.show.entity.dto.RequirementTemplateDTO;
import com.gy.show.service.RequirementInfoService;
import com.gy.show.service.RequirementTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/requirement/template")
@Api(tags = "需求模板管理")
public class RequirementTemplateController {

    @Autowired
    private RequirementTemplateService requirementTemplateService;

    @GetMapping("/{id}")
    @ApiOperation("根据ID获取需求模板")
    public Result getRequirementTemplateById(@PathVariable String id) {
        RequirementTemplateDTO result = requirementTemplateService.getRequirementTemplateById(id);
        return Result.ok(result);
    }

    @PostMapping
    @ApiOperation("新增需求模板")
    public Result addRequirementTemplate(@RequestBody RequirementTemplateDTO requirementTemplateDTO) {
        RequirementTemplate requirementTemplate = requirementTemplateService.addRequirementTemplate(requirementTemplateDTO);
        return Result.ok(requirementTemplate.getId());
    }

    @PutMapping
    @ApiOperation("更新需求模板")
    public Result updateRequirementTemplate(@RequestBody RequirementTemplateDTO requirementTemplate) {
        requirementTemplateService.updateRequirementTemplate(requirementTemplate);
        return Result.ok();
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除需求模板")
    public Result deleteRequirementTemplate(@PathVariable String id) {
        requirementTemplateService.deleteRequirementTemplate(id);
        return Result.ok();
    }

    @GetMapping
    @ApiOperation("分页查询需求模板信息")
    public Result listRequirementTemplate(@RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize,
                                       @RequestParam(required = false) String keyword) {
        IPage<RequirementTemplateDTO> page = requirementTemplateService.getAllRequirementTemplate(pageNum, pageSize, keyword);
        return Result.ok(page);
    }

}
