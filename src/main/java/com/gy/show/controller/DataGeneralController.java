package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.DataGeneral;
import com.gy.show.entity.dto.BasicDataDTO;
import com.gy.show.entity.dto.DataGeneralDTO;
import com.gy.show.service.DataGeneralService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/general")
@Api(tags = "基础数据管理")
public class DataGeneralController extends BaseController {

    @Autowired
    private DataGeneralService dataGeneralService;

    @ApiOperation("获取所有小类型")
    @GetMapping("/queryLittleType")
    public Result queryLittleType(@RequestParam("type") Integer type) {
        List<DataGeneral> result = dataGeneralService.queryLittleType(type);
        return Result.ok(result);
    }

    @ApiOperation("根据ID获取基础数据信息")
    @GetMapping("/{id}")
    public Result getDataGeneralById(@PathVariable("id") String id) {
        DataGeneral result = dataGeneralService.getDataGeneralById(id);
        return Result.ok(result);
    }

    @ApiOperation("创建目标数据信息")
    @PostMapping
    public Result createDataGeneral(@RequestBody DataGeneral dataGeneral) {
        dataGeneralService.createDataGeneral(dataGeneral);
        return Result.ok();
    }

    @ApiOperation("更新目标数据信息")
    @PutMapping("/{id}")
    public Result updateDataGeneral(@PathVariable("id") String id, @RequestBody DataGeneral newDataGeneral) {
        dataGeneralService.updateDataGeneral(id, newDataGeneral);
        return Result.ok();
    }

    @ApiOperation("删除目标数据信息")
    @DeleteMapping("/{id}")
    public Result deleteDataGeneral(@PathVariable("id") String id) {
        dataGeneralService.deleteDataGeneral(id);
        return Result.ok();
    }

    @ApiOperation("根据资源域分类查询基础数据分类树")
    @GetMapping("/tree")
    public Result getTreeByArea(@RequestParam(name = "areaId") String areaId) {
        List<DataGeneralDTO> result = dataGeneralService.getDataByArea(areaId);
        return Result.ok(result);
    }

    /**
     * 根据类型查询基础数据(动态查询)
     * @param dataTypeId
     * @param keyword
     * @return
     */
    @ApiOperation("根据类型查询基础数据(动态查询)")
    @GetMapping("/data")
    public Result getDataByType(@RequestParam(name = "dataTypeId") String dataTypeId,
                                @RequestParam(name = "areaId", required = false) String areaId,
                                @RequestParam(name = "keyword", required = false) String keyword) {
        IPage result = dataGeneralService.getDataByType(dataTypeId, areaId, keyword, getPage());
        return Result.ok(result);
    }

    /**
     * 根据ID查询基础数据详情
     * @param dataTypeId
     * @return
     */
    @ApiOperation("根据ID查询基础数据详情")
    @GetMapping("/dataDetail")
    public Result getDataDetail(@RequestParam(name = "dataTypeId") String dataTypeId,
                                @RequestParam(name = "dataId") String dataId) {
        Map<String, Object> result = dataGeneralService.getDataDetail(dataTypeId, dataId);
        return Result.ok(result);
    }

    /**
     * 根据类型查询基础字段(动态查询)
     * @param dataTypeId
     * @return
     */
    @ApiOperation("根据类型查询基础字段(动态查询)")
    @GetMapping("field")
    public Result getFieldByType(@RequestParam(name = "dataTypeId") String dataTypeId) {
        List<Map<String, Object>> result = dataGeneralService.getFieldByType(dataTypeId);
        return Result.ok(result);
    }

    @ApiOperation("新增资源、目标基础数据接口")
    @PostMapping("/saveData")
    public Result saveData(@RequestBody BasicDataDTO basicDataDTO) {
        dataGeneralService.saveData(basicDataDTO);
        return Result.ok();
    }

    @ApiOperation("修改资源、目标基础数据接口")
    @PutMapping("/updateData")
    public Result updateData(@RequestBody BasicDataDTO basicDataDTO) {
        dataGeneralService.updateData(basicDataDTO);
        return Result.ok();
    }

    @ApiOperation("删除资源、目标基础数据接口")
    @DeleteMapping("/deleteData")
    public Result deleteData(@RequestParam("dataTypeId") String dataTypeId,
                             @RequestParam("dataId") String dataId) {
        dataGeneralService.deleteData(dataTypeId, dataId);
        return Result.ok();
    }

    @ApiOperation("资源域关联类型选择接口")
    @GetMapping("/getType")
    public Result getType(@RequestParam("areaType") @ApiParam("资源域类型 1 资源 2 目标") Integer areaType) {
        Object result = dataGeneralService.getType(areaType);
        return Result.ok(result);
    }

    @ApiOperation("根据资源域查询所有空闲的资源（主要用于资源调度页面的资源查询）")
    @GetMapping("/queryIdleSource")
    public Result queryIdleSource(@RequestParam("areaId") String areaId) {
        Map<String, IPage<Map<String, Object>>> result = dataGeneralService.queryIdleSource(getPage(), areaId);
        return Result.ok(result);
    }

    @ApiOperation("查询所有资源类型")
    @GetMapping("/queryEquipmentType")
    public Result queryEquipmentType(@ApiParam("1 资源 2 目标") @RequestParam("type") Integer type) {
        List<DataGeneralDTO> result = dataGeneralService.queryEquipmentType(type);
        return Result.ok(result);
    }

    @ApiOperation("根据资源类型进行分页聚合查询")
    @GetMapping("/pageEquipmentByType")
    public Result pageEquipmentByType(@ApiParam(" 1 地基平台 2 天基平台 3 空基平台") @RequestParam(value = "type", required = false) Integer type,
                                      @ApiParam("1 资源 2 目标") @RequestParam("category") Integer category,
                                      @RequestParam(value = "areaId", required = false) String areaId) {
        Object result = dataGeneralService.pageEquipmentByType(getPage(), type, areaId, category);
        return Result.ok(result);
    }

    @ApiOperation("资源设备统计数据分页查询")
    @GetMapping("/pageStatsEquipment")
    public Result pageStatsEquipment(@RequestParam(value = "generaId", required = false) String generaId) {
        Object result = dataGeneralService.pageStatsEquipment(getPage(), generaId);
        return Result.ok(result);
    }

}
