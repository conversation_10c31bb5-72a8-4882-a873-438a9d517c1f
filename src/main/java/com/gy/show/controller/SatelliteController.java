package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.SatelliteDTO;
import com.gy.show.service.SatelliteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@Api(tags = "卫星轨道计算相关接口")
@RequestMapping("/satellite")
@RestController
public class SatelliteController {

    @Autowired
    private SatelliteService satelliteService;

    @ApiOperation("计算卫星轨道")
    @GetMapping("/calculateOrbit")
    public Result calculateOrbit(@RequestParam("generaId") String generaId,
                                 @RequestParam("equipmentId") String equipmentId,
                                 @RequestParam(value = "startTime", required = false) String startTime) {
        Object result = satelliteService.calculateOrbit(generaId, equipmentId, startTime);
        return Result.ok(result);
    }

    @ApiOperation("根据时间获取卫星当前位置")
    @PostMapping("/getSatelliteCoordinate")
    public Result getSatelliteCoordinate(@RequestBody List<SatelliteDTO> satelliteDTO) {
        Object result = satelliteService.getSatelliteCoordinate(satelliteDTO);
        return Result.ok(result);
    }

    @ApiOperation("开始推送卫星实时位置")
    @GetMapping("/startNumericalForecast")
    public Result startNumericalForecast(@RequestParam("generaId") String generaId,
                                         @RequestParam("equipmentId") String equipmentId) {
        satelliteService.startNumericalForecast(generaId, equipmentId);
        return Result.ok();
    }

    @ApiOperation("停止推送卫星实时位置")
    @GetMapping("/stopNumericalForecast")
    public Result stopNumericalForecast(@RequestParam("generaId") String generaId,
                                         @RequestParam("equipmentId") String equipmentId) {
        satelliteService.stopNumericalForecast(generaId, equipmentId);
        return Result.ok();
    }
}
