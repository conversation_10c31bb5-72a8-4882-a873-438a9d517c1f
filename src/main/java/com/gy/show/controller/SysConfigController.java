package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dos.SysConfig;
import com.gy.show.service.SysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "系统设置相关接口")
@RequestMapping("/config")
@RestController
public class SysConfigController {

    @Autowired
    private SysConfigService sysConfigService;

    @ApiOperation("查询系统配置")
    @GetMapping
    public Result list() {
        List<SysConfig> configs = sysConfigService.list();
        return Result.ok(configs);
    }

    @ApiOperation("保存系统配置")
    @PostMapping
    public Result storeConfig(@RequestBody SysConfig sysConfig) {
        sysConfigService.saveOrUpdate(sysConfig);
        return Result.ok();
    }

}
