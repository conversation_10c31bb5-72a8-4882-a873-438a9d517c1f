package com.gy.show.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gy.show.common.Result;
import com.gy.show.entity.dos.DataPresetTrackInfo;
import com.gy.show.entity.dto.DataPresetTrackInfoDTO;
import com.gy.show.service.DataPresetTrackInfoService;
import com.gy.show.service.RequirementInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "预设航迹信息管理")
@RestController
@RequestMapping("/preset")
public class DataPresetTrackInfoController extends BaseController {

    @Autowired
    private DataPresetTrackInfoService dataPresetTrackInfoService;

    @Autowired
    private RequirementInfoService requirementInfoService;

    @ApiOperation("分页查询航迹列表")
    @GetMapping
    public Result pageTrackInfo(@RequestParam(value = "keyword", required = false) String keyword) {
        IPage<DataPresetTrackInfoDTO> result = dataPresetTrackInfoService.pageTrackInfo(getPage(), keyword);
        return Result.ok(result);
    }

    @ApiOperation("分页查询航迹列表")
    @DeleteMapping("/{id}")
    public Result deleteTrackInfo(@PathVariable("id") String id) {
        dataPresetTrackInfoService.deleteTrackInfo(id);
        return Result.ok();
    }

    @ApiOperation("新增预设航迹")
    @PostMapping
    public Result createPresetTrackInfo(@RequestBody DataPresetTrackInfoDTO dataPresetTrackInfoDTO) {
        DataPresetTrackInfo presetTrackInfo = dataPresetTrackInfoService.createPresetTrackInfo(dataPresetTrackInfoDTO);
        return Result.ok(presetTrackInfo);
    }

    @ApiOperation("修改预设航迹")
    @PutMapping
    public Result updatePresetTrackInfo(@RequestBody DataPresetTrackInfoDTO dataPresetTrackInfoDTO) {
        DataPresetTrackInfo trackInfo = dataPresetTrackInfoService.updatePresetTrackInfo(dataPresetTrackInfoDTO);
        return Result.ok(trackInfo);
    }

    @ApiOperation("更新任务关联航迹")
    @PostMapping("/updateTaskTrack")
    public Result updateTaskTrack(@RequestParam("presetId") String presetId) {
        dataPresetTrackInfoService.updateTaskTrack(presetId);
        return Result.ok();
    }

    @ApiOperation("获取航迹结束时间")
    @GetMapping("/getTrackEndTime")
    public Result getTrackEndTime(@RequestParam("presetId") String presetId) {
        Integer trackEndTime = requirementInfoService.getTrackEndTime(presetId);
        return Result.ok(trackEndTime);
    }
}
