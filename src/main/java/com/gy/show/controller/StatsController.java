package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.DataAreaDTO;
import com.gy.show.entity.dto.RequirementTaskDTO;
import com.gy.show.service.StatsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/stats")
@Api(tags = "数据统计相关接口")
public class StatsController extends BaseController {

    @Autowired
    private StatsService statsService;

    @ApiOperation("资源设备类型数据统计信息")
    @GetMapping("/leaf")
    public Result typeStats(@RequestParam("areaId") String areaId) {
        Map<String, Object> result = statsService.typeStats(areaId);
        return Result.ok(result);
    }

    @ApiOperation("查询所有资源域下面资源设备类型数据统计信息")
    @GetMapping("/all/leaf")
    public Result allTypeStats(@ApiParam("1 资源 2 目标") @RequestParam("type") Integer type) {
        Map<String, Integer> result = statsService.allTypeStats(type);
        return Result.ok(result);
    }

    @ApiOperation("类型数据统计信息")
    @GetMapping("/type")
    public Result typeStatsByDataType(@RequestParam("areaId") String areaId) {
        Map<String, Integer> result = statsService.typeStatsByDataType(areaId);
        return Result.ok(result);
    }

    @ApiOperation("资源域数量统计接口")
    @GetMapping("/area")
    public Result areaStats(@RequestParam("type") Integer type) {
        Map<String, Integer> result = statsService.areaStats(type);
        return Result.ok(result);
    }

    @ApiOperation("资源域类型计接口")
    @GetMapping("/areaType")
    public Result areaDataTypeStats(@RequestParam("type") Integer type) {
        Map<String, Integer> result = statsService.areaDataTypeStats(type);
        return Result.ok(result);
    }

    @ApiOperation("资源域空闲率统计")
    @GetMapping("/areaIdle")
    public Result areaIdle() {
        Map<String, Double> result = statsService.areaIdle();
        return Result.ok(result);
    }

    @ApiOperation("需求数量统计")
    @GetMapping("/requirementStat")
    public Result requirementStat() {
        Map<String, Object> result = statsService.requirementStat();
        return Result.ok(result);
    }

    @ApiOperation("实时态势推演统计目标及业务类型")
    @GetMapping("/real-time/targetBz")
    public Result targetBz(@RequestParam("requirementId") String requirementId) {
        List<RequirementTaskDTO> result = statsService.targetBz(requirementId);
        return Result.ok(result);
    }

    @ApiOperation("实时态势推演统计资源域类型")
    @GetMapping("/real-time/areaType")
    public Result realTimeAreaType(@RequestParam("requirementId") String requirementId) {
        Object result = statsService.realTimeAreaType(requirementId);
        return Result.ok(result);
    }

}
