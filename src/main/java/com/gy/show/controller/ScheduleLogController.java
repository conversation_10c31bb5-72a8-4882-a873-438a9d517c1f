package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.service.ScheduleLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "调度日志相关接口")
@RequestMapping("/schedule/log")
@RestController
public class ScheduleLogController extends BaseController {

    @Autowired
    ScheduleLogService scheduleLogService;

    @ApiOperation("根据设备ID查询日志")
    @GetMapping("/pageByEquipmentId")
    public Result pageByEquipmentId(@RequestParam("equipmentId") String equipmentId) {
        Object result = scheduleLogService.pageByEquipmentId(getPage(), equipmentId);
        return Result.ok(result);
    }

}
