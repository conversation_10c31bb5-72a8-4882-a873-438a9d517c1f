package com.gy.show.controller;

import com.gy.show.common.Result;
import com.gy.show.entity.dto.TaskTargetRelationDTO;
import com.gy.show.service.CountService;
import com.gy.show.service.TaskTargetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 统计相关接口
 */
@RestController
@RequestMapping("/count")
@Api(tags = "统计相关接口")
public class CountController extends BaseController {

    @Autowired
    private CountService countService;

    @ApiOperation("需求执行情况个数统计")
    @GetMapping("/countRequirementExeCondition")
    public Result countRequirementExeCondition() {
        Map<String, Integer> countMap = countService.countRequirementExeCondition();
        return Result.ok(countMap);
    }

    @ApiOperation("任务执行情况个数统计")
    @GetMapping("/countTaskExeCondition")
    public Result countTaskExeCondition() {
        Map<String, Integer> countMap = countService.countTaskExeCondition();
        return Result.ok(countMap);
    }

    @ApiOperation("目标-业务类型个数统计")
    @GetMapping("/countMbBusinessType")
    public Result countMbBusinessType() {
        Map<String, Integer> countMap = countService.countMbBusinessType();
        return Result.ok(countMap);
    }

    @ApiOperation("资源域统计")
    @GetMapping("/countDataAreaSchedule")
    public Result countDataAreaSchedule() {
        Map<Object, Integer> countMap = countService.countDataAreaSchedule();
        return Result.ok(countMap);
    }

}
