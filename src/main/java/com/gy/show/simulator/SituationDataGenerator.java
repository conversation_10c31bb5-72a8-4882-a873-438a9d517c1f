package com.gy.show.simulator;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 态势数据生成器
 * 根据协议规范生成标准的态势数据包
 */
@Slf4j
public class SituationDataGenerator {

    private final Random random = new Random();
    
    /**
     * 无人机数据模型
     */
    @Data
    public static class UavData {
        private String name;
        private long targetCode;
        private double longitude;
        private double latitude;
        private double relativeAltitude;
        private double gpsAltitude;
        private double airSpeed;
        private double groundSpeed;
        private double pitch;
        private double roll;
        private double yaw;
        private byte linkStatus;
    }
    
    /**
     * 无人艇数据模型
     */
    @Data
    public static class ShipData {
        private String name;
        private long targetCode;
        private double longitude;
        private double latitude;
        private double roll;
        private double pitch;
        private double heading;
        private double speedDirection;
        private double speedMagnitude;
        private long timestamp;
        private double rudderAngle;
        private byte controlAuthority;
        private byte controlMode;
        private int navigationMode;
        private byte linkStatus;
    }
    
    /**
     * 生成无人机态势数据包
     */
    public byte[] generateUavSituationPacket(List<UavData> uavList) {
        ByteBuf buffer = Unpooled.buffer(2048);

        // 构建报文头
        buildControlMessageHeader(buffer, (byte) 0x04); // 无人机数据类型

        // 接口类型 (1字节) - 0x02表示态势数据
        buffer.writeByte(0x02);

        // 目标数量 (1字节)
        buffer.writeByte(uavList.size());

        // 遍历每个无人机目标
        for (int i = 0; i < uavList.size(); i++) {
            UavData uav = uavList.get(i);

            // 目标序号 (1字节)
            buffer.writeByte(i + 1);


            // 目标代号 (8字节)
            buffer.writeLong(uav.getTargetCode());

            // 目标名称 (20字节)
            writeFixedLengthString(buffer, uav.getName(), 20);

            // 目标类型 (2字节) - 无人机类型
            buffer.writeByte(0x02);

            // 无人机特有字段
            // 相对高度 (4字节) - UavPositionField，乘以100
            buffer.writeInt((int) (uav.getRelativeAltitude() * 100));

            // GPS高度 (4字节)
            buffer.writeInt((int) (uav.getGpsAltitude() * 100));

            // 空速 (4字节)
            buffer.writeInt((int) (uav.getAirSpeed() * 100));

            // 地速 (4字节)
            buffer.writeInt((int) (uav.getGroundSpeed() * 100));

            // 经度 (4字节) - PositionField，乘以10000000
            buffer.writeInt((int) (uav.getLongitude() * 10000000));

            // 纬度 (4字节)
            buffer.writeInt((int) (uav.getLatitude() * 10000000));

            // 俯仰 (4字节) - 角度乘以100
            buffer.writeInt((int) (uav.getPitch() * 100));

            // 滚转 (4字节)
            buffer.writeInt((int) (uav.getRoll() * 100));

            // 偏航 (4字节)
            buffer.writeInt((int) (uav.getYaw() * 100));

            // 链路连接状态 (1字节)
            buffer.writeByte(uav.getLinkStatus());
        }

        // 执行任务数量 (2字节)
        buffer.writeShort(1);

        // 任务数据 - 添加一个示例任务
        writeTaskData(buffer);

        return finalizePacket(buffer);
    }
    
    /**
     * 生成无人艇态势数据包
     */
    public byte[] generateShipSituationPacket(List<ShipData> shipList) {
        ByteBuf buffer = Unpooled.buffer(2048);

        // 构建报文头
        buildControlMessageHeader(buffer, (byte) 0x03); // 无人艇数据类型

        // 接口类型 (1字节) - 0x02表示态势数据
        buffer.writeByte(0x02);

        // 目标数量 (1字节)
        buffer.writeByte(shipList.size());

        // 遍历每个无人艇目标
        for (int i = 0; i < shipList.size(); i++) {
            ShipData ship = shipList.get(i);

            // 目标序号 (1字节)
            buffer.writeByte(i + 1);

            // 目标代号 (8字节)
            buffer.writeLong(ship.getTargetCode());

            // 目标名称 (20字节)
            writeFixedLengthString(buffer, ship.getName(), 20);

            // 目标类型 (2字节) - 无人艇类型
            buffer.writeByte(0x01);

            // 无人艇特有字段
            // 经度 (4字节) - PositionField，乘以10000000
            buffer.writeInt((int) (ship.getLongitude() * 10000000));

            // 纬度 (4字节)
            buffer.writeInt((int) (ship.getLatitude() * 10000000));

            // 横摇 (4字节) - AngleField，乘以10
            buffer.writeInt((int) (ship.getRoll() * 10));

            // 纵摇 (4字节)
            buffer.writeInt((int) (ship.getPitch() * 10));

            // 艏相角 (4字节)
            buffer.writeInt((int) (ship.getHeading() * 10));

            // 速度方向 (4字节)
            buffer.writeInt((int) (ship.getSpeedDirection() * 10));

            // 速度大小 (4字节)
            buffer.writeInt((int) (ship.getSpeedMagnitude() * 10));

            // 时间 (8字节)
            buffer.writeLong(ship.getTimestamp());

            // 舵角 (4字节) - RudderAngleField，特殊处理
            buffer.writeInt((int) (ship.getRudderAngle() * 1000 / 30));

            // 控制权 (1字节)
            buffer.writeByte(ship.getControlAuthority());

            // 控制模式 (1字节)
            buffer.writeByte(ship.getControlMode());

            // 惯导模式 (4字节)
            buffer.writeInt(ship.getNavigationMode());

            // 链路连接状态 (1字节)
            buffer.writeByte(ship.getLinkStatus());
        }

        // 执行任务数量 (2字节)
        buffer.writeShort(1);

        // 任务数据 - 添加一个示例任务
        writeTaskData(buffer);

        return finalizePacket(buffer);
    }
    
    /**
     * 构建控制消息头部 (48字节)
     * 根据HeadMessage结构
     */
    private void buildControlMessageHeader(ByteBuf buffer, byte packageType) {
        // 包序号 (2字节)
        buffer.writeShort(random.nextInt(65535) + 1);

        // 信源 (2字节)
        buffer.writeShort(0x1001);

        // 信宿 (2字节)
        buffer.writeShort(0x2001);

        // 包类型 (1字节)
        buffer.writeByte(packageType);

        // 包长度 (2字节) - 占位，后续更新
        buffer.writeShort(0);

        // 分段数 (1字节)
        buffer.writeByte(0x01);

        // 分段号 (1字节)
        buffer.writeByte(0x01);

        // 预留字段 (37字节)
        buffer.writeBytes(new byte[37]);
    }
    
    /**
     * 写入固定长度字符串
     */
    private void writeFixedLengthString(ByteBuf buffer, String str, int length) {
        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        if (bytes.length >= length) {
            buffer.writeBytes(bytes, 0, length);
        } else {
            buffer.writeBytes(bytes);
            buffer.writeBytes(new byte[length - bytes.length]); // 填充零
        }
    }
    
    /**
     * 完成数据包构建，更新长度字段
     */
    private byte[] finalizePacket(ByteBuf buffer) {
        int totalLength = buffer.writerIndex();

        // 更新包长度字段（位置7-8，不包含头部48字节）
        buffer.setShort(7, totalLength - 48);

        byte[] data = new byte[totalLength];
        buffer.readBytes(data);
        buffer.release(); // 释放ByteBuf资源
        return data;
    }

    /**
     * 写入任务数据
     */
    private void writeTaskData(ByteBuf buffer) {
        // 任务ID (4字节)
        buffer.writeShort(1001);

        // 需求ID
        buffer.writeLong(1000L);

        // 任务ID
        buffer.writeLong(112L);

        // 任务优先级 (1字节) - 1: 低, 2: 中, 3: 高
        buffer.writeBytes(new byte[20]);

        // 任务开始时间 (8字节) - 时间戳
        buffer.writeShort(1);

        buffer.writeBytes(new byte[17]);

    }

    /**
     * 写入航点数据
     */
    private void writeWaypoint(ByteBuf buffer, double longitude, double latitude, double altitude) {
        // 经度 (4字节) - 乘以10000000
        buffer.writeInt((int) (longitude * 10000000));

        // 纬度 (4字节) - 乘以10000000
        buffer.writeInt((int) (latitude * 10000000));

        // 高度 (4字节) - 乘以100
        buffer.writeInt((int) (altitude * 100));

        // 航点类型 (1字节) - 1: 普通航点, 2: 目标点, 3: 返航点
        buffer.writeByte(1);

        // 航点速度 (2字节) - 期望速度，单位: m/s
        buffer.writeShort(80);

        // 悬停时间 (2字节) - 单位: 秒
        buffer.writeShort(30);
    }
    
    /**
     * 创建模拟无人机数据
     */
    public List<UavData> createMockUavData() {
        List<UavData> uavList = new ArrayList<>();
        
        // 无人机1
        UavData uav1 = new UavData();
        uav1.setName("UAV-Alpha");
        uav1.setTargetCode(1837780265439596546L);
        uav1.setLongitude(116.3974 + random.nextGaussian() * 0.01);
        uav1.setLatitude(39.9093 + random.nextGaussian() * 0.01);
        uav1.setRelativeAltitude(1000 + random.nextGaussian() * 50);
        uav1.setGpsAltitude(1050 + random.nextGaussian() * 50);
        uav1.setAirSpeed(80 + random.nextGaussian() * 5);
        uav1.setGroundSpeed(75 + random.nextGaussian() * 5);
        uav1.setPitch(random.nextGaussian() * 5);
        uav1.setRoll(random.nextGaussian() * 3);
        uav1.setYaw(random.nextDouble() * 360);
        uav1.setLinkStatus((byte) 0x03); // 电台和测控链路都连接
        uavList.add(uav1);
        
        // 无人机2
//        UavData uav2 = new UavData();
//        uav2.setName("UAV-Beta");
//        uav2.setTargetCode(1002L);
//        uav2.setLongitude(116.4074 + random.nextGaussian() * 0.01);
//        uav2.setLatitude(39.9193 + random.nextGaussian() * 0.01);
//        uav2.setRelativeAltitude(1200 + random.nextGaussian() * 50);
//        uav2.setGpsAltitude(1250 + random.nextGaussian() * 50);
//        uav2.setAirSpeed(85 + random.nextGaussian() * 5);
//        uav2.setGroundSpeed(80 + random.nextGaussian() * 5);
//        uav2.setPitch(random.nextGaussian() * 5);
//        uav2.setRoll(random.nextGaussian() * 3);
//        uav2.setYaw(random.nextDouble() * 360);
//        uav2.setLinkStatus((byte) 0x01); // 仅电台链路连接
//        uavList.add(uav2);
        
        return uavList;
    }
    
    /**
     * 创建模拟无人艇数据
     */
    public List<ShipData> createMockShipData() {
        List<ShipData> shipList = new ArrayList<>();
        
        // 无人艇1
        ShipData ship1 = new ShipData();
        ship1.setName("SHIP-Alpha");
        ship1.setTargetCode(1837788487298846721L);
        ship1.setLongitude(121.4737 + random.nextGaussian() * 0.001);
        ship1.setLatitude(31.2304 + random.nextGaussian() * 0.001);
        ship1.setRoll(random.nextGaussian() * 2);
        ship1.setPitch(random.nextGaussian() * 3);
        ship1.setHeading(random.nextDouble() * 360);
        ship1.setSpeedDirection(random.nextDouble() * 360);
        ship1.setSpeedMagnitude(15 + random.nextGaussian() * 2);
        ship1.setTimestamp(System.currentTimeMillis());
        ship1.setRudderAngle(random.nextGaussian() * 10);
        ship1.setControlAuthority((byte) 0x01);
        ship1.setControlMode((byte) 0x02);
        ship1.setNavigationMode(1);
        ship1.setLinkStatus((byte) 0x03);
        shipList.add(ship1);
        
        // 无人艇2
//        ShipData ship2 = new ShipData();
//        ship2.setName("SHIP-Beta");
//        ship2.setTargetCode(2002L);
//        ship2.setLongitude(121.4837 + random.nextGaussian() * 0.001);
//        ship2.setLatitude(31.2404 + random.nextGaussian() * 0.001);
//        ship2.setRoll(random.nextGaussian() * 2);
//        ship2.setPitch(random.nextGaussian() * 3);
//        ship2.setHeading(random.nextDouble() * 360);
//        ship2.setSpeedDirection(random.nextDouble() * 360);
//        ship2.setSpeedMagnitude(12 + random.nextGaussian() * 2);
//        ship2.setTimestamp(System.currentTimeMillis());
//        ship2.setRudderAngle(random.nextGaussian() * 10);
//        ship2.setControlAuthority((byte) 0x01);
//        ship2.setControlMode((byte) 0x01);
//        ship2.setNavigationMode(2);
//        ship2.setLinkStatus((byte) 0x02); // 仅测控链路连接
//        shipList.add(ship2);
        
        return shipList;
    }
}
