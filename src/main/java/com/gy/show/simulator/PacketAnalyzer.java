package com.gy.show.simulator;

import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * 数据包分析工具
 * 用于验证生成的态势数据包格式
 */
@Slf4j
public class PacketAnalyzer {

    /**
     * 分析数据包
     */
    public static void analyzePacket(byte[] data, String packetType) {
        log.info("=== {} 数据包分析 ===", packetType);
        log.info("数据包总长度: {} 字节", data.length);
        
        ByteBuffer buffer = ByteBuffer.wrap(data);
        
        // 解析报文头 (48字节)
        analyzeHeader(buffer);
        
        // 解析报文体
        analyzeBody(buffer, packetType);
        
        log.info("=== 分析完成 ===");
    }
    
    /**
     * 分析报文头
     */
    private static void analyzeHeader(ByteBuffer buffer) {
        log.info("--- 报文头分析 ---");
        
        // 包序号 (2字节)
        short packetSeq = buffer.getShort();
        log.info("包序号: {}", packetSeq);
        
        // 信源 (2字节)
        short source = buffer.getShort();
        log.info("信源: 0x{}", Integer.toHexString(source & 0xFFFF));
        
        // 信宿 (2字节)
        short destination = buffer.getShort();
        log.info("信宿: 0x{}", Integer.toHexString(destination & 0xFFFF));
        
        // 包类型 (1字节)
        byte packetType = buffer.get();
        log.info("包类型: 0x{} ({})", Integer.toHexString(packetType & 0xFF), getPacketTypeName(packetType));
        
        // 包长度 (2字节)
        short packetLength = buffer.getShort();
        log.info("包长度: {} 字节", packetLength);
        
        // 分段数 (1字节)
        byte segmentCount = buffer.get();
        log.info("分段数: {}", segmentCount);
        
        // 分段号 (1字节)
        byte segmentNo = buffer.get();
        log.info("分段号: {}", segmentNo);
        
        // 预留字段 (37字节)
        byte[] reserved = new byte[37];
        buffer.get(reserved);
        log.info("预留字段: {} 字节", reserved.length);
    }
    
    /**
     * 分析报文体
     */
    private static void analyzeBody(ByteBuffer buffer, String packetType) {
        log.info("--- 报文体分析 ---");
        
        // 接口类型 (1字节)
        byte interfaceType = buffer.get();
        log.info("接口类型: 0x{} ({})", Integer.toHexString(interfaceType & 0xFF), getInterfaceTypeName(interfaceType));
        
        // 目标数量 (1字节)
        byte targetCount = buffer.get();
        log.info("目标数量: {}", targetCount & 0xFF);
        
        // 解析每个目标
        for (int i = 0; i < (targetCount & 0xFF); i++) {
            log.info("--- 目标 {} ---", i + 1);
            analyzeTarget(buffer, packetType);
        }
        
        // 执行任务数量 (2字节)
        if (buffer.remaining() >= 2) {
            short taskCount = buffer.getShort();
            log.info("执行任务数量: {}", taskCount);
        }
    }
    
    /**
     * 分析单个目标数据
     */
    private static void analyzeTarget(ByteBuffer buffer, String packetType) {
        // 目标序号 (1字节)
        byte targetSeq = buffer.get();
        log.info("目标序号: {}", targetSeq);
        
        // 目标类型 (2字节)
        short targetType = buffer.getShort();
        log.info("目标类型: 0x{} ({})", Integer.toHexString(targetType & 0xFFFF), getTargetTypeName(targetType));
        
        // 目标代号 (8字节)
        long targetCode = buffer.getLong();
        log.info("目标代号: {}", targetCode);
        
        // 目标名称 (20字节)
        byte[] nameBytes = new byte[20];
        buffer.get(nameBytes);
        String targetName = new String(nameBytes, StandardCharsets.UTF_8).trim();
        log.info("目标名称: '{}'", targetName);
        
        // 根据包类型解析特有字段
        if ("无人机".equals(packetType)) {
            analyzeUavFields(buffer);
        } else if ("无人艇".equals(packetType)) {
            analyzeShipFields(buffer);
        }
    }
    
    /**
     * 分析无人机特有字段
     */
    private static void analyzeUavFields(ByteBuffer buffer) {
        // 相对高度 (4字节)
        int relativeAltitude = buffer.getInt();
        log.info("相对高度: {:.2f} m", relativeAltitude / 100.0);
        
        // GPS高度 (4字节)
        int gpsAltitude = buffer.getInt();
        log.info("GPS高度: {:.2f} m", gpsAltitude / 100.0);
        
        // 空速 (4字节)
        int airSpeed = buffer.getInt();
        log.info("空速: {:.2f} m/s", airSpeed / 100.0);
        
        // 地速 (4字节)
        int groundSpeed = buffer.getInt();
        log.info("地速: {:.2f} m/s", groundSpeed / 100.0);
        
        // 经度 (4字节)
        int longitude = buffer.getInt();
        log.info("经度: {:.7f}°", longitude / 10000000.0);
        
        // 纬度 (4字节)
        int latitude = buffer.getInt();
        log.info("纬度: {:.7f}°", latitude / 10000000.0);
        
        // 俯仰 (4字节)
        int pitch = buffer.getInt();
        log.info("俯仰: {:.2f}°", pitch / 100.0);
        
        // 滚转 (4字节)
        int roll = buffer.getInt();
        log.info("滚转: {:.2f}°", roll / 100.0);
        
        // 偏航 (4字节)
        int yaw = buffer.getInt();
        log.info("偏航: {:.2f}°", yaw / 100.0);
        
        // 链路连接状态 (1字节)
        byte linkStatus = buffer.get();
        log.info("链路状态: 0x{} ({})", Integer.toHexString(linkStatus & 0xFF), analyzeLinkStatus(linkStatus));
    }
    
    /**
     * 分析无人艇特有字段
     */
    private static void analyzeShipFields(ByteBuffer buffer) {
        // 经度 (4字节)
        int longitude = buffer.getInt();
        log.info("经度: {:.7f}°", longitude / 10000000.0);
        
        // 纬度 (4字节)
        int latitude = buffer.getInt();
        log.info("纬度: {:.7f}°", latitude / 10000000.0);
        
        // 横摇 (4字节)
        int roll = buffer.getInt();
        log.info("横摇: {:.1f}°", roll / 10.0);
        
        // 纵摇 (4字节)
        int pitch = buffer.getInt();
        log.info("纵摇: {:.1f}°", pitch / 10.0);
        
        // 艏相角 (4字节)
        int heading = buffer.getInt();
        log.info("艏相角: {:.1f}°", heading / 10.0);
        
        // 速度方向 (4字节)
        int speedDirection = buffer.getInt();
        log.info("速度方向: {:.1f}°", speedDirection / 10.0);
        
        // 速度大小 (4字节)
        int speedMagnitude = buffer.getInt();
        log.info("速度大小: {:.1f} 节", speedMagnitude / 10.0);
        
        // 时间 (8字节)
        long timestamp = buffer.getLong();
        log.info("时间戳: {} ({})", timestamp, new java.util.Date(timestamp));
        
        // 舵角 (4字节)
        int rudderAngle = buffer.getInt();
        log.info("舵角: {:.2f}°", (rudderAngle * 30.0) / 1000.0);
        
        // 控制权 (1字节)
        byte controlAuthority = buffer.get();
        log.info("控制权: {} ({})", controlAuthority, controlAuthority == 1 ? "有控制权" : "无控制权");
        
        // 控制模式 (1字节)
        byte controlMode = buffer.get();
        log.info("控制模式: {} ({})", controlMode, getControlModeName(controlMode));
        
        // 惯导模式 (4字节)
        int navigationMode = buffer.getInt();
        log.info("惯导模式: {}", navigationMode);
        
        // 链路连接状态 (1字节)
        byte linkStatus = buffer.get();
        log.info("链路状态: 0x{} ({})", Integer.toHexString(linkStatus & 0xFF), analyzeLinkStatus(linkStatus));
    }
    
    /**
     * 获取包类型名称
     */
    private static String getPacketTypeName(byte type) {
        switch (type) {
            case 0x03: return "无人艇数据";
            case 0x04: return "无人机数据";
            default: return "未知类型";
        }
    }
    
    /**
     * 获取接口类型名称
     */
    private static String getInterfaceTypeName(byte type) {
        switch (type) {
            case 0x01: return "目标信息";
            case 0x02: return "态势数据";
            default: return "未知类型";
        }
    }
    
    /**
     * 获取目标类型名称
     */
    private static String getTargetTypeName(short type) {
        switch (type) {
            case 0x03: return "无人艇";
            case 0x04: return "无人机";
            default: return "未知类型";
        }
    }
    
    /**
     * 获取控制模式名称
     */
    private static String getControlModeName(byte mode) {
        switch (mode) {
            case 1: return "手动模式";
            case 2: return "自动模式";
            default: return "未知模式";
        }
    }
    
    /**
     * 分析链路状态
     */
    private static String analyzeLinkStatus(byte status) {
        StringBuilder sb = new StringBuilder();
        if ((status & 0x01) != 0) {
            sb.append("电台链路连接 ");
        }
        if ((status & 0x02) != 0) {
            sb.append("测控链路连接 ");
        }
        return sb.length() > 0 ? sb.toString().trim() : "无连接";
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        SituationDataGenerator generator = new SituationDataGenerator();
        
        // 测试无人机数据包
        log.info("生成无人机测试数据包...");
        byte[] uavData = generator.generateUavSituationPacket(generator.createMockUavData());
        analyzePacket(uavData, "无人机");
        
        log.info("");
        
        // 测试无人艇数据包
        log.info("生成无人艇测试数据包...");
        byte[] shipData = generator.generateShipSituationPacket(generator.createMockShipData());
        analyzePacket(shipData, "无人艇");
    }
}
