<?xml version="1.0" encoding="UTF-8"?>
<configuration  scan="true" scanPeriod="60 seconds" debug="false">
    <!--控制台输出格式-->
    <substitutionProperty name="log.pattern" value="[%X{trackId}]%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr(-){faint} %clr(%-55.80logger{79}){cyan} %clr(:){faint} %m%n%wEx"/>

    <!--控制台输出颜色-->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <contextName>logback</contextName>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--<pattern>[%d{yyyy-MM-dd HH:mm:ss} %5p %c:%L] %m%n</pattern>-->
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>
    <!-- 日志格式 -->
    <property name="FILE_LOG_PATTERN"
              value="${FILE_LOG_PATTERN:-%d{yyyy-MM-dd HH:mm:ss.SSS} [${TRACK_ID:-%X{trackId}}]${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!-- 每个日志文件的大小 -->
    <property name="DEBUG_MAX_FILE_SIZE" value="100MB"/>
    <property name="INFO_MAX_FILE_SIZE" value="100MB"/>
    <property name="ERROR_MAX_FILE_SIZE" value="100MB"/>
    <property name="TRACE_MAX_FILE_SIZE" value="100MB"/>
    <property name="WARN_MAX_FILE_SIZE" value="100MB"/>

    <!-- 最长保存日期 -->
    <property name="DEBUG_MAX_HISTORY" value="9"/>
    <property name="INFO_MAX_HISTORY" value="9"/>
    <property name="ERROR_MAX_HISTORY" value="9"/>
    <property name="TRACE_MAX_HISTORY" value="9"/>
    <property name="WARN_MAX_HISTORY" value="9"/>

    <!-- 总大小超过删除 -->
    <property name="DEBUG_TOTAL_SIZE_CAP" value="5GB"/>
    <property name="INFO_TOTAL_SIZE_CAP" value="5GB"/>
    <property name="ERROR_TOTAL_SIZE_CAP" value="5GB"/>
    <property name="TRACE_TOTAL_SIZE_CAP" value="5GB"/>
    <property name="WARN_TOTAL_SIZE_CAP" value="5GB"/>

    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>/home/<USER>/debug.log</file>
        <!-- 压缩备份 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                /home/<USER>/backup/debug.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <maxHistory>${DEBUG_MAX_HISTORY}</maxHistory>
            <maxFileSize>${DEBUG_MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${DEBUG_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>/home/<USER>/info.log</file>
        <!-- 压缩备份 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                /home/<USER>/backup/info.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <maxHistory>${INFO_MAX_HISTORY}</maxHistory>
            <maxFileSize>${INFO_MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${INFO_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>/home/<USER>/error.log</file>
        <!-- 压缩备份 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                /home/<USER>/backup/error.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <maxHistory>${ERROR_MAX_HISTORY}</maxHistory>
            <maxFileSize>${ERROR_MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${ERROR_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <appender name="TRACE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>/home/<USER>/trace.log</file>
        <!-- 压缩备份 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                /home/<USER>/backup/trace.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <maxHistory>${TRACE_MAX_HISTORY}</maxHistory>
            <maxFileSize>${TRACE_MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${TRACE_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>TRACE</level>
        </filter>
    </appender>

    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>/home/<USER>/warn.log</file>
        <!-- 压缩备份 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>
                /home/<USER>/backup/warn.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <maxHistory>${WARN_MAX_HISTORY}</maxHistory>
            <maxFileSize>${WARN_MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>${WARN_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!--输出到文件-->
    <!--<appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
    <!--        &lt;!&ndash;windows&ndash;&gt;-->
    <!--        <fileNamePattern>D:\\log\\logback-mnxl-%d{yyyy-MM-dd}.log</fileNamePattern>-->
    <!--        &lt;!&ndash;linux&ndash;&gt;-->
    <!--        <fileNamePattern>/home/<USER>/logback-mnxl-%d{yyyy-MM-dd}.log.gz</fileNamePattern>-->
    <!--        <maxHistory>90</maxHistory>-->
    <!--    </rollingPolicy>-->
    <!--    <encoder charset="UTF-8">-->
    <!--        <pattern>[%d{yyyy-MM-dd HH:mm:ss} %5p %c:%L] %m%n</pattern>-->
    <!--    </encoder>-->
    <!--</appender>-->
    <!--日志级别-->
    <logger name="com.gyxt.show" level="debug"/>
    <root level="info">
        <appender-ref ref="DEBUG_FILE" />
        <appender-ref ref="INFO_FILE" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="TRACE_FILE" />
        <appender-ref ref="WARN_FILE" />
        <!--<appender-ref ref="file" />-->
        <appender-ref ref="console" />
    </root>
</configuration>