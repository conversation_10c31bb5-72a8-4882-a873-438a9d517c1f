<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gy.show.mapper.CommonMapper">

    <select id="getCreateTableSql" resultType="map">
        show create table ${tableName}
    </select>
    
    <update id="exec">
        ${sql}
    </update>

    <select id="getCols" resultType="map">
        SELECT COLUMN_NAME, COLUMN_COMMENT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE,
        COLUMN_TYPE FROM information_schema.columns WHERE TABLE_NAME = #{tableName}
        AND TABLE_SCHEMA = 'wrpt' ORDER BY information_schema.columns.ORDINAL_POSITION
    </select>

    <insert id="save">
        INSERT INTO ${tableName}
        <foreach collection="data.keys" close=")" index="i" item="fieldName" open="(" separator=",">
            ${fieldName}
        </foreach>
        VALUES
        <foreach collection="data.values" separator="," open="(" item="field" index="i" close=")">
            #{field}
        </foreach>
    </insert>

    <update id="update">
        UPDATE ${tableName} SET
        <foreach collection="data.keys" index="i" item="fieldName" separator=",">
            ${fieldName} = #{data[${fieldName}]}
        </foreach>
        WHERE ID = #{primaryKey}
    </update>

    <delete id="delete">
        DELETE FROM ${tableName} where ID in
        <foreach collection="ids" separator="," close=")" open="(" item="id" index="i">
            #{id}
        </foreach>
    </delete>

    <select id="getList" resultType="map">
        SELECT * FROM ${tableName}
        <if test="keyword != null and keyword != ''">
            WHERE
            <foreach collection="cols" index="i" item="col" separator="OR">
                ${col} LIKE "%${keyword}%"
            </foreach>
        </if>
    </select>

    <select id="getData" resultType="map">
        SELECT * FROM ${tableName}
        WHERE 1 = 1
        <if test="keyword != null and keyword != ''">
            AND
            <foreach collection="cols" index="i" item="col" separator="OR">
                ${col} LIKE "%${keyword}%"
            </foreach>
        </if>
        <if test="args != null and args.size != 0">
        AND
            <foreach collection="args.keys" item="fieldName" separator="AND" index="i">
                ${fieldName} = #{args[${fieldName}]}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="getListByCol" resultType="map">
        SELECT * FROM ${tableName} WHERE ${colName} = #{colValue}
    </select>

    <select id="getListByColumn" resultType="map">
        SELECT * FROM ${tableName} WHERE ${colName} in
        <foreach collection="colValues" separator="," item="colValue" index="i" open="(" close=")">
            #{colValue}
        </foreach>
    </select>

    <select id="getOne" resultType="map">
        SELECT * FROM ${tableName} WHERE ID = #{dataId}
    </select>

    <select id="getCount" resultType="Integer">
        SELECT COUNT(1) FROM ${tableName} WHERE 1=1
        <if test="args != null and args.size != 0">
            AND
            <foreach collection="args.keys" item="fieldName" separator="AND" index="i">
                ${fieldName} = #{args[${fieldName}]}
            </foreach>
        </if>
    </select>

    <select id="getCountByField" resultType="Integer">
        SELECT COUNT(1) FROM ${tableName}
        <if test="args != null and args.size != 0">
            WHERE
            <foreach collection="args.keys" item="fieldName" separator="AND" index="i">
                ${fieldName} = #{args[${fieldName}]}
            </foreach>
        </if>
        <if test="sql != null and sql != ''">
            <if test="args == null or args.size == 0">
                WHERE
            </if>
            <if test="args != null and args.size != 0">
                AND
            </if>
            ${sql}
        </if>
    </select>

    <select id="getDataByFieldLike" resultType="map">
        SELECT * FROM ${tableName}
        WHERE 1 = 1
        <if test="fieldName != null and fieldName != '' and fieldValue != null and fieldValue != ''">
            AND ${fieldName} LIKE CONCAT('%', #{fieldValue}, '%')
        </if>
        <if test="args != null and args.size != 0">
            AND
            <foreach collection="args.keys" item="argFieldName" separator="AND" index="i">
                ${argFieldName} = #{args[${argFieldName}]}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>
